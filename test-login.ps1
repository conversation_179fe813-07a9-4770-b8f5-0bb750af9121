$body = @{
    email = "<EMAIL>"
    password = "password"
} | ConvertTo-<PERSON>son

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/api/v2/auth/login" -Method POST -ContentType "application/json" -Body $body -UseBasicParsing
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Content: $($response.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
    }
}