<?php

namespace App\Http\Middleware;

use App\Models\Anyone;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip check for login and logout routes
        if (in_array($request->route()->getName(), ['api.auth.login', 'api.auth.logout'])) {
            return $next($request);
        }

        $user = $request->user();

        // Only check if user is authenticated
        if ($user) {
            // Check if subscription is actually expired
            if ($user->isSubscriptionExpired()) {
                // Double check: only update if subscription_status is not already 'expired'
                // This prevents unnecessary updates for users who are already marked as expired
                if ($user->subscription_status !== 'expired') {
                    Anyone::where('id', $user->id)->update([
                        'subscription_status' => 'expired',
                        'subscription_plan' => null,
                        'role' => 'none',
                        'is_active' => false,
                    ]);

                    // Refresh user model to get updated data
                    $user->refresh();
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.',
                    'subscription_expired' => true,
                    'redirect_url' => route('payment.index'),
                ], 403);
            }
        }

        return $next($request);
    }
}
