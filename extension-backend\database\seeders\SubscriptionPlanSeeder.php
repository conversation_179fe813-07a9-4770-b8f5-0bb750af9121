<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Basic Plan',
                'price' => 30000,
                'duration_days' => 14,
                'description' => 'Paket dasar untuk 2 pekan akses penuh',
                'features' => [
                    'Akses semua fitur dasar',
                    'Support via WhatsApp',
                    'Update otomatis',
                    'Akses 2 pekan penuh'
                ],
                'is_active' => true,
                'whatsapp_number' => '6281234567890',
                'whatsapp_message_template' => 'Halo Admin Satu Pintu, saya ingin membeli paket {plan} dengan harga Rp {price} untuk {duration}. Mohon informasi pembayarannya.'
            ],
            [
                'name' => 'Premium Plan',
                'price' => 50000,
                'duration_days' => 30,
                'description' => 'Paket premium untuk 1 bulan akses penuh',
                'features' => [
                    'Akses semua fitur premium',
                    'Support prioritas via WhatsApp',
                    'Update otomatis',
                    'Akses 1 bulan penuh',
                    'Fitur tambahan eksklusif'
                ],
                'is_active' => true,
                'whatsapp_number' => '6281234567890',
                'whatsapp_message_template' => 'Halo Admin Satu Pintu, saya ingin membeli paket {plan} dengan harga Rp {price} untuk {duration}. Mohon informasi pembayarannya.'
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::updateOrCreate(
                ['name' => $plan['name']],
                $plan
            );
        }
    }
}