<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Extension;
use Illuminate\Http\Request;

class ExtensionController extends Controller
{
    public function checkVersion(Request $request, $version = null)
    {
        // Get version from URL parameter or request body
        $currentVersion = $version ?? $request->input('version');

        if (!$currentVersion) {
            return response()->json([
                'status' => 'error',
                'message' => 'Version parameter is required',
                'needs_update' => false
            ], 400);
        }

        $latestExtension = Extension::orderBy('version', 'desc')->first();

        if (!$latestExtension) {
            return response()->json([
                'status' => 'error',
                'message' => 'No extension versions found.',
                'needs_update' => false
            ], 404);
        }

        if (version_compare($currentVersion, $latestExtension->version, '<')) {
            return response()->json([
                'status' => 'success',
                'message' => 'New extension version available.',
                'needs_update' => true,
                'latest_version' => $latestExtension->version,
                'download_path' => $latestExtension->file_path,
                'guide_path' => $latestExtension->file_guide
            ]);
        } else {
            return response()->json([
                'status' => 'success',
                'message' => 'You are using the latest extension version.',
                'needs_update' => false
            ]);
        }
    }
}
