<?php

namespace App\Filament\Resources\ApiV2\ImportantInfoResource\Pages;

use App\Filament\Resources\ApiV2\ImportantInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\IconEntry;
use Filament\Support\Enums\FontWeight;

class ViewImportantInfo extends ViewRecord
{
    protected static string $resource = ImportantInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
            DeleteAction::make()
                ->requiresConfirmation()
                ->modalHeading('Hapus Informasi')
                ->modalDescription('Apakah Anda yakin ingin menghapus informasi ini? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Hapus'),
        ];
    }
    
    public function getTitle(): string
    {
        return 'Detail Informasi Penting';
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Dasar')
                    ->schema([
                        TextEntry::make('title')
                            ->label('Judul')
                            ->weight(FontWeight::Bold)
                            ->size('lg'),
                        
                        TextEntry::make('content')
                            ->label('Konten')
                            ->markdown()
                            ->columnSpanFull(),
                        
                        TextEntry::make('type')
                            ->label('Tipe')
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'announcement' => 'Pengumuman',
                                'warning' => 'Peringatan',
                                'info' => 'Informasi',
                                'success' => 'Sukses',
                                default => $state,
                            })
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'announcement' => 'primary',
                                'warning' => 'warning',
                                'info' => 'info',
                                'success' => 'success',
                                default => 'secondary',
                            }),
                        
                        TextEntry::make('priority')
                            ->label('Prioritas')
                            ->badge()
                            ->color('gray'),
                    ])
                    ->columns(2),
                
                Section::make('Pengaturan Tampilan')
                    ->schema([
                        IconEntry::make('is_active')
                            ->label('Status')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),
                        
                        TextEntry::make('start_date')
                            ->label('Tanggal Mulai')
                            ->dateTime('d F Y, H:i')
                            ->placeholder('Langsung aktif'),
                        
                        TextEntry::make('end_date')
                            ->label('Tanggal Berakhir')
                            ->dateTime('d F Y, H:i')
                            ->placeholder('Tidak terbatas'),
                    ])
                    ->columns(3),
                
                Section::make('Informasi Sistem')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Dibuat pada')
                            ->dateTime('d F Y, H:i'),
                        
                        TextEntry::make('updated_at')
                            ->label('Diperbarui pada')
                            ->dateTime('d F Y, H:i'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}