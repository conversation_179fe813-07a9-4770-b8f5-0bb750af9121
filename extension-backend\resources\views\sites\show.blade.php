@extends('layout.homeLayout')

@section('title', 'Detail Situs - ' . $site->name)

@push('styles')
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section -->
    <div class="gradient-bg relative overflow-hidden py-16">
        <div class="absolute inset-0">
            <div
                class="absolute top-0 left-0 w-72 h-72 bg-white opacity-10 rounded-full -translate-x-1/2 -translate-y-1/2 floating">
            </div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full translate-x-1/2 translate-y-1/2 floating"
                style="animation-delay: -1s;"></div>
        </div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-30">
            <div class="flex flex-col lg:flex-row items-center justify-between">
                <div class="flex items-center text-white mb-8 lg:mb-0">
                    <div class="relative">
                        <img src="{{ $site->logo_path ? Storage::url($site->logo_path) : '/images/default-site-logo.png' }}"
                            alt="{{ $site->name }}"
                            class="w-24 h-24 rounded-3xl object-cover shadow-2xl border-4 border-white border-opacity-20">
                        <div
                            class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-6">
                        <h1 class="text-4xl text-white lg:text-5xl font-bold mb-2">{{ $site->name }}</h1>
                        <p class="text-sm opacity-75">{{ $site->description ?? 'Deskripsi tidak tersedia' }}</p>
                    </div>
                </div>
                <div class="glass-effect rounded-2xl p-6 border border-white border-opacity-20">
                    <div class="flex flex-col items-center space-y-4">
                        @switch($site->visibility)
                            @case('elite')
                                <span
                                    class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                                    <i class="fas fa-crown mr-2"></i>
                                    Elite Only
                                </span>
                            @break

                            @case('premium')
                                <span
                                    class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                                    <i class="fas fa-star mr-2"></i>
                                    Premium Only
                                </span>
                            @break

                            @case('both')
                                <span
                                    class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 text-white">
                                    <i class="fas fa-users mr-2"></i>
                                    All Access
                                </span>
                            @break
                        @endswitch
                        <a href="{{ $site->url }}" target="_blank" rel="noopener noreferrer"
                            class="bg-white text-blue-500 bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-xl transition-all duration-300 text-sm font-semibold border border-white border-opacity-20">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            Kunjungi Situs
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Main Content -->
    <div class="mt-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            <!-- Main Content -->
            <div class="w-full mt-20">
                <!-- Alternative Accounts Section -->
                <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 card-hover">
                    <div class="flex items-center justify-between mb-8">
                        <div class="flex items-center">
                            <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl mr-4">
                                <i class="fas fa-users text-white text-xl"></i>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">Akun Alternatif</h2>
                                <p class="text-gray-500">Pilih akun yang tersedia untuk digunakan</p>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-4 py-2 rounded-xl">
                            <span class="text-blue-600 font-semibold">{{ count($site->alternative_accounts ?? []) }}
                                akun tersedia</span>
                        </div>
                    </div>

                    @if (!empty($site->alternative_accounts))
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach ($site->alternative_accounts as $index => $account)
                                <div
                                    class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 hover:border-blue-300 transition-all duration-300 card-hover">
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="flex items-center">
                                            <div
                                                class="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl mr-4">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-bold text-gray-900 text-lg">
                                                    {{ $account['account_name'] ?? 'Akun ' . ($index + 1) }}</h3>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Status Badges -->
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        @if (!empty($account['is_primary']))
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-green-500 to-green-600 text-white">
                                                <i class="fas fa-crown mr-1"></i>
                                                Utama
                                            </span>
                                        @endif
                                        @if (!empty($account['is_active']))
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                Aktif
                                            </span>
                                        @else
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-gray-400 to-gray-500 text-white">
                                                <i class="fas fa-times-circle mr-1"></i>
                                                Nonaktif
                                            </span>
                                        @endif
                                    </div>

                                    <!-- Account Credentials -->
                                    <div class="space-y-4">
                                        <!-- Username/Email Field -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                <i class="fas fa-user mr-1"></i>
                                                Username/Email
                                            </label>
                                            <div class="w-full px-4 py-3 bg-gray-100 border border-gray-300 rounded-lg text-gray-700 font-mono text-sm select-all">
                                                {{ $account['username'] ?? 'Username tidak tersedia' }}
                                            </div>
                                        </div>

                                        <!-- Password Field -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                <i class="fas fa-lock mr-1"></i>
                                                Password
                                            </label>
                                            <div class="w-full px-4 py-3 bg-gray-100 border border-gray-300 rounded-lg text-gray-700 font-mono text-sm select-all">
                                                {{ $account['password'] ?? 'Password tidak tersedia' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada akun alternatif</h3>
                            <p class="mt-1 text-sm text-gray-500">Situs ini belum memiliki akun alternatif yang
                                tersedia.</p>
                        </div>
                    @endif
                </div>

            </div>
        </div>
    </div>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // All interactive Javascript has been removed as requested by the user.
    </script>
@endsection
