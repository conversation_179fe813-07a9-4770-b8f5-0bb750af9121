<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cookies</title>
</head>
<body>
    <h1>Test Cookie Management</h1>
    <div id="cookies-info">
        <h2>Current Cookies:</h2>
        <div id="cookie-list"></div>
    </div>
    
    <button onclick="showCookies()">Refresh Cookies</button>
    <button onclick="clearAllCookies()">Clear All Cookies</button>
    
    <script>
        function showCookies() {
            const cookieList = document.getElementById('cookie-list');
            const cookies = document.cookie.split(';');
            
            if (cookies.length === 1 && cookies[0] === '') {
                cookieList.innerHTML = '<p>No cookies found</p>';
            } else {
                cookieList.innerHTML = cookies.map(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    return `<p><strong>${name}:</strong> ${value}</p>`;
                }).join('');
            }
        }
        
        function clearAllCookies() {
            const cookies = document.cookie.split(';');
            cookies.forEach(cookie => {
                const eqPos = cookie.indexOf('=');
                const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
            });
            showCookies();
        }
        
        // Show cookies on page load
        window.onload = showCookies;
        
        // Auto refresh every 2 seconds
        setInterval(showCookies, 2000);
    </script>
</body>
</html>