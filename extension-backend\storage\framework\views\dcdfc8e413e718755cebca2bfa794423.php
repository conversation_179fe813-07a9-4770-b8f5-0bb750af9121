<?php $__env->startSection('title', 'Riwayat Pembelian'); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .text-red-600 {
            color: #DC2626;
        }

        .text-yellow-600 {
            color: #F59E0B;
        }

        .text-green-600 {
            color: #10B981;
            /* Hijau untuk status paid */
        }

        .text-blue-600 {
            color: #3B82F6;
            /* Biru untuk info */
        }

        /* Tambahkan style untuk daftar riwayat */
        .history-transactions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .history-transaction-item {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid #4B5563;
            border-radius: 0.5rem;
            padding: 1.25rem;
            /* Sedikit lebih besar dari pending */
            margin-bottom: 1rem;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;
            /* <PERSON>jajarkan ke atas */
            gap: 1rem;
            /* Jarak antar kolom */
        }

        .history-transaction-main {
            flex-grow: 1;
            min-width: 0;
            /* Izinkan truncate */
        }

        .history-transaction-title {
            color: white;
            font-weight: 600;
            /* Sedikit lebih tebal */
            margin-bottom: 0.5rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .history-transaction-info {
            color: #9CA3AF;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .history-transaction-details {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            flex-shrink: 0;
            /* Jangan menyusut */
            min-width: 120px;
            /* Lebar minimum untuk kolom kanan */
        }

        .history-transaction-amount {
            color: white;
            font-weight: 700;
            /* Tebal untuk jumlah */
            font-size: 1.125rem;
            /* Sedikit lebih besar */
            margin-bottom: 0.25rem;
        }

        .history-transaction-date {
            color: #9CA3AF;
            font-size: 0.75rem;
            /* Lebih kecil */
        }

        .history-transaction-status {
            margin-top: 0.5rem;
            display: inline-block;
            padding: 0.2em 0.6em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.375rem;
            background-color: #10B981;
            /* Hijau untuk paid */
            color: #fff;
        }

        /* Pagination Styles (Opsional, bisa disesuaikan dengan Tailwind jika digunakan) */
        .pagination {
            display: flex;
            justify-content: center;
            padding: 1rem 0;
            list-style: none;
            margin-top: 1rem;
        }

        .pagination li {
            margin: 0 0.25rem;
        }

        .pagination a,
        .pagination span {
            display: block;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            text-decoration: none;
            color: #9CA3AF;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid #4B5563;
            transition: all 0.2s;
        }

        .pagination a:hover {
            background-color: #4B5563;
            color: white;
        }

        .pagination .active span {
            background-color: #DB2777;
            /* Warna aksen */
            color: white;
            border-color: #DB2777;
        }

        .pagination .disabled span {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Tombol Kembali */
        .back-button {
            background-color: #4B5563;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            text-decoration: none;
            /* Hilangkan garis bawah untuk link */
            border: none;
            /* Hilangkan border untuk button */
            font-size: 1rem;
            /* Sesuaikan ukuran font */
        }

        .back-button:hover {
            background-color: #1F2937;
        }

        .back-button i {
            font-size: 1.25rem;
            /* Ukuran ikon */
        }

        /* Pesan jika tidak ada riwayat */
        .no-history-message {
            text-align: center;
            padding: 2rem;
            color: #9CA3AF;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 0.5rem;
            border: 1px dashed #4B5563;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section class="bg-darkPink-900 relative py-28 md:py-28" style="min-height: 100vh;">
        <div class="absolute top-0 left-0 w-full h-full">
            <img class="w-full h-full object-cover" src="<?php echo e(asset('assets/img/lines-dashboard.svg')); ?>" alt="" />
        </div>
        <div class="container mx-auto px-4">
            <div class="mx-auto relative z-10">
                <!-- Header -->
                <div class="p-4 md:p-6 rounded-lg mb-6">
                    <div class="flex flex-col md:flex-row justify-between items-center gap-4 md:gap-0">
                        <h2 class="text-white text-xl md:text-2xl lg:text-3xl font-bold text-center md:text-left">
                            Riwayat Pembelian
                        </h2>
                        <!-- Tombol Kembali -->
                        <a href="<?php echo e(route('user.dashboard')); ?>" class="back-button">
                            <span>Kembali</span>
                        </a>
                    </div>
                </div>

                <!-- Konten Riwayat -->
                <div
                    style="display: flex; flex-direction: column; gap: 1.5rem; max-width: 48rem; margin-left: auto; margin-right: auto;">
                    <?php if($paidTransactions->isEmpty()): ?>
                        <div class="no-history-message">
                            <p>Belum ada riwayat pembelian.</p>
                        </div>
                    <?php else: ?>
                        <div
                            style="background-color: rgba(0, 0, 0, 0.3); border-radius: 0.5rem; padding: 1.5rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                            <ul class="history-transactions-list">
                                <?php $__currentLoopData = $paidTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="history-transaction-item">
                                        <div class="history-transaction-main">
                                            <div class="history-transaction-title">
                                                <?php echo e($transaction->subscriptionPlan->name ?? 'Plan Tidak Diketahui'); ?>

                                            </div>
                                            <div class="history-transaction-info">
                                                ID Transaksi: <?php echo e($transaction->reference_id); ?>

                                            </div>
                                            <div class="history-transaction-info">
                                                Metode: <?php echo e(ucfirst(str_replace('_', ' ', $transaction->payment_method))); ?>

                                            </div>
                                            <?php if($transaction->paid_at): ?>
                                                <div class="history-transaction-info">
                                                    Dibayar: <?php echo e($transaction->paid_at->format('d M Y H:i')); ?>

                                                </div>
                                            <?php endif; ?>
                                            <span class="history-transaction-status">
                                                <?php echo e(ucfirst($transaction->status)); ?>

                                            </span>
                                        </div>
                                        <div class="history-transaction-details">
                                            <div class="history-transaction-amount">
                                                Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?>

                                            </div>
                                            <div class="history-transaction-date">
                                                Dibuat: <?php echo e($transaction->created_at->format('d M Y')); ?>

                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>

                            <!-- Pagination Links -->
                            <?php if($paidTransactions->hasPages()): ?>
                                <div class="pagination-wrapper">
                                    <?php echo e($paidTransactions->links()); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.appLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/dashboard/payment/history.blade.php ENDPATH**/ ?>