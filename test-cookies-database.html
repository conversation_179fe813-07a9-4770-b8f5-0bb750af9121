<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cookies Database</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍪 Test Cookies Database</h1>
        <p>Tool untuk menguji pengambilan cookies dari database backend.</p>
        
        <div class="test-section">
            <h3>1. Test Login</h3>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="password">
            <button onclick="testLogin()">Login</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Test Get Sites</h3>
            <button onclick="testGetSites()">Get Sites</button>
            <div id="sitesResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Test Get Cookies by Domain</h3>
            <input type="text" id="domain" placeholder="Domain" value="facebook.com">
            <button onclick="testGetCookies()">Get Cookies</button>
            <div id="cookiesResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Test All Domains</h3>
            <button onclick="testAllDomains()">Test All Domains</button>
            <div id="allDomainsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        let authToken = null;
        
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    authToken = data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Login berhasil!\nToken: ${data.token.substring(0, 20)}...\nUser: ${data.user.name} (${data.user.role})`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Login gagal: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        async function testGetSites() {
            const resultDiv = document.getElementById('sitesResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Silakan login terlebih dahulu';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/sites`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Sites ditemukan: ${data.sites.length}\n\n${JSON.stringify(data.sites, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Gagal mengambil sites: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        async function testGetCookies() {
            const domain = document.getElementById('domain').value;
            const resultDiv = document.getElementById('cookiesResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Silakan login terlebih dahulu';
                return;
            }
            
            if (!domain) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Silakan masukkan domain';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/sites/cookies`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ domain })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const cookies = data.data.cookies || [];
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Cookies ditemukan untuk ${domain}: ${cookies.length}\n\n${JSON.stringify(cookies, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Gagal mengambil cookies: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        async function testAllDomains() {
            const resultDiv = document.getElementById('allDomainsResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Silakan login terlebih dahulu';
                return;
            }
            
            const domains = ['facebook.com', 'twitter.com', 'netflix.com', 'primevideo.com', 'youtube.com'];
            let results = [];
            
            resultDiv.className = 'result';
            resultDiv.textContent = 'Testing all domains...';
            
            for (const domain of domains) {
                try {
                    const response = await fetch(`${API_BASE_URL}/sites/cookies`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ domain })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        const cookies = data.data.cookies || [];
                        results.push(`✅ ${domain}: ${cookies.length} cookies`);
                        if (cookies.length > 0) {
                            results.push(`   Cookies: ${cookies.map(c => c.name).join(', ')}`);
                        }
                    } else {
                        results.push(`❌ ${domain}: ${data.message}`);
                    }
                } catch (error) {
                    results.push(`❌ ${domain}: Error - ${error.message}`);
                }
            }
            
            resultDiv.className = 'result success';
            resultDiv.textContent = results.join('\n');
        }
    </script>
</body>
</html>