<?php

namespace App\Filament\Resources\ApiV1;

use App\Filament\Resources\ApiV1\AnyoneResource\Pages;
use App\Filament\Resources\ApiV1\AnyoneResource\RelationManagers;
use App\Models\Anyone;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AnyoneResource extends Resource
{
    protected static ?string $model = Anyone::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Pengguna Elite/Premium';

    protected static ?string $modelLabel = 'Pengguna';

    protected static ?string $pluralModelLabel = 'Pengguna Elite/Premium';

    // protected static ?string $navigationGroup = 'API V1';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),

                Forms\Components\TextInput::make('password')
                    ->label('Password')
                    ->password()
                    ->required(fn(string $context): bool => $context === 'create')
                    ->dehydrated(fn($state) => filled($state))
                    ->minLength(8),

                Forms\Components\Select::make('role')
                    ->label('Role')
                    ->options([
                        'elite' => 'Elite',
                        'premium' => 'Premium',
                    ])
                    ->required()
                    ->default('elite'),

                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->default(true),

                Forms\Components\Select::make('subscription_plan')
                    ->label('Paket Langganan')
                    ->options(function () {
                        return \App\Models\SubscriptionPlan::where('is_active', true)
                            ->pluck('name', 'name')
                            ->toArray();
                    })
                    ->nullable()
                    ->placeholder('Pilih paket langganan'),

                Forms\Components\Select::make('subscription_status')
                    ->label('Status Langganan')
                    ->options([
                        'active' => 'Aktif',
                        'pending' => 'Menunggu',
                        'expired' => 'Berakhir',
                        'cancelled' => 'Dibatalkan',
                        'suspended' => 'Ditangguhkan',
                    ])
                    ->default('pending')
                    ->required(),

                Forms\Components\DateTimePicker::make('subscription_expires_at')
                    ->label('Masa Berakhir Langganan')
                    ->nullable()
                    ->helperText('Kosongkan untuk langganan unlimited'),

                Forms\Components\DateTimePicker::make('subscription_activated_at')
                    ->label('Tanggal Aktivasi Langganan')
                    ->nullable(),

                Forms\Components\TextInput::make('pending_order_id')
                    ->label('ID Order Pending')
                    ->nullable()
                    ->maxLength(255),

                Forms\Components\Textarea::make('pending_payment_data')
                    ->label('Data Pembayaran Pending')
                    ->nullable()
                    ->helperText('Data JSON untuk pembayaran yang sedang diproses'),

                Forms\Components\Select::make('payment_status')
                    ->label('Status Pembayaran')
                    ->options([
                        'pending' => 'Menunggu',
                        'paid' => 'Lunas',
                        'failed' => 'Gagal',
                        'cancelled' => 'Dibatalkan',
                        'refunded' => 'Dikembalikan',
                    ])
                    ->nullable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('role')
                    ->label('Role')
                    ->colors([
                        'primary' => 'elite',
                        'success' => 'premium',
                    ])
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'elite' => 'Elite',
                        'premium' => 'Premium',
                        default => $state,
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('subscription_plan')
                    ->label('Paket')
                    ->badge()
                    ->getStateUsing(function ($record) {
                        $plan = \App\Models\SubscriptionPlan::find($record->subscription_plan);
                        return $plan ? $plan->name : 'Tidak ada';
                    })
                    ->color(fn($state): string => match ($state) {
                        'Basic Plan' => 'warning',
                        'Premium Plan' => 'success',
                        default => 'gray',
                    })
                    ->placeholder('Tidak ada'),

                Tables\Columns\TextColumn::make('subscription_status')
                    ->label('Status Langganan')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'pending' => 'warning',
                        'expired' => 'danger',
                        'cancelled' => 'gray',
                        'suspended' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('subscription_expires_at')
                    ->label('Masa Berakhir')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->placeholder('Unlimited'),

                Tables\Columns\TextColumn::make('subscription_activated_at')
                    ->label('Aktivasi')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('payment_status')
                    ->label('Status Bayar')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'cancelled' => 'gray',
                        'refunded' => 'info',
                        default => 'gray',
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('payment_amount')
                    ->label('Jumlah Bayar')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Tanggal Bayar')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('email_verified_at')
                    ->label('Email Terverifikasi')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label('Role')
                    ->options([
                        'elite' => 'Elite',
                        'premium' => 'Premium',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),

                Tables\Filters\Filter::make('email_verified')
                    ->label('Email Terverifikasi')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('email_verified_at')),

                Tables\Filters\SelectFilter::make('subscription_plan')
                    ->label('Paket Langganan')
                    ->options(function () {
                        return \App\Models\SubscriptionPlan::where('is_active', true)
                            ->pluck('name', 'id')
                            ->toArray();
                    }),

                Tables\Filters\SelectFilter::make('subscription_status')
                    ->label('Status Langganan')
                    ->options([
                        'active' => 'Aktif',
                        'pending' => 'Menunggu',
                        'expired' => 'Berakhir',
                        'cancelled' => 'Dibatalkan',
                        'suspended' => 'Ditangguhkan',
                    ]),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->label('Status Pembayaran')
                    ->options([
                        'pending' => 'Menunggu',
                        'paid' => 'Lunas',
                        'failed' => 'Gagal',
                        'cancelled' => 'Dibatalkan',
                        'refunded' => 'Dikembalikan',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAnyones::route('/'),
            'create' => Pages\CreateAnyone::route('/create'),
            'edit' => Pages\EditAnyone::route('/{record}/edit'),
        ];
    }
}
