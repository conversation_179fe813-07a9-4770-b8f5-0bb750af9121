<?php

namespace App\Http\Controllers;

use App\Models\Anyone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    public function index()
    {
        /** @var \App\Models\Anyone $user */
        $user = Auth::guard('anyone')->user();

        // Ambil semua plan yang aktif
        $plans = \App\Models\SubscriptionPlan::where('is_active', true)->get();

        // Hitung hari tersisa untuk perpanjangan
        $daysLeft = $user->getDaysUntilExpiry();
        $showWarning = $daysLeft !== null && $daysLeft <= 6;

        return view(
            'payment.index',
            [
                'user' => $user,
                'plans' => $plans,
                'daysLeft' => $daysLeft,
                'showWarning' => $showWarning,
            ]
        );
    }
}
