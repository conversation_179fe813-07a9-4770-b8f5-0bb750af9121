# 🍪 Universal Cookie Manager Extension

Browser extension yang memungkinkan Anda mengupload cookies dari file JSON ke browser dengan akses penuh, termasuk cookies dengan atribut `httpOnly` dan `secure`.

## ✨ Fitur Utama

- ✅ **Upload cookies dengan akses penuh** - Mendukung semua jenis cookie termasuk `httpOnly`
- ✅ **Multiple format support** - Array JSON standar, Cookie Editor export, single cookie object
- ✅ **Cross-domain support** - Dapat mengatur cookies untuk domain apapun
- ✅ **Batch upload** - Upload semua cookies sekaligus atau per domain
- ✅ **Progress tracking** - Monitor progress upload dengan progress bar
- ✅ **Data persistence** - Data tersimpan dan dapat dipulihkan
- ✅ **Auto site opening** - Buka situs target dengan cookies yang sudah diset

## 📋 Persyaratan

- Google Chrome atau browser berbasis Chromium
- File cookies dalam format JSON

## 🚀 Cara Instalasi

### 1. Persiapan Extension

1. **Download atau clone** folder extension ini
2. **Pastikan semua file** berada dalam satu folder:
   ```
   extension-browser/
   ├── manifest.json
   ├── popup.html
   ├── popup.js
   ├── background.js
   ├── content.js
   └── README.md
   ```

### 2. Install ke Chrome

1. **Buka Chrome** dan ketik `chrome://extensions/` di address bar
2. **Aktifkan Developer mode** (toggle di kanan atas)
3. **Klik "Load unpacked"** (Muat yang tidak dikemas)
4. **Pilih folder** `extension-browser` yang berisi semua file extension
5. **Extension akan muncul** di daftar dan icon akan tampil di toolbar

### 3. Verifikasi Instalasi

- ✅ Icon extension muncul di toolbar Chrome
- ✅ Klik icon untuk membuka popup
- ✅ Popup menampilkan interface "Cookie Manager"

## 📖 Cara Penggunaan

### 1. Persiapan File Cookies

Pastikan file JSON cookies Anda dalam format yang didukung:

**Format Array JSON (Recommended):**
```json
[
  {
    "domain": ".example.com",
    "name": "session_token",
    "value": "abc123",
    "path": "/",
    "secure": true,
    "httpOnly": true,
    "sameSite": "lax",
    "expirationDate": 1234567890
  }
]
```

### 2. Upload Cookies

1. **Klik icon extension** di toolbar
2. **Pilih file JSON** dengan cara:
   - Klik area "Pilih atau Drop File JSON"
   - Atau drag & drop file ke area tersebut
3. **File akan diproses** dan menampilkan jumlah cookies
4. **Klik "Upload Cookies"** untuk mengupload semua cookies
5. **Monitor progress** melalui progress bar

### 3. Buka Situs dengan Cookies

1. **Setelah upload berhasil**, akan muncul tombol situs
2. **Klik tombol situs** (contoh: "🚀 Buka chatgpt.com")
3. **Extension akan**:
   - Upload cookies khusus untuk domain tersebut
   - Membuka situs di tab baru
   - Cookies sudah aktif di situs target

### 4. Reset Data

- **Klik "Reset Data"** untuk menghapus semua data tersimpan
- **Upload file baru** akan otomatis mengganti data lama

## 🔧 Format Cookies yang Didukung

### 1. Array JSON Standar
```json
[
  {
    "domain": ".chatgpt.com",
    "name": "session_token",
    "value": "eyJhbGciOiJkaXI...",
    "path": "/",
    "secure": true,
    "httpOnly": true,
    "sameSite": "lax",
    "expirationDate": 1759626625
  }
]
```

### 2. Cookie Editor Format
```json
{
  "cookies": [
    {
      "domain": ".example.com",
      "name": "cookie_name",
      "value": "cookie_value"
    }
  ]
}
```

### 3. Single Cookie Object
```json
{
  "domain": ".example.com",
  "name": "cookie_name",
  "value": "cookie_value"
}
```

## 🛠 Troubleshooting

### Extension Tidak Muncul
- Pastikan Developer mode aktif
- Reload extension di `chrome://extensions/`
- Periksa console untuk error

### Cookies Tidak Terupload
- Periksa format JSON file
- Pastikan domain valid
- Cek console browser untuk error details

### Situs Tidak Terbuka
- Pastikan popup blocker tidak aktif
- Coba buka situs manual setelah upload cookies

## 🔒 Keamanan

- ⚠️ **Hanya upload cookies dari sumber terpercaya**
- ⚠️ **Jangan share file cookies yang berisi data sensitif**
- ⚠️ **Extension memiliki akses penuh ke cookies browser**
- ✅ **Data hanya tersimpan lokal di browser**
- ✅ **Tidak ada data yang dikirim ke server eksternal**

## 📝 Catatan Penting

1. **Extension ini memerlukan permission tinggi** untuk mengakses cookies
2. **Cookies httpOnly dapat diset** (keunggulan utama vs JavaScript biasa)
3. **Data tersimpan di Chrome storage** dan akan hilang jika extension diuninstall
4. **Gunakan dengan bijak** dan hanya untuk keperluan legitimate

## 🆚 Perbandingan dengan Versi Web

| Fitur | Web Version | Extension Version |
|-------|-------------|-------------------|
| Set httpOnly cookies | ❌ | ✅ |
| Cross-domain support | ❌ | ✅ |
| Secure cookies | ❌ | ✅ |
| All cookie attributes | ❌ | ✅ |
| File protocol support | ❌ | ✅ |

## 🤝 Kontribusi

Jika Anda menemukan bug atau ingin menambahkan fitur:
1. Buat issue untuk melaporkan bug
2. Fork repository untuk kontribusi
3. Submit pull request dengan perubahan

## 📄 Lisensi

MIT License - Bebas digunakan untuk keperluan personal dan komersial.

---

**Happy Cookie Managing! 🍪**