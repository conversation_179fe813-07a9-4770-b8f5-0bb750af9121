@extends('layout.homeLayout')

@section('title', 'Dashboard')

@push('styles')
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section with Gradient Background -->
    <div class="gradient-bg relative overflow-hidden">
        <div class="absolute inset-0">
            <div
                class="absolute top-0 left-0 w-72 h-72 bg-white opacity-10 rounded-full -translate-x-1/2 -translate-y-1/2 floating">
            </div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full translate-x-1/2 translate-y-1/2 floating"
                style="animation-delay: -1s;"></div>
        </div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 mt-30">
            <div class="flex justify-between items-center mb-8">
                <div class="text-white">
                    <h1 class="text-4xl text-white font-bold mb-2">Dashboard</h1>
                    <p class="text-xl opacity-90">Selamat datang kembali, <span
                            class="font-semibold">{{ $user->name }}</span>!</p>
                    <p class="text-sm opacity-75 mt-1">Kelola akun dan akses aplikasi premium Anda</p>
                </div>
                <div>
                    <form method="GET" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit"
                            class="glass-effect text-white font-medium py-3 px-6 rounded-xl transition duration-300 flex items-center space-x-2 hover:bg-white hover:bg-opacity-20 border border-white border-opacity-20">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50 -mt-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Status Akun -->
                <div class="bg-white rounded-2xl shadow-lg border-0 p-8 card-hover relative overflow-hidden">
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -translate-y-10 translate-x-10 opacity-10">
                    </div>
                    <div class="flex items-center relative z-10">
                        <div
                            class="p-4 rounded-2xl {{ $user->is_active ? 'bg-gradient-to-br from-green-400 to-green-600' : 'bg-gradient-to-br from-red-400 to-red-600' }} shadow-lg">
                            <i class="fas {{ $user->is_active ? 'fa-check' : 'fa-times' }} text-white text-xl"></i>
                        </div>
                        <div class="ml-6">
                            <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Status Akun</p>
                            <p class="text-2xl font-bold {{ $user->is_active ? 'text-green-600' : 'text-red-600' }} mt-1">
                                {{ $user->is_active ? 'Aktif' : 'Tidak Aktif' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Role/Plan -->
                <div class="bg-white rounded-2xl shadow-lg border-0 p-8 card-hover relative overflow-hidden">
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -translate-y-10 translate-x-10 opacity-10">
                    </div>
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Plan</p>
                            <p class="text-lg font-semibold text-blue-600 capitalize">
                                {{ $user->role ?? 'Basic' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Status Pembayaran -->
                <div class="bg-white rounded-2xl shadow-lg border-0 p-8 card-hover relative overflow-hidden">
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -translate-y-10 translate-x-10 opacity-10">
                    </div>
                    <div class="flex items-center">
                        <div
                            class="p-3 rounded-full
                        @if ($user->payment_status === 'paid') bg-green-100
                        @elseif($user->payment_status === 'pending') bg-yellow-100
                        @else bg-red-100 @endif">
                            <svg class="w-6 h-6
                            @if ($user->payment_status === 'paid') text-green-600
                            @elseif($user->payment_status === 'pending') text-yellow-600
                            @else text-red-600 @endif"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Status Pembayaran</p>
                            <p
                                class="text-lg font-semibold
                            @if ($user->payment_status === 'paid') text-green-600
                            @elseif($user->payment_status === 'pending') text-yellow-600
                            @else text-red-600 @endif capitalize">
                                @if ($user->payment_status === 'paid')
                                    Lunas
                                @elseif($user->payment_status === 'pending')
                                    Tidak Ada
                                @else
                                    Belum Bayar
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Informasi Subscription -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Informasi Subscription</h2>

                        <div class="space-y-4">
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Plan Subscription</span>
                                <span
                                    class="font-medium text-gray-900 capitalize">{{ $user->subscription_plan ?? 'Tidak ada' }}</span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Status Subscription</span>
                                <span
                                    class="px-3 py-1 rounded-full text-sm font-medium
                                @if ($user->subscription_status === 'active') bg-green-100 text-green-800
                                @elseif($user->subscription_status === 'expired') bg-red-100 text-red-800
                                @else bg-gray-100 text-gray-800 @endif">
                                    {{ $user->subscription_status ?? 'Tidak ada' }}
                                </span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Tanggal Aktivasi</span>
                                <span class="font-medium text-gray-900">
                                    {{ $user->subscription_activated_at ? $user->subscription_activated_at->format('d M Y') : 'Belum diaktivasi' }}
                                </span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Tanggal Berakhir</span>
                                <span class="font-medium text-gray-900">
                                    {{ $user->subscription_expires_at ? $user->subscription_expires_at->format('d M Y') : 'Tidak ada batas waktu' }}
                                </span>
                            </div>

                            <div class="flex justify-between items-center py-3">
                                <span class="text-gray-600">Status Aktif</span>
                                <span
                                    class="px-3 py-1 rounded-full text-sm font-medium
                                {{ $user->isSubscriptionActive() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $user->isSubscriptionActive() ? 'Aktif' : 'Tidak Aktif' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Info -->
                <div class="space-y-6">
                    <!-- Informasi Penting -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Penting</h3>

                        <div class="space-y-3">
                            @if ($importantInfos->count() > 0)
                                @foreach ($importantInfos->take(3) as $info)
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                                        onclick="openInfoModal('{{ $info->id }}', '{{ addslashes($info->title) }}', '{{ addslashes($info->content) }}', '{{ $info->type }}')">
                                        <div class="flex items-center space-x-3">
                                            <div
                                                class="p-2 rounded-full
                                                @if ($info->type == 'info') bg-blue-100 text-blue-600
                                                @elseif($info->type == 'warning') bg-yellow-100 text-yellow-600
                                                @elseif($info->type == 'error') bg-red-100 text-red-600
                                                @elseif($info->type == 'success') bg-green-100 text-green-600
                                                @else bg-gray-100 text-gray-600 @endif">
                                                @if ($info->type == 'info')
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                        </path>
                                                    </svg>
                                                @elseif($info->type == 'warning')
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                                        </path>
                                                    </svg>
                                                @elseif($info->type == 'error')
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                @elseif($info->type == 'success')
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                @else
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                        </path>
                                                    </svg>
                                                @endif
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">{{ $info->title }}</p>
                                                <p class="text-xs text-gray-500">{{ Str::limit($info->content, 50) }}</p>
                                            </div>
                                        </div>
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <svg class="w-12 h-12 text-gray-300 mx-auto mb-2" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <p class="text-gray-500 text-sm">Tidak ada informasi penting saat ini</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Subscription Warning -->
                    @php
                        $daysLeft = $user->getDaysUntilExpiry();
                        $showWarning = $daysLeft !== null && $daysLeft <= 6;
                    @endphp

                    @if ($showWarning)
                        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
                            <div class="flex items-center mb-3">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                    </path>
                                </svg>
                                <h3 class="text-lg font-semibold text-red-800">Peringatan!</h3>
                            </div>
                            <p class="text-red-700 mb-4">
                                Akun Anda akan berakhir dalam
                                <span class="font-bold text-red-800">{{ $daysLeft }} hari</span>.
                                Segera lakukan pembayaran untuk perpanjangan.
                            </p>
                            <a href="{{ route('payment.index') }}"
                                class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                                Perpanjang Sekarang
                            </a>
                        </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

                        <div class="space-y-3">
                            @if ($user->subscription_status === 'active')
                                <a href="{{ route('sites.index') }}"
                                    class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                                    Akses Situs Premium
                                </a>
                            @endif

                            <a href="{{ route('payment.index') }}"
                                class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                                Pembayaran Manual
                            </a>

                            <a href="{{ route('installation.guide') }}"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 block text-center">
                                Panduan Pemasangan
                            </a>
                        </div>
                    </div>

                    <!-- Kontak Support -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white">
                        <h3 class="text-lg text-white font-semibold mb-2">Butuh Bantuan?</h3>
                        <p class="text-blue-100 text-sm mb-4">Tim support kami siap membantu Anda 24/7</p>
                        <button
                            class="bg-white text-blue-600 font-medium py-2 px-4 rounded-lg hover:bg-gray-100 transition duration-200">
                            Hubungi Support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script>
        function openInfoModal(id, title, content, type) {
            // Map type to SweetAlert2 icon
            let swalIcon = 'info';
            switch (type) {
                case 'info':
                    swalIcon = 'info';
                    break;
                case 'warning':
                    swalIcon = 'warning';
                    break;
                case 'error':
                    swalIcon = 'error';
                    break;
                case 'success':
                    swalIcon = 'success';
                    break;
                default:
                    swalIcon = 'info';
            }

            // Format content to handle line breaks
            const formattedContent = content.replace(/\\n/g, '<br>');

            // Show SweetAlert2 modal
            Swal.fire({
                title: title,
                html: `<div class="text-gray-700 leading-relaxed text-left">${formattedContent}</div>`,
                icon: swalIcon,
                confirmButtonText: 'Tutup',
                confirmButtonColor: '#6B7280',
                width: '600px',
                padding: '2rem',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp animate__faster'
                },
                customClass: {
                    popup: 'rounded-xl shadow-2xl',
                    title: 'text-xl font-bold text-gray-900 mb-4',
                    htmlContainer: 'text-base text-gray-700',
                    confirmButton: 'px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg'
                },
                backdrop: `
                    rgba(0,0,0,0.4)
                    url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23ffffff' fill-opacity='0.05'%3e%3ccircle cx='30' cy='30' r='4'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")
                    center/100px 100px
                `,
                buttonsStyling: false
            });
        }

        // === EXTENSION AUTHENTICATION BRIDGE ===
        // Simpan informasi user untuk akses ekstensi
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Data user dari server dengan struktur yang konsisten untuk ekstensi
                const userData = {
                    id: {{ $user->id }},
                    user_id: {{ $user->id }}, // Alias untuk konsistensi dengan backend
                    email: '{{ $user->email }}',
                    name: '{{ $user->name }}',
                    role: '{{ $user->role }}',
                    subscription_status: '{{ $user->subscription_status ?? 'inactive' }}',
                    subscription_active: {{ $user->isSubscriptionActive() ? 'true' : 'false' }},
                    subscription_plan: '{{ $user->subscription_plan ?? 'none' }}',
                    subscription_expires_at: '{{ $user->subscription_expires_at ?? null }}',
                    login_timestamp: new Date().getTime(),
                    session_key: '{{ session()->getId() }}',
                    session_token: '{{ csrf_token() }}',
                    timestamp: Date.now()
                };

                // Validasi data sebelum disimpan
                if (!userData.id || !userData.email) {
                    console.error('❌ Data user tidak lengkap');
                    return;
                }

                // Simpan ke localStorage untuk akses ekstensi
                localStorage.setItem('satu_pintu_user_auth', JSON.stringify(userData));

                // Simpan juga ke sessionStorage sebagai backup
                sessionStorage.setItem('satu_pintu_user_auth', JSON.stringify(userData));

                // Set flag bahwa user sudah login
                localStorage.setItem('satu_pintu_logged_in', 'true');
                sessionStorage.setItem('satu_pintu_logged_in', 'true');

                // Broadcast event untuk ekstensi yang mungkin sedang listening
                window.dispatchEvent(new CustomEvent('satuPintuUserLogin', {
                    detail: userData
                }));

                // Event tambahan untuk kompatibilitas dengan versi lama
                window.dispatchEvent(new CustomEvent('extensionUserLogin', {
                    detail: userData
                }));

                console.log('✅ Data autentikasi user berhasil disimpan untuk akses ekstensi');
                console.log('📋 Data yang disimpan:', userData);

            } catch (error) {
                console.error('❌ Gagal menyimpan data autentikasi:', error);
            }
        });

        // Cleanup saat logout (jika ada tombol logout di halaman ini)
        function handleLogout() {
            try {
                // Hapus data user dari storage
                localStorage.removeItem('satu_pintu_user_auth');
                localStorage.removeItem('satu_pintu_logged_in');
                localStorage.removeItem('extension_user_auth');
                localStorage.removeItem('extension_logged_in');
                sessionStorage.removeItem('satu_pintu_user_auth');
                sessionStorage.removeItem('satu_pintu_logged_in');
                sessionStorage.removeItem('extension_user_auth');
                sessionStorage.removeItem('extension_logged_in');

                // Broadcast logout event untuk ekstensi
                window.dispatchEvent(new CustomEvent('satuPintuUserLogout'));
                window.dispatchEvent(new CustomEvent('extensionUserLogout'));

                console.log('✅ Data autentikasi user berhasil dibersihkan');
            } catch (error) {
                console.error('❌ Gagal membersihkan data autentikasi:', error);
            }
        }

        // Attach logout handler ke tombol logout jika ada
        const logoutButton = document.querySelector('form[action*="logout"] button[type="submit"]');
        if (logoutButton) {
            logoutButton.addEventListener('click', handleLogout);
        }
    </script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showSupportModal() {
            Swal.fire({
                title: 'Hubungi Support',
                html: `
                    <div class="text-center space-y-4">
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">WhatsApp Support</h4>
                            <p class="text-green-700">Admin tersedia 24/7 untuk membantu Anda</p>
                            <a href="https://wa.me/6281234567890" target="_blank"
                               class="inline-block mt-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                Chat via WhatsApp
                            </a>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p>Atau kirim email ke: <strong><EMAIL></strong></p>
                        </div>
                    </div>
                `,
                icon: 'question',
                showConfirmButton: false
            });
        }

        // Auto show warning if subscription is near expiry
        document.addEventListener('DOMContentLoaded', function() {
            @if ($showWarning)
                setTimeout(() => {
                    Swal.fire({
                        title: 'Peringatan Masa Aktif!',
                        html: `
                        <div class="text-center">
                            <p class="text-lg mb-4">Akun Anda akan berakhir dalam</p>
                            <div class="bg-red-100 text-red-800 text-2xl font-bold py-3 px-6 rounded-lg mb-4">
                                {{ $daysLeft }} hari
                            </div>
                            <p class="text-gray-600 mb-4">Segera lakukan pembayaran untuk perpanjangan akun Anda.</p>
                        </div>
                    `,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Perpanjang Sekarang',
                        cancelButtonText: 'Nanti',
                        confirmButtonColor: '#EF4444'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '{{ route('payment.index') }}';
                        }
                    });
                }, 2000);
            @endif
        });

        // Function to copy payment details
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                Swal.fire({
                    icon: 'success',
                    title: 'Tersalin!',
                    text: 'Informasi telah disalin ke clipboard',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        }
    </script>
@endsection
