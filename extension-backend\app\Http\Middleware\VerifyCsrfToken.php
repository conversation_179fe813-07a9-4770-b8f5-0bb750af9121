<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\ValidateCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        // Add any routes that should be excluded from CSRF verification
        'livewire/*',
        'filament/*',
        'api/*',
        'sanctum/csrf-cookie',
        'v2/*',
        'extension/*',
        'admin/*'
    ];
}
