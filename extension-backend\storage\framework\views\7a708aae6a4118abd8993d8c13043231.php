<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .site-card {
            position: relative;
            overflow: hidden;
        }

        .site-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <div class="gradient-bg relative overflow-hidden py-16">
        <div class="absolute inset-0">
            <div
                class="absolute top-0 left-0 w-72 h-72 bg-white opacity-10 rounded-full -translate-x-1/2 -translate-y-1/2 floating">
            </div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full translate-x-1/2 translate-y-1/2 floating"
                style="animation-delay: -1s;"></div>
        </div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-30">
            <div class="flex flex-col lg:flex-row items-center justify-between">
                <div class="text-white mb-8 lg:mb-0">
                    <h1 class="text-4xl text-white lg:text-5xl font-bold mb-4">Akses Situs Premium</h1>
                    <p class="text-xl opacity-90 mb-2">
                        Paket Anda: <span
                            class="font-bold bg-white text-black bg-opacity-20 px-3 py-1 rounded-full"><?php echo e(ucfirst($user->role)); ?></span>
                    </p>
                    <p class="text-sm opacity-75">Nikmati akses unlimited ke semua aplikasi premium favorit Anda</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50 mt-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">

            <!-- Sites Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 pt-8">
                <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="site-card bg-white rounded-2xl shadow-lg card-hover border-0 overflow-hidden">
                        <div class="p-6">
                            <!-- Site Logo -->
                            <div class="flex items-center mb-6">
                                <div class="relative">
                                    <img src="<?php echo e($site->logo_path ? Storage::url($site->logo_path) : '/images/default-site-logo.png'); ?>"
                                        alt="<?php echo e($site->name); ?>" class="w-16 h-16 rounded-2xl object-cover shadow-md">
                                    <div
                                        class="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h3 class="text-lg font-bold text-gray-900 mb-1"><?php echo e($site->name); ?></h3>
                                    <p class="text-sm text-gray-500 truncate">
                                        <?php echo e($site->domain ?? parse_url($site->url, PHP_URL_HOST)); ?></p>
                                </div>
                            </div>

                            <!-- Site Description -->
                            <p class="text-sm text-gray-600 mb-6 line-clamp-2 leading-relaxed">
                                <?php echo e($site->description ?? 'Aplikasi premium dengan fitur lengkap untuk produktivitas maksimal'); ?>

                            </p>

                            <!-- Stats Row -->
                            <div class="flex items-center justify-between mb-6">
                                <!-- Visibility Badge -->
                                <div>
                                    <?php switch($site->visibility):
                                        case ('elite'): ?>
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                                                <i class="fas fa-crown mr-1"></i>
                                                Elite
                                            </span>
                                        <?php break; ?>

                                        <?php case ('premium'): ?>
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                                                <i class="fas fa-star mr-1"></i>
                                                Premium
                                            </span>
                                        <?php break; ?>

                                        <?php case ('both'): ?>
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-green-500 to-green-600 text-white">
                                                <i class="fas fa-users mr-1"></i>
                                                All Access
                                            </span>
                                        <?php break; ?>
                                    <?php endswitch; ?>
                                </div>
                            </div>

                            <!-- Alternative Accounts Count -->
                            <div class="flex items-center text-sm text-gray-500 mb-6">
                                <div class="p-1 bg-blue-100 rounded-full mr-2">
                                    <i class="fas fa-users text-blue-600 text-xs"></i>
                                </div>
                                <?php echo e(count($site->alternative_accounts ?? [])); ?> akun alternatif tersedia
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-col space-y-3">
                                <a href="<?php echo e(route('sites.show', $site)); ?>"
                                    class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-center py-3 px-4 rounded-xl transition-all duration-300 text-sm font-semibold transform hover:scale-105 shadow-md">
                                    <i class="fas fa-eye mr-2"></i>
                                    Lihat Detail
                                </a>
                                <a href="<?php echo e($site->url); ?>" target="_blank" rel="noopener noreferrer"
                                    class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-center py-3 px-4 rounded-xl transition-all duration-300 text-sm font-semibold border border-gray-200">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    Kunjungi Situs
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($sites->isEmpty()): ?>
                <div class="text-center py-20">
                    <div
                        class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-globe text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Tidak Ada Situs Tersedia</h3>
                    <p class="text-gray-500 mb-6 max-w-md mx-auto">Belum ada situs yang sesuai dengan paket langganan Anda.
                        Hubungi admin untuk informasi lebih lanjut.</p>
                    <a href="<?php echo e(route('payment.index')); ?>"
                        class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300">
                        <i class="fas fa-upgrade mr-2"></i>
                        Upgrade Paket
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/sites/index.blade.php ENDPATH**/ ?>