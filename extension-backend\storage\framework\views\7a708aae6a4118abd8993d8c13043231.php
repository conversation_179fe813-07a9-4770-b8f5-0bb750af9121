<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Akses Situs Premium</h1>
                    <p class="mt-1 text-sm text-gray-600">
                        Anda memiliki akses ke situs berikut berdasarkan paket langganan Anda:
                        <span class="font-semibold text-blue-600"><?php echo e(ucfirst($user->role)); ?></span>
                    </p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Sisa hari langganan:</div>
                    <div class="text-2xl font-bold text-green-600">
                        <?php echo e($user->getDaysUntilExpiry() ?? 'Unlimited'); ?>

                    </div>
                </div>
            </div>
        </div>

        <!-- Sites Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <!-- Site Logo -->
                        <div class="flex items-center mb-4">
                            <img src="<?php echo e($site->logo_path ? Storage::url($site->logo_path) : '/images/default-site-logo.png'); ?>"
                                 alt="<?php echo e($site->name); ?>"
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900"><?php echo e($site->name); ?></h3>
                                <p class="text-sm text-gray-500"><?php echo e($site->domain ?? parse_url($site->url, PHP_URL_HOST)); ?></p>
                            </div>
                        </div>

                        <!-- Site Description -->
                        <p class="text-sm text-gray-600 mb-4 line-clamp-2">
                            <?php echo e($site->description ?? 'Tidak ada deskripsi'); ?>

                        </p>

                        <!-- Visibility Badge -->
                        <div class="mb-4">
                            <?php switch($site->visibility):
                                case ('elite'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Elite Only
                                    </span>
                                    <?php break; ?>
                                <?php case ('premium'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Premium Only
                                    </span>
                                    <?php break; ?>
                                <?php case ('both'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Semua Role
                                    </span>
                                    <?php break; ?>
                            <?php endswitch; ?>
                        </div>

                        <!-- JSON Files Count -->
                        <div class="flex items-center text-sm text-gray-500 mb-4">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <?php echo e($site->jsonFiles()->count()); ?> file JSON
                        </div>

                        <!-- Alternative Accounts Count -->
                        <div class="flex items-center text-sm text-gray-500 mb-4">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <?php echo e(count($site->alternative_accounts ?? [])); ?> akun alternatif
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('sites.show', $site)); ?>"
                               class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium">
                                Lihat Detail
                            </a>
                            <a href="<?php echo e($site->url); ?>"
                               target="_blank"
                               rel="noopener noreferrer"
                               class="flex-1 bg-gray-200 text-gray-800 text-center py-2 px-4 rounded-md hover:bg-gray-300 transition-colors text-sm font-medium">
                                Kunjungi
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <?php if($sites->isEmpty()): ?>
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada situs tersedia</h3>
                <p class="mt-1 text-sm text-gray-500">Belum ada situs yang sesuai dengan paket langganan Anda.</p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/sites/index.blade.php ENDPATH**/ ?>