<?php

namespace App\Filament\Resources\ApiV2\ImportantInfoResource\Pages;

use App\Filament\Resources\ApiV2\ImportantInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Notifications\Notification;

class EditImportantInfo extends EditRecord
{
    protected static string $resource = ImportantInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make()
                ->requiresConfirmation()
                ->modalHeading('Hapus Informasi')
                ->modalDescription('Apakah Anda yakin ingin menghapus informasi ini? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Hapus'),
        ];
    }
    
    public function getTitle(): string
    {
        return 'Edit Informasi Penting';
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Informasi berhasil diperbarui')
            ->body('Perubahan informasi telah berhasil disimpan.');
    }
}