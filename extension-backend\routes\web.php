<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Web\DashboardController;
use Illuminate\Support\Facades\Route;

Route::get('/', [HomeController::class, 'index'])->name('home');

Route::get('/storage-link', function () {
    $targetFolder = base_path() . '/storage/app/public';
    $linkFolder =  $_SERVER['DOCUMENT_ROOT'] . '/storage';

    if (!file_exists($linkFolder)) {
        symlink($targetFolder, $linkFolder);
        return "Storage link successfully created!";
    } else {
        return "Storage link already exists!";
    }
});

// Routes untuk autentikasi pengguna elite/premium
Route::get('/login', [AuthController::class, 'login'])->name('login');
Route::post('/login', [AuthController::class, 'authenticate'])->name('login.authenticate');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');

Route::get('/register', [AuthController::class, 'register'])->name('register');
Route::post('/register', [AuthController::class, 'store'])->name('register.store');

// Password Reset Routes
Route::get('/forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
Route::post('/forgot-password', [AuthController::class, 'sendResetLinkEmail'])->name('password.email');
Route::get('/verify-otp', [AuthController::class, 'showVerifyOtpForm'])->name('password.verify-otp-form');
Route::post('/verify-otp', [AuthController::class, 'verifyOtp'])->name('password.verify-otp');
Route::post('/resend-otp', [AuthController::class, 'resendOtp'])->name('password.resend-otp');
Route::get('/reset-password', [AuthController::class, 'showResetPasswordForm'])->name('password.reset-form');
Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');

// Routes yang memerlukan autentikasi anyones
Route::middleware('auth:anyone')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('user.dashboard');
    
    Route::get('/installation-guide', function () {
        return view('installation_guide');
    })->name('installation.guide');
    
    // Routes untuk pembayaran berlangganan
    Route::prefix('payment')->group(function () {
        Route::get('/', [App\Http\Controllers\PaymentController::class, 'index'])->name('payment.index');
    });
    
    // Routes untuk akses situs berdasarkan langganan
    Route::prefix('sites')->middleware(['subscription.access'])->group(function () {
        Route::get('/', [App\Http\Controllers\SiteAccessController::class, 'index'])->name('sites.index');
        Route::get('/{site}', [App\Http\Controllers\SiteAccessController::class, 'show'])->name('sites.show');
    });
});
