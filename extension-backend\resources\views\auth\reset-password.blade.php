@extends('layout.authLayout')

@section('title', 'Reset Kata Sandi')

@section('content')
    <div class="min-h-screen bg-gray-100 text-gray-900 flex justify-center">
        <div class="max-w-screen-xl m-0 sm:m-10 bg-white shadow sm:rounded-lg flex justify-center flex-1">
            <div class="lg:w-1/2 xl:w-5/12 p-6 sm:p-12">
                <a href="{{ route('home') }}" class="flex flex-col items-center">
                    <img src="{{ asset('assets/img/logo_satu_pintu.png') }}" class="w-20 h-auto" />
                    <h1 class="text-2xl font-bold text-gray-800 mt-2">Satu Pintu</h1>
                </a>
                <div class="mt-12 flex flex-col items-center">
                    <div class="w-full flex-1 mt-8">
                        <div class="text-center mb-8">
                            <h2 class="text-xl font-semibold text-gray-800 mb-2">Reset Kata Sandi</h2>
                            <p class="text-sm text-gray-600">
                                Masukkan kata sandi baru untuk akun Anda.
                            </p>
                        </div>

                        <div class="mx-auto max-w-xs">
                            @if (session('error'))
                                <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                                    {{ session('error') }}
                                </div>
                            @endif

                            <form method="POST" action="{{ route('password.update') }}">
                                @csrf
                                <input type="hidden" name="token" value="{{ $token ?? '' }}">
                                <input type="hidden" name="email" value="{{ $email ?? old('email') }}">
                                
                                <input
                                    class="w-full px-8 py-4 rounded-lg font-medium bg-gray-100 border border-gray-200 placeholder-gray-500 text-sm focus:outline-none focus:border-gray-400 focus:bg-white"
                                    type="password" name="password" placeholder="Kata Sandi Baru" required />
                                @error('password')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                
                                <input
                                    class="w-full px-8 py-4 rounded-lg font-medium bg-gray-100 border border-gray-200 placeholder-gray-500 text-sm focus:outline-none focus:border-gray-400 focus:bg-white mt-5"
                                    type="password" name="password_confirmation" placeholder="Konfirmasi Kata Sandi Baru" required />
                                
                                <div class="mt-4 text-xs text-gray-600">
                                    <p class="mb-1">Kata sandi harus memenuhi kriteria berikut:</p>
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Minimal 8 karakter</li>
                                        <li>Mengandung huruf besar dan kecil</li>
                                        <li>Mengandung angka</li>
                                        <li>Mengandung karakter khusus (!@#$%^&*)</li>
                                    </ul>
                                </div>
                                
                                <button type="submit"
                                    class="mt-5 tracking-wide font-semibold bg-green-400 text-white w-full py-4 rounded-lg hover:bg-green-700 transition-all duration-300 ease-in-out flex items-center justify-center focus:shadow-outline focus:outline-none">
                                    <span class="ml-">
                                        Reset Kata Sandi
                                    </span>
                                </button>
                            </form>
                            
                            <div class="mt-6 text-center">
                                <a href="{{ route('login') }}"
                                    class="text-xs text-blue-600 hover:text-blue-800">
                                    Kembali ke halaman login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex-1 bg-green-100 text-center hidden lg:flex">
                <div class="m-12 xl:m-16 w-full bg-contain bg-center bg-no-repeat"
                    style="background-image: url('https://cdni.iconscout.com/illustration/premium/thumb/reset-password-illustration-download-in-svg-png-gif-file-formats--change-security-login-digital-marketing-pack-business-illustrations-8333962.png');">
                </div>
            </div>
        </div>
    </div>
@endsection