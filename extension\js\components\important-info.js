/**
 * Important Info Component
 * Displays important information at the bottom of the extension
 */

class ImportantInfoComponent {
    constructor() {
        this.container = null;
        this.info = null;
        this.isVisible = false;
        this.autoHideTimer = null;
    }

    /**
     * Initialize the component
     */
    init() {
        // Create container if it doesn't exist
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'important-info-container';
            this.container.style.display = 'none';
            document.body.appendChild(this.container);

            // Add styles
            const style = document.createElement('style');
            style.textContent = `
                .important-info-container {
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background-color: rgba(0, 0, 0, 0.8);
                    backdrop-filter: blur(10px);
                    color: white;
                    padding: 10px 15px;
                    font-size: 12px;
                    z-index: 1000;
                    transition: transform 0.3s ease-in-out;
                    transform: translateY(100%);
                    border-top: 1px solid rgba(255, 255, 255, 0.1);
                }
                
                .important-info-container.visible {
                    transform: translateY(0);
                }
                
                .important-info-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                
                .important-info-text {
                    flex: 1;
                }
                
                .important-info-title {
                    font-weight: bold;
                    margin-bottom: 2px;
                }
                
                .important-info-message {
                    opacity: 0.8;
                }
                
                .important-info-close {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    opacity: 0.6;
                    font-size: 16px;
                    padding: 0 0 0 10px;
                    transition: opacity 0.2s;
                }
                
                .important-info-close:hover {
                    opacity: 1;
                }
                
                .important-info-container.info {
                    background-color: rgba(59, 130, 246, 0.9);
                }
                
                .important-info-container.warning {
                    background-color: rgba(245, 158, 11, 0.9);
                }
                
                .important-info-container.error {
                    background-color: rgba(239, 68, 68, 0.9);
                }
                
                .important-info-container.success {
                    background-color: rgba(34, 197, 94, 0.9);
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Show important information
     * @param {Object} info - The important information object
     * @param {boolean} autoHide - Whether to automatically hide the info after a delay
     */
    show(info, autoHide = false) {
        if (!info) return;
        
        this.info = info;
        this.init();
        
        // Clear any existing auto-hide timer
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }
        
        // Update content
        this.container.innerHTML = `
            <div class="important-info-content">
                <div class="important-info-text">
                    <div class="important-info-title">${info.title}</div>
                    <div class="important-info-message">${info.content}</div>
                </div>
                <button class="important-info-close">×</button>
            </div>
        `;
        
        // Set type class
        this.container.className = 'important-info-container';
        if (info.type) {
            this.container.classList.add(info.type);
        }
        
        // Show container
        this.container.style.display = 'block';
        setTimeout(() => {
            this.container.classList.add('visible');
        }, 10);
        
        this.isVisible = true;
        
        // Add close button event listener
        const closeButton = this.container.querySelector('.important-info-close');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.hide());
        }
        
        // Auto-hide if specified
        if (autoHide) {
            this.autoHideTimer = setTimeout(() => {
                this.hide();
            }, 10000); // Hide after 10 seconds
        }
    }

    /**
     * Hide the important information
     */
    hide() {
        if (!this.container || !this.isVisible) return;
        
        this.container.classList.remove('visible');
        this.isVisible = false;
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            this.container.style.display = 'none';
        }, 300);
    }
}

// Create and export a singleton instance
const importantInfo = new ImportantInfoComponent();
export default importantInfo;