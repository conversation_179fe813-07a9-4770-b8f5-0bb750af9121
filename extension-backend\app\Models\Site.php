<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Site extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'url',
        'domain',
        'category',
        'visibility',
        'thumbnail',
        'logo_path',
        'description',
        'cookie_file_path',
        'cookie_file_uploaded_at',
        'cookies',
        'cookies_json',
        'alternative_accounts',
        'login_url',
        'email_selector',
        'password_selector',
        'submit_selector',
        'additional_script',
        'enable_custom_redirect',
        'redirect_title',
        'redirect_content',
        'redirect_url',
        'redirect_delay',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'cookie_file_uploaded_at' => 'datetime',
            'cookies' => 'array',
            'cookies_json' => 'array',
            'json_files' => 'array',
            'alternative_accounts' => 'array',
            'enable_custom_redirect' => 'boolean',
            'redirect_delay' => 'integer',
        ];
    }

    /**
     * Get cookies from uploaded JSON file
     */
    public function getCookiesFromFile(): array
    {
        if (!$this->cookie_file_path) {
            return [];
        }

        $filePath = storage_path('app/public/' . $this->cookie_file_path);
        
        if (!file_exists($filePath)) {
            return [];
        }

        $content = file_get_contents($filePath);
        $cookies = json_decode($content, true);

        return is_array($cookies) ? $cookies : [];
    }

    /**
     * Get cookies attribute (for backward compatibility)
     */
    public function getCookiesAttribute(): array
    {
        return $this->getCookiesFromFile();
    }

    /**
     * Get the category that owns the site.
     */
    public function categoryModel()
    {
        return $this->belongsTo(Category::class, 'category', 'slug');
    }

    /**
     * Scope for active sites
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for sites by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get category name
     */
    public function getCategoryName(): string
    {
        return $this->categoryModel ? $this->categoryModel->name : ucfirst($this->category);
    }

    /**
     * Get category icon
     */
    public function getCategoryIcon(): ?string
    {
        return $this->categoryModel ? $this->categoryModel->icon : null;
    }

    /**
     * Get category color
     */
    public function getCategoryColor(): string
    {
        return $this->categoryModel ? $this->categoryModel->color : '#6366f1';
    }

    /**
     * Check if site is visible for elite users
     */
    public function isVisibleForElite(): bool
    {
        return in_array($this->visibility, ['elite', 'both']);
    }

    /**
     * Check if site is visible for premium users
     */
    public function isVisibleForPremium(): bool
    {
        return in_array($this->visibility, ['premium', 'both']);
    }

    /**
     * Scope for sites visible to user role
     */
    public function scopeVisibleToRole($query, $role)
    {
        if ($role === 'elite') {
            return $query->whereIn('visibility', ['elite', 'both']);
        } elseif ($role === 'premium') {
            return $query->whereIn('visibility', ['premium', 'both']);
        }
        
        return $query;
    }
    
    /**
     * Get all cookies from uploaded JSON file
     */
    public function getAllCookies(): array
    {
        return $this->getCookiesFromFile();
    }

    /**
     * Get the JSON files for the site.
     */
    public function jsonFiles()
    {
        return $this->hasMany(SiteJsonFile::class);
    }

    /**
     * Get active JSON files for the site.
     */
    public function activeJsonFiles()
    {
        return $this->hasMany(SiteJsonFile::class)->where('is_active', true);
    }
}
