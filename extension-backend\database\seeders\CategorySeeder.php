<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'General',
                'slug' => 'general',
                'icon' => '🌐',
                'color' => '#6366f1',
                'description' => 'Kategori umum untuk situs web',
                'sort_order' => 0,
                'is_active' => true,
            ],
            [
                'name' => 'Social Media',
                'slug' => 'social-media',
                'icon' => '📱',
                'color' => '#3b82f6',
                'description' => 'Platform media sosial dan komunikasi',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Streaming',
                'slug' => 'streaming',
                'icon' => '🎬',
                'color' => '#ef4444',
                'description' => 'Platform streaming video dan musik',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Education',
                'slug' => 'education',
                'icon' => '📚',
                'color' => '#10b981',
                'description' => 'Platform pembelajaran dan edukasi',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Artificial Intelligence',
                'slug' => 'artificial-intelligence',
                'icon' => '🤖',
                'color' => '#8b5cf6',
                'description' => 'Platform AI dan machine learning',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'Productivity',
                'slug' => 'productivity',
                'icon' => '⚡',
                'color' => '#f59e0b',
                'description' => 'Tools produktivitas dan manajemen',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Design',
                'slug' => 'design',
                'icon' => '🎨',
                'color' => '#ec4899',
                'description' => 'Tools desain dan kreativitas',
                'sort_order' => 6,
                'is_active' => true,
            ],
            [
                'name' => 'Development',
                'slug' => 'development',
                'icon' => '💻',
                'color' => '#06b6d4',
                'description' => 'Platform development dan coding',
                'sort_order' => 7,
                'is_active' => true,
            ],
            [
                'name' => 'E-commerce',
                'slug' => 'e-commerce',
                'icon' => '🛒',
                'color' => '#84cc16',
                'description' => 'Platform belanja online',
                'sort_order' => 8,
                'is_active' => true,
            ],
            [
                'name' => 'Gaming',
                'slug' => 'gaming',
                'icon' => '🎮',
                'color' => '#a855f7',
                'description' => 'Platform gaming dan hiburan',
                'sort_order' => 9,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
