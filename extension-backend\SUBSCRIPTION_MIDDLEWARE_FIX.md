# Perbaikan Middleware CheckSubscriptionStatus

## Ma<PERSON>ah yang <PERSON>

### 1. **Update yang Terlalu Agresif**
- Middleware melakukan update database setiap kali pengecekan, bahkan untuk user yang sudah expired
- Tidak ada validasi apakah user sudah dalam status expired

### 2. **Field yang Tidak Valid**
- Duplikasi field `subscription_status` dalam query update
- Field `payment_status` yang tidak ada dalam model `Anyone`

### 3. **Logika Pengecekan yang Tidak Konsisten**
- Method `isSubscriptionExpired()` tidak mempertimbangkan status subscription
- Tidak ada pengecekan ganda untuk memastikan akurasi

## Perbaikan yang Dilakukan

### 1. **Middleware CheckSubscriptionStatus.php**

#### Sebelum:
```php
if ($user->isSubscriptionExpired()) {
    Anyone::where('id', $user->id)->update([
        'subscription_status' => 'expired',
        'payment_status' => 'pending',        // Field tidak ada
        'subscription_status' => 'expired',   // Duplikasi
        'subscription_plan' => null,
        'role' => 'none',
    ]);
}
```

#### Sesudah:
```php
if ($user->isSubscriptionExpired()) {
    // Double check: only update if subscription_status is not already 'expired'
    if ($user->subscription_status !== 'expired') {
        Anyone::where('id', $user->id)->update([
            'subscription_status' => 'expired',
            'subscription_plan' => null,
            'role' => 'none',
            'is_active' => false,
        ]);
        
        // Refresh user model to get updated data
        $user->refresh();
    }
}
```

### 2. **Model Anyone.php - Method isSubscriptionExpired()**

#### Sebelum:
```php
public function isSubscriptionExpired(): bool
{
    if (!$this->subscription_expires_at) {
        return false; // No expiry date means never expired
    }

    return $this->subscription_expires_at->isPast();
}
```

#### Sesudah:
```php
public function isSubscriptionExpired(): bool
{
    // If subscription status is already expired, return true
    if ($this->subscription_status === 'expired') {
        return true;
    }
    
    // If no expiry date and status is active/pending, never expired
    if (!$this->subscription_expires_at && in_array($this->subscription_status, ['active', 'pending'])) {
        return false;
    }
    
    // If no expiry date but status is not active/pending, consider expired
    if (!$this->subscription_expires_at) {
        return !in_array($this->subscription_status, ['active', 'pending']);
    }

    // Check if expiry date has passed
    return $this->subscription_expires_at->isPast();
}
```

### 3. **Model Anyone.php - Method isSubscriptionActive()**

#### Perbaikan:
```php
public function isSubscriptionActive(): bool
{
    // If subscription status is expired, cancelled, or suspended, not active
    if (in_array($this->subscription_status, ['expired', 'cancelled', 'suspended'])) {
        return false;
    }
    
    // Check if subscription status is active or pending
    if (in_array($this->subscription_status, ['active', 'pending'])) {
        // If no expiry date, consider it unlimited access
        if (!$this->subscription_expires_at) {
            return true;
        }
        // Check if not expired (still in future or today)
        return $this->subscription_expires_at->isFuture() || $this->subscription_expires_at->isToday();
    }

    return false;
}
```

### 4. **Model Anyone.php - Method getDaysUntilExpiry()**

#### Perbaikan:
```php
public function getDaysUntilExpiry(): ?int
{
    // If subscription is already expired, return 0
    if ($this->subscription_status === 'expired') {
        return 0;
    }
    
    if (!$this->subscription_expires_at) {
        return null; // Unlimited
    }

    $daysLeft = now()->diffInDays($this->subscription_expires_at, false);
    
    // If negative (expired), return 0
    return max(0, $daysLeft);
}
```

### 5. **Command untuk Pengecekan Berkala**

Dibuat command `CheckExpiredSubscriptions` untuk menjalankan pengecekan subscription expired secara berkala:

```php
php artisan subscription:check-expired
```

## Logika Pengecekan yang Diperbaiki

### 1. **Prioritas Pengecekan:**
1. Cek status subscription terlebih dahulu
2. Jika sudah expired, langsung return true
3. Jika tidak ada tanggal expired dan status active/pending, return false
4. Jika ada tanggal expired, cek apakah sudah lewat

### 2. **Update Database:**
- Hanya update jika status belum expired
- Menghindari update berulang yang tidak perlu
- Refresh model setelah update untuk konsistensi data

### 3. **Konsistensi Status:**
- `subscription_status` menjadi sumber kebenaran utama
- `subscription_expires_at` sebagai validasi tambahan
- `is_active` di-update sesuai status subscription

## Skenario Testing

### 1. **User dengan Subscription Aktif**
```php
// User dengan subscription_status = 'active' dan expires_at masih future
$user->subscription_status = 'active';
$user->subscription_expires_at = Carbon::now()->addDays(10);

// Result: isSubscriptionExpired() = false, tidak ada update
```

### 2. **User dengan Subscription Expired (Tanggal)**
```php
// User dengan subscription_status = 'active' tapi expires_at sudah past
$user->subscription_status = 'active';
$user->subscription_expires_at = Carbon::now()->subDays(1);

// Result: isSubscriptionExpired() = true, update status ke expired
```

### 3. **User dengan Status Expired**
```php
// User dengan subscription_status = 'expired'
$user->subscription_status = 'expired';

// Result: isSubscriptionExpired() = true, TIDAK ada update (sudah expired)
```

### 4. **User dengan Unlimited Subscription**
```php
// User dengan subscription_status = 'active' dan expires_at = null
$user->subscription_status = 'active';
$user->subscription_expires_at = null;

// Result: isSubscriptionExpired() = false, tidak ada update
```

## Manfaat Perbaikan

1. **Performa Lebih Baik**: Menghindari update database yang tidak perlu
2. **Konsistensi Data**: Status subscription menjadi lebih akurat
3. **Logika yang Jelas**: Prioritas pengecekan yang terstruktur
4. **Maintenance**: Command untuk pengecekan berkala
5. **Debugging**: Log yang lebih informatif

## Cara Menjalankan

### 1. **Manual Check**
```bash
php artisan subscription:check-expired
```

### 2. **Scheduled Check (Tambahkan ke Kernel.php)**
```php
$schedule->command('subscription:check-expired')->daily();
```

### 3. **Testing**
```bash
php artisan test --filter SubscriptionTest
```

---

**Catatan**: Semua perbaikan mempertahankan backward compatibility dan tidak mengubah struktur database yang ada.
