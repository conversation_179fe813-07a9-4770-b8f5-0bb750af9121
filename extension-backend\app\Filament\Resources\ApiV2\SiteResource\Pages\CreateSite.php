<?php

namespace App\Filament\Resources\ApiV2\SiteResource\Pages;

use App\Filament\Resources\ApiV2\SiteResource;
use App\Models\SiteJsonFile;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class CreateSite extends CreateRecord
{
    protected static string $resource = SiteResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Extract json_files from data before creating the site
        $jsonFiles = $data['json_files'] ?? [];
        unset($data['json_files']);

        // Create the site record
        $record = static::getModel()::create($data);

        // Handle JSON file uploads after site creation
        if (!empty($jsonFiles)) {
            foreach ($jsonFiles as $index => $filePath) {
                if ($filePath && Storage::disk('public')->exists($filePath)) {
                    // Get original filename for name
                    $originalName = pathinfo($filePath, PATHINFO_FILENAME);
                    
                    // Create SiteJsonFile record
                    SiteJsonFile::create([
                        'site_id' => $record->id,
                        'name' => $originalName ?: 'JSON File ' . ($index + 1),
                        'file_path' => $filePath,
                        'description' => 'Uploaded JSON file',
                        'is_active' => true,
                    ]);
                }
            }
        }

        return $record;
    }
}