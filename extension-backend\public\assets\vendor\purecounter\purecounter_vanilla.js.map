{"version": 3, "file": "purecounter_vanilla.js", "mappings": ";;;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,IARxB,CASGK,MAAM,WACT,O,23BCTA,SAASC,EAAWC,GAAyB,IAAjBC,EAAiB,uDAAJ,GAEjCC,EAAY,GAGhB,IAAK,IAAIC,KAAOH,EAEZ,GAAIC,GAAc,IAAOA,EAAWG,eAAeD,GAAnD,CAEA,IAAIE,EAAMC,EAAWN,EAAOG,IAE5BD,EAAUC,GAAOE,EAGbF,EAAII,MAAM,oBACVL,EAAUC,GAAqB,kBAAPE,EAAyB,IAANA,EAAaA,GAKhE,OAAOG,OAAOC,OAAO,GAAIR,EAAYC,GAIzC,SAASQ,EAAaC,EAASX,GAE3B,IAAIY,GACCZ,EAAOa,IAAMb,EAAOc,QAAUd,EAAOe,SAAWf,EAAOgB,OAExDC,EAAY,MAGZjB,EAAOc,MAAQd,EAAOa,MACtBI,EAAY,MACZL,IAAsB,GAI1B,IAAIM,EAAeZ,EAAWN,EAAOc,OAErCH,EAAQQ,UAAYC,EAAaF,EAAclB,IAG3B,IAAhBA,EAAOqB,MACPV,EAAQW,aAAa,4BAA6B,GAItD,IAAIC,EAAgBC,aAAY,WAE5B,IAAIC,EA8BZ,SAAoBC,EAAQC,GAAqB,IAAdC,EAAc,uDAAP,MAOtC,OALAF,EAASpB,EAAWoB,GACpBC,EAAQrB,EAAWqB,GAIZE,WAAoB,QAATD,EAAiBF,EAASC,EAAQD,EAASC,GArC3CG,CAAWZ,EAAcN,EAAmBK,GAE1DN,EAAQQ,UAAYC,EAAaK,EAASzB,KAE1CkB,EAAeO,IAIMzB,EAAOa,KAAoB,OAAbI,GAC9BC,GAAgBlB,EAAOa,KAAoB,OAAbI,KAE/BN,EAAQQ,UAAYC,EAAapB,EAAOa,IAAKb,GAEzCA,EAAO+B,QAEPpB,EAAQW,aAAa,4BAA6B,GAElDU,YAAW,WACPrB,EAAQW,aACJ,4BACAtB,EAAOe,SAAW,OAEvBf,EAAO+B,QAEdE,cAAcV,MAEnBvB,EAAOgB,OAyDd,SAASkB,EAAqBC,EAAYC,GACtC,OAAOC,KAAKC,IAAIH,EAAYC,GA4DhC,SAAShB,EAAaM,EAAQ1B,GAE1B,IAAIuC,EAAY,CACZC,sBAAuBxC,EAAOyC,SAC9BC,sBAAuB1C,EAAOyC,UAG9BE,EAAoC,iBAApB3C,EAAO4C,SAAwB5C,EAAO4C,cAAWC,EAWrE,OARAnB,EAjHJ,SAAuBA,EAAQ1B,GAE3B,GAAIA,EAAO8C,YAAc9C,EAAO+C,SAAU,CACtCrB,EAASW,KAAKW,IAAIC,OAAOvB,IAEzB,IAAIS,EAAa,IACbe,EACIlD,EAAO+C,UAAuC,iBAApB/C,EAAO+C,SAC3B/C,EAAO+C,SACP,GACVI,EAAQnD,EAAOyC,UAAY,EAC3BW,EAAO,CAAC,GAAI,IAAK,IAAK,IAAK,KAC3BC,EAAQ,GAGRrD,EAAO8C,aACPX,EAAa,KACbiB,EAAO,CAAC,QAAS,KAAM,KAAM,KAAM,OAIvC,IAAK,IAAIE,EAAI,EAAGA,GAAK,EAAGA,IAIpB,GAFU,IAANA,IAASD,EAAQ,GAAH,OAAM3B,EAAO6B,QAAQJ,GAArB,YAA+BC,EAAKE,KAElD5B,GAAUQ,EAAqBC,EAAYmB,GAAI,CAC/CD,EAAQ,GAAH,QAAO3B,EAASQ,EAAqBC,EAAYmB,IAAIC,QACtDJ,GADC,YAEAC,EAAKE,IACV,MAKR,OAAOJ,EAASG,EAGhB,OAAOxB,WAAWH,GA4Eb8B,CAAc9B,EAAQ1B,GAlEnC,SAAwBqD,EAAOrD,GAoC3B,GAAIA,EAAO4C,SAAU,CAEjB,IAAIM,EAASlD,EAAOyD,UACc,iBAArBzD,EAAOyD,UACVzD,EAAOyD,UACP,IACJ,GAGN,MAAwB,UAApBzD,EAAO4C,WAA6C,IAArB5C,EAAOyD,UAC/BJ,GA5CaI,EA+CIP,EAAPG,EAxCVK,QAFP,uGAEgC,SAAUnD,EAAOoD,EAAIC,EAAIC,EAAIC,GAE7D,IAAIC,EAAS,GACTC,EAAM,GAYV,QAXWnB,IAAPc,GAEAI,EAASJ,EAAGD,QAAQ,IAAIO,OAAO,MAAO,MAAOR,GAC7CO,EAAM,UACQnB,IAAPe,EAEPG,EAASH,EAAGF,QAAQ,IAAIO,OAAO,OAAQ,MAAOR,QAChCZ,IAAPgB,IAEPE,EAASF,EAAGH,QAAQ,IAAIO,OAAO,MAAO,MAAOR,SAEtCZ,IAAPiB,EAAkB,CAClB,IAAII,EAAkB,MAARF,GAA6B,MAAdP,EAAoB,IAAa,IAC9DM,QACWlB,IAAPiB,EACMA,EAAGJ,QAAQ,IAAIO,OAAO,SAAU,MAAOC,GACvC,GAGd,OAAOH,MA9Bf,IAA4BN,EAkD5B,OAAOJ,EAsBAc,CALPzC,EAAS1B,EAAO4C,SACVlB,EAAO0C,eAAezB,EAAQJ,GAC9B8B,SAAS3C,GAAQ4C,WAGOtE,GAIlC,SAASM,EAAWiE,GAEhB,MAAI,mBAAmBC,KAAKD,GACjB1C,WAAW0C,GAGlB,WAAWC,KAAKD,GACTF,SAASE,GAGhB,eAAeC,KAAKD,GACb,SAASC,KAAKD,GAGlBA,EAIX,SAASE,EAAgB9D,GAMrB,IALA,IAAI+D,EAAM/D,EAAQgE,UACdC,EAAOjE,EAAQkE,WACfC,EAAQnE,EAAQoE,YAChBC,EAASrE,EAAQsE,aAEdtE,EAAQuE,cAEXR,IADA/D,EAAUA,EAAQuE,cACHP,UACfC,GAAQjE,EAAQkE,WAGpB,OACIH,GAAOS,OAAOC,aACdR,GAAQO,OAAOE,aACfX,EAAMM,GAAUG,OAAOC,YAAcD,OAAOG,aAC5CV,EAAOE,GAASK,OAAOE,YAAcF,OAAOI,WAKpD,SAASC,IACL,MACI,yBAA0BL,QAC1B,8BAA+BA,QAC/B,sBAAuBA,OAAOM,0BAA0BC,UAgIhE/F,EAAOD,QA3HP,WAAmC,IAAdiG,EAAc,uDAAJ,GACvBC,EAAU,CACV9E,MAAO,EACPD,IAAK,IACLE,SAAU,IACVC,MAAO,GACPK,MAAM,EACNU,OAAO,EACPU,SAAU,EACVoD,QAAQ,EACR/C,YAAY,EACZC,UAAU,EACVU,WAAW,EACXb,SAAU,QACVkD,SAAU,gBAEVC,EAAgBhG,EAAW4F,EAASC,GAExC,SAASI,IAEL,IAAIC,EAAWC,SAASC,iBAAiBJ,EAAcD,UAEvD,GAAwB,IAApBG,EAASG,OAKb,GAAIZ,IAAiC,CACjC,IAAIa,EAAoB,IAAIC,qBAAqBC,EAAgBC,KAAKC,MAAO,CACzEjH,KAAM,KACNkH,WAAY,OACZC,UAAW,KAGfV,EAASW,SAAQ,SAACjG,GACd0F,EAAkBQ,QAAQlG,WAG1BwE,OAAO2B,mBACPC,EAAcd,GACdd,OAAO2B,iBACH,UACA,SAAUE,GACND,EAAcd,KAElB,CAAEgB,SAAS,KAO3B,SAASF,EAAcd,GACnBA,EAASW,SAAQ,SAACjG,IAEQ,IADTuG,EAAYvG,GACdkF,QAAmBpB,EAAgB9D,IAC1C4F,EAAgB,CAAC5F,OAM7B,SAAS4F,EAAgBN,EAAUkB,GAC/BlB,EAASW,SAAQ,SAACjG,GACd,IAAIyG,EAAMzG,EAAQ0G,QAAU1G,EACxB2G,EAAgBJ,EAAYE,GAGhC,GAAIE,EAAcvG,UAAY,EAC1B,OAAQqG,EAAIjG,UAAYC,EAAakG,EAAczG,IAAKyG,GAG5D,IACMH,IAAa1C,EAAgB9D,IAC9BwG,GAAYxG,EAAQ4G,kBAAoB,GAC3C,CACE,IAAIlE,EACAiE,EAAcxG,MAAQwG,EAAczG,IAC9ByG,EAAczG,IACdyG,EAAcxG,MACxB,OAAQsG,EAAIjG,UAAYC,EAAaiC,EAAOiE,GAIhDtF,YAAW,WACP,OAAOtB,EAAa0G,EAAKE,KAC1BA,EAActG,UAKzB,SAASkG,EAAYvG,GAGjB,IAAIV,EAAa8F,EAGbyB,EAAe,GAAGC,OAAOC,KAAK/G,EAAQgH,YAAY,SAAUC,GAC5D,MAAO,qBAAqBpD,KAAKoD,EAAKC,SAkB1C,OAAO9H,EAboB,GAAvByH,EAAapB,OACP5F,OAAOC,OAAP,MAAAD,OAAM,CACF,IADE,SAECgH,EAAaM,KAAI,YAAqB,IAAlBD,EAAkB,EAAlBA,KAAMxE,EAAY,EAAZA,MAIzB,YAHUwE,EAAKnE,QAAQ,oBAAqB,IAAIqE,cACtCzH,EAAW+C,UAK7B,GAGuBpD,GAIrC+F,OChYAgC,EAA2B,GCE3BC,EDCJ,SAASC,EAAoBC,GAE5B,IAAIC,EAAeJ,EAAyBG,GAC5C,QAAqBtF,IAAjBuF,EACH,OAAOA,EAAa1I,QAGrB,IAAIC,EAASqI,EAAyBG,GAAY,CAGjDzI,QAAS,IAOV,OAHA2I,EAAoBF,GAAUxI,EAAQA,EAAOD,QAASwI,GAG/CvI,EAAOD,QClBWwI,CAAoB,K,EHO9C,I,EETIF,ECEAC", "sources": ["webpack://PureCounter/webpack/universalModuleDefinition", "webpack://PureCounter/./js/purecounter_vanilla.js", "webpack://PureCounter/webpack/bootstrap", "webpack://PureCounter/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"PureCounter\"] = factory();\n\telse\n\t\troot[\"PureCounter\"] = factory();\n})(self, function() {\nreturn ", "/** This function is for create and merge configuration */\r\nfunction setOptions(config, baseConfig = {}) {\r\n    // Create new Config object;\r\n    var newConfig = {};\r\n\r\n    // Loop config items to set it value into newConfig\r\n    for (var key in config) {\r\n        // if baseConfig is set, only accept the baseconfig property\r\n        if (baseConfig != {} && !baseConfig.hasOwnProperty(key)) continue;\r\n        // parse the config value\r\n        var val = parseValue(config[key]);\r\n        // set the newConfig property value\r\n        newConfig[key] = val;\r\n        // Exclusive for 'duration' or 'pulse' property, recheck the value\r\n        // If it's not a boolean, just set it to milisecond unit\r\n        if (key.match(/duration|pulse/)) {\r\n            newConfig[key] = typeof val != \"boolean\" ? val * 1000 : val;\r\n        }\r\n    }\r\n\r\n    // Finally, we can just merge the baseConfig (if any), with newConfig.\r\n    return Object.assign({}, baseConfig, newConfig);\r\n}\r\n\r\n/** This is the the counter method */\r\nfunction startCounter(element, config) {\r\n    // First, get the increments step\r\n    var incrementsPerStep =\r\n        (config.end - config.start) / (config.duration / config.delay);\r\n    // Next, set the counter mode (Increment or Decrement)\r\n    var countMode = \"inc\";\r\n\r\n    // Set mode to 'decrement' and 'increment step' to minus if start is larger than end\r\n    if (config.start > config.end) {\r\n        countMode = \"dec\";\r\n        incrementsPerStep *= -1;\r\n    }\r\n\r\n    // Next, determine the starting value\r\n    var currentCount = parseValue(config.start);\r\n    // And then print it's value to the page\r\n    element.innerHTML = formatNumber(currentCount, config);\r\n\r\n    // If the config 'once' is true, then set the 'duration' to 0\r\n    if (config.once === true) {\r\n        element.setAttribute(\"data-purecounter-duration\", 0);\r\n    }\r\n\r\n    // Now, start counting with counterWorker using Interval method based on delay\r\n    var counterWorker = setInterval(() => {\r\n        // First, determine the next value base on current value, increment value, and count mode\r\n        var nextNum = nextNumber(currentCount, incrementsPerStep, countMode);\r\n        // Next, print that value to the page\r\n        element.innerHTML = formatNumber(nextNum, config);\r\n        // Now set that value to the current value, because it's already printed\r\n        currentCount = nextNum;\r\n\r\n        // If the value is larger or less than the 'end' (base on mode), then  print the end value and stop the Interval\r\n        if (\r\n            (currentCount >= config.end && countMode == \"inc\") ||\r\n            (currentCount <= config.end && countMode == \"dec\")\r\n        ) {\r\n            element.innerHTML = formatNumber(config.end, config);\r\n            // If 'pulse' is set ignore the 'once' config\r\n            if (config.pulse) {\r\n                // First set the 'duration' to zero\r\n                element.setAttribute(\"data-purecounter-duration\", 0);\r\n                // Next, use timeout to reset it duration back based on 'pulse' config\r\n                setTimeout(() => {\r\n                    element.setAttribute(\r\n                        \"data-purecounter-duration\",\r\n                        config.duration / 1000\r\n                    );\r\n                }, config.pulse);\r\n            }\r\n            clearInterval(counterWorker);\r\n        }\r\n    }, config.delay);\r\n}\r\n\r\n/** This function is to get the next number */\r\nfunction nextNumber(number, steps, mode = \"inc\") {\r\n    // First, get the exact value from the number and step (int or float)\r\n    number = parseValue(number);\r\n    steps = parseValue(steps);\r\n\r\n    // Last, get the next number based on current number, increment step, and count mode\r\n    // Always return it as float\r\n    return parseFloat(mode === \"inc\" ? number + steps : number - steps);\r\n}\r\n\r\n/** This function is to convert number into currency format */\r\nfunction convertNumber(number, config) {\r\n    /** Use converter if filesizing or currency is on */\r\n    if (config.filesizing || config.currency) {\r\n        number = Math.abs(Number(number)); // Get the absolute value of number\r\n\r\n        var baseNumber = 1000, // Base multiplying treshold\r\n            symbol =\r\n                config.currency && typeof config.currency === \"string\"\r\n                    ? config.currency\r\n                    : \"\", // Set the Currency Symbol (if any)\r\n            limit = config.decimals || 1, // Set the decimal limit (default is 1)\r\n            unit = [\"\", \"K\", \"M\", \"B\", \"T\"], // Number unit based exponent threshold\r\n            value = \"\"; // Define value variable\r\n\r\n        /** Changes base number and its unit for filesizing */\r\n        if (config.filesizing) {\r\n            baseNumber = 1024; // Use 1024 instead of 1000\r\n            unit = [\"bytes\", \"KB\", \"MB\", \"GB\", \"TB\"]; // Change to 'bytes' unit\r\n        }\r\n\r\n        /** Set value based on the threshold */\r\n        for (var i = 4; i >= 0; i--) {\r\n            // If the exponent is 0\r\n            if (i === 0) value = `${number.toFixed(limit)} ${unit[i]}`;\r\n            // If the exponent is above zero\r\n            if (number >= getFilesizeThreshold(baseNumber, i)) {\r\n                value = `${(number / getFilesizeThreshold(baseNumber, i)).toFixed(\r\n                    limit\r\n                )} ${unit[i]}`;\r\n                break;\r\n            }\r\n        }\r\n\r\n        // Apply symbol before the value and return it as string\r\n        return symbol + value;\r\n    } else {\r\n        /** Return its value as float if not using filesizing or currency*/\r\n        return parseFloat(number);\r\n    }\r\n}\r\n\r\n/** This function will get the given base.  */\r\nfunction getFilesizeThreshold(baseNumber, index) {\r\n    return Math.pow(baseNumber, index);\r\n}\r\n\r\n/** This function is to get the last formated number */\r\nfunction applySeparator(value, config) {\r\n    // Get replaced value based on it's separator/symbol.\r\n    function replacedValue(val, separator) {\r\n        // Well this is my regExp for detecting the Thausands Separator\r\n        // I use 3 groups to determine it's separator\r\n        // THen the group 4 is to get the decimals value\r\n        var separatorRegExp =\r\n            /^(?:(\\d{1,3},(?:\\d{1,3},?)*)|(\\d{1,3}\\.(?:\\d{1,3}\\.?)*)|(\\d{1,3}(?:\\s\\d{1,3})*))([\\.,]?\\d{0,2}?)$/gi;\r\n\r\n        return val.replace(separatorRegExp, function (match, g1, g2, g3, g4) {\r\n            // set initial result value\r\n            var result = \"\",\r\n                sep = \"\";\r\n            if (g1 !== undefined) {\r\n                // Group 1 is using comma as thausands separator, and period as decimal separator\r\n                result = g1.replace(new RegExp(/,/gi, \"gi\"), separator);\r\n                sep = \",\";\r\n            } else if (g2 !== undefined) {\r\n                // Group 2 is using period as thausands separator, and comma as decimal separator\r\n                result = g2.replace(new RegExp(/\\./gi, \"gi\"), separator);\r\n            } else if (g3 !== undefined) {\r\n                // Group 3 is using space as thausands separator, and comma as decimal separator\r\n                result = g3.replace(new RegExp(/ /gi, \"gi\"), separator);\r\n            }\r\n            if (g4 !== undefined) {\r\n                var decimal = sep !== \",\" ? (separator !== \",\" ? \",\" : \".\") : \".\";\r\n                result +=\r\n                    g4 !== undefined\r\n                        ? g4.replace(new RegExp(/\\.|,/gi, \"gi\"), decimal)\r\n                        : \"\";\r\n            }\r\n            // Returning result value;\r\n            return result;\r\n        });\r\n    }\r\n    // If config formater is not false, then apply separator\r\n    if (config.formater) {\r\n        // Now get the separator symbol\r\n        var symbol = config.separator // if config separator is setted\r\n            ? typeof config.separator === \"string\" // Check the type of value\r\n                ? config.separator // If it's type is string, then apply it's value\r\n                : \",\" // If it's not string (boolean), then apply comma as default separator\r\n            : \"\";\r\n        // Special exception when locale is not 'en-US' but separator value is 'true'\r\n        // Use it's default locale thausands separator.\r\n        if (config.formater !== \"en-US\" && config.separator === true) {\r\n            return value;\r\n        }\r\n        // Return the replaced Value based on it's symbol\r\n        return replacedValue(value, symbol);\r\n    }\r\n    // If config formater is false, then return it's default value\r\n    return value;\r\n}\r\n\r\n/** This function is to get formated number to be printed in the page */\r\nfunction formatNumber(number, config) {\r\n    // This is the configuration for 'toLocaleString' method\r\n    var strConfig = {\r\n        minimumFractionDigits: config.decimals,\r\n        maximumFractionDigits: config.decimals,\r\n    };\r\n    // Get locale from config formater\r\n    var locale = typeof config.formater === \"string\" ? config.formater : undefined;\r\n\r\n    // Set and convert the number base on its config.\r\n    number = convertNumber(number, config);\r\n\r\n    // Now format the number to string base on it's locale\r\n    number = config.formater\r\n        ? number.toLocaleString(locale, strConfig)\r\n        : parseInt(number).toString();\r\n\r\n    // Last, apply the number separator using number as string\r\n    return applySeparator(number, config);\r\n}\r\n\r\n/** This function is to get the parsed value */\r\nfunction parseValue(data) {\r\n    // If number with dot (.), will be parsed as float\r\n    if (/^[0-9]+\\.[0-9]+$/.test(data)) {\r\n        return parseFloat(data);\r\n    }\r\n    // If just number, will be parsed as integer\r\n    if (/^[0-9]+$/.test(data)) {\r\n        return parseInt(data);\r\n    }\r\n    // If it's boolean string, will be parsed as boolean\r\n    if (/^true|false/i.test(data)) {\r\n        return /^true/i.test(data);\r\n    }\r\n    // Return it's value as default\r\n    return data;\r\n}\r\n\r\n// This function is to detect the element is in view or not.\r\nfunction elementIsInView(element) {\r\n    var top = element.offsetTop;\r\n    var left = element.offsetLeft;\r\n    var width = element.offsetWidth;\r\n    var height = element.offsetHeight;\r\n\r\n    while (element.offsetParent) {\r\n        element = element.offsetParent;\r\n        top += element.offsetTop;\r\n        left += element.offsetLeft;\r\n    }\r\n\r\n    return (\r\n        top >= window.pageYOffset &&\r\n        left >= window.pageXOffset &&\r\n        top + height <= window.pageYOffset + window.innerHeight &&\r\n        left + width <= window.pageXOffset + window.innerWidth\r\n    );\r\n}\r\n\r\n/** Just some condition to check browser Intersection Support */\r\nfunction intersectionListenerSupported() {\r\n    return (\r\n        \"IntersectionObserver\" in window &&\r\n        \"IntersectionObserverEntry\" in window &&\r\n        \"intersectionRatio\" in window.IntersectionObserverEntry.prototype\r\n    );\r\n}\r\n\r\n/** Initialize PureCounter */\r\nfunction PureCounter(options = {}) {\r\n    var configs = {\r\n        start: 0, // Starting number [uint]\r\n        end: 100, // End number [uint]\r\n        duration: 2000, // Count duration [milisecond]\r\n        delay: 10, // Count delay [milisecond]\r\n        once: true, // Counting at once or recount when scroll [boolean]\r\n        pulse: false, // Pulse count for certain time [boolean|milisecond]\r\n        decimals: 0, // Decimal places [uint]\r\n        legacy: true, // If this is true it will use the scroll event listener on browsers\r\n        filesizing: false, // Is it for filesize?\r\n        currency: false, // Is it for currency? Use it for set the symbol too [boolean|char|string]\r\n        separator: false, // Do you want to use thausands separator? use it for set the symbol too [boolean|char|string]\r\n        formater: \"us-US\", // Number toLocaleString locale/formater, by default is \"en-US\" [string|boolean:false]\r\n        selector: \".purecounter\", // HTML query selector for spesific element\r\n    };\r\n    var configOptions = setOptions(options, configs);\r\n\r\n    function registerEventListeners() {\r\n        /** Get all elements with class 'purecounter' */\r\n        var elements = document.querySelectorAll(configOptions.selector);\r\n        /** Return if no elements */\r\n        if (elements.length === 0) {\r\n            return;\r\n        }\r\n\r\n        /** Run animateElements base on Intersection Support */\r\n        if (intersectionListenerSupported()) {\r\n            var intersectObserver = new IntersectionObserver(animateElements.bind(this), {\r\n                root: null,\r\n                rootMargin: \"20px\",\r\n                threshold: 0.5,\r\n            });\r\n\r\n            elements.forEach((element) => {\r\n                intersectObserver.observe(element);\r\n            });\r\n        } else {\r\n            if (window.addEventListener) {\r\n                animateLegacy(elements);\r\n                window.addEventListener(\r\n                    \"scroll\",\r\n                    function (e) {\r\n                        animateLegacy(elements);\r\n                    },\r\n                    { passive: true }\r\n                );\r\n            }\r\n        }\r\n    }\r\n\r\n    /** This legacy to make Purecounter use very lightweight & fast */\r\n    function animateLegacy(elements) {\r\n        elements.forEach((element) => {\r\n            var config = parseConfig(element);\r\n            if (config.legacy === true && elementIsInView(element)) {\r\n                animateElements([element]);\r\n            }\r\n        });\r\n    }\r\n\r\n    /** Main Element Count Animation */\r\n    function animateElements(elements, observer) {\r\n        elements.forEach((element) => {\r\n            var elm = element.target || element; // Just make sure which element will be used\r\n            var elementConfig = parseConfig(elm); // Get config value on that element\r\n\r\n            // If duration is less than or equal zero, just format the 'end' value\r\n            if (elementConfig.duration <= 0) {\r\n                return (elm.innerHTML = formatNumber(elementConfig.end, elementConfig));\r\n            }\r\n\r\n            if (\r\n                (!observer && !elementIsInView(element)) ||\r\n                (observer && element.intersectionRatio < 0.5)\r\n            ) {\r\n                var value =\r\n                    elementConfig.start > elementConfig.end\r\n                        ? elementConfig.end\r\n                        : elementConfig.start;\r\n                return (elm.innerHTML = formatNumber(value, elementConfig));\r\n            }\r\n\r\n            // If duration is more than 0, then start the counter\r\n            setTimeout(() => {\r\n                return startCounter(elm, elementConfig);\r\n            }, elementConfig.delay);\r\n        });\r\n    }\r\n\r\n    /** This function is to generate the element Config */\r\n    function parseConfig(element) {\r\n        // First, we need to declare the base Config\r\n        // This config will be used if the element doesn't have config\r\n        var baseConfig = configOptions;\r\n\r\n        // Next, get all 'data-precounter-*' attributes value. Store to array\r\n        var configValues = [].filter.call(element.attributes, function (attr) {\r\n            return /^data-purecounter-/.test(attr.name);\r\n        });\r\n\r\n        // Now, we create element config as an object\r\n        var elementConfig =\r\n            configValues.length != 0\r\n                ? Object.assign(\r\n                      {},\r\n                      ...configValues.map(({ name, value }) => {\r\n                          var key = name.replace(\"data-purecounter-\", \"\").toLowerCase(),\r\n                              val = parseValue(value);\r\n\r\n                          return { [key]: val };\r\n                      })\r\n                  )\r\n                : {};\r\n\r\n        // Last setOptions and return\r\n        return setOptions(elementConfig, baseConfig);\r\n    }\r\n\r\n    /** Run the initial function */\r\n    registerEventListeners();\r\n}\r\n\r\nmodule.exports = PureCounter;\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(638);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "setOptions", "config", "baseConfig", "newConfig", "key", "hasOwnProperty", "val", "parseValue", "match", "Object", "assign", "startCounter", "element", "incrementsPerStep", "end", "start", "duration", "delay", "countMode", "currentCount", "innerHTML", "formatNumber", "once", "setAttribute", "counterWorker", "setInterval", "nextNum", "number", "steps", "mode", "parseFloat", "nextNumber", "pulse", "setTimeout", "clearInterval", "getFilesizeThreshold", "baseNumber", "index", "Math", "pow", "strConfig", "minimumFractionDigits", "decimals", "maximumFractionDigits", "locale", "formater", "undefined", "filesizing", "currency", "abs", "Number", "symbol", "limit", "unit", "value", "i", "toFixed", "convertNumber", "separator", "replace", "g1", "g2", "g3", "g4", "result", "sep", "RegExp", "decimal", "applySeparator", "toLocaleString", "parseInt", "toString", "data", "test", "elementIsInView", "top", "offsetTop", "left", "offsetLeft", "width", "offsetWidth", "height", "offsetHeight", "offsetParent", "window", "pageYOffset", "pageXOffset", "innerHeight", "innerWidth", "intersectionListenerSupported", "IntersectionObserverEntry", "prototype", "options", "configs", "legacy", "selector", "configOptions", "registerEventListeners", "elements", "document", "querySelectorAll", "length", "intersectObserver", "IntersectionObserver", "animateElements", "bind", "this", "rootMargin", "threshold", "for<PERSON>ach", "observe", "addEventListener", "animateLegacy", "e", "passive", "parseConfig", "observer", "elm", "target", "elementConfig", "intersectionRatio", "config<PERSON><PERSON><PERSON>", "filter", "call", "attributes", "attr", "name", "map", "toLowerCase", "__webpack_module_cache__", "__webpack_exports__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}