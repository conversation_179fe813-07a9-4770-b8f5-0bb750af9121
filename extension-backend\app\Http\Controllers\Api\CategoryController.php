<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Mendapatkan daftar semua kategori yang aktif
     */
    public function index(Request $request)
    {
        try {
            $categories = Category::where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get(['id', 'name', 'slug', 'icon', 'color', 'description']);

            return response()->json([
                'success' => true,
                'message' => 'Kategori berhasil dimuat',
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat kategori',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mendapatkan detail kategori berdasarkan ID atau slug
     */
    public function show($identifier)
    {
        try {
            // Coba cari berdasarkan ID terlebih dahulu, kemudian slug
            $category = Category::where('id', $identifier)
                ->orWhere('slug', $identifier)
                ->where('is_active', true)
                ->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kategori tidak ditemukan'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Detail kategori berhasil dimuat',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat detail kategori',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mendapatkan situs berdasarkan kategori
     */
    public function getSites($identifier, Request $request)
    {
        try {
            // Cari kategori berdasarkan ID atau slug
            $category = Category::where('id', $identifier)
                ->orWhere('slug', $identifier)
                ->where('is_active', true)
                ->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kategori tidak ditemukan'
                ], 404);
            }

            // Ambil situs berdasarkan kategori dengan mempertimbangkan role pengguna
            $user = $request->user();
            $query = $category->sites()->where('is_active', true);

            // Filter berdasarkan visibility dan role pengguna
            if ($user->role !== 'elite') {
                $query->where(function ($q) use ($user) {
                    $q->where('visibility', 'public')
                      ->orWhere(function ($subQ) use ($user) {
                          $subQ->where('visibility', 'premium')
                               ->where(function ($roleQ) use ($user) {
                                   $roleQ->where(function ($premiumQ) use ($user) {
                                       $premiumQ->whereIn('category', ['premium'])
                                                ->where(function ($userRoleQ) use ($user) {
                                                    $userRoleQ->where('role', 'premium')
                                                             ->orWhere('role', 'elite');
                                                });
                                   });
                               });
                      });
                });
            }

            $sites = $query->with('categoryModel')
                          ->orderBy('name')
                          ->get();

            return response()->json([
                'success' => true,
                'message' => 'Situs berdasarkan kategori berhasil dimuat',
                'data' => [
                    'category' => $category,
                    'sites' => $sites
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat situs berdasarkan kategori',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}