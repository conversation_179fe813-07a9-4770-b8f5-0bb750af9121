<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sites', function (Blueprint $table) {
            // Add logo field
            $table->string('logo_path')->nullable()->after('thumbnail');
            
            // Add JSON files storage field (array of file paths)
            $table->json('json_files')->nullable()->after('cookies_json');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sites', function (Blueprint $table) {
            $table->dropColumn(['logo_path', 'json_files']);
        });
    }
};
