<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReportResource\Pages;
use App\Filament\Resources\ReportResource\RelationManagers;
use App\Models\Report;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';

    protected static ?string $navigationLabel = 'Laporan Masalah';

    protected static ?string $modelLabel = 'Laporan';

    protected static ?string $pluralModelLabel = 'Laporan Masalah';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('site_id')
                    ->label('ID Situs')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('site_name')
                    ->label('Nama Situs')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('report_type')
                    ->label('Jenis Masalah')
                    ->options(Report::REPORT_TYPES)
                    ->required(),
                Forms\Components\Textarea::make('custom_message')
                    ->label('Pesan Kustom')
                    ->rows(3)
                    ->columnSpanFull()
                    ->visible(fn(callable $get) => $get('report_type') === 'other'),
                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options(Report::STATUSES)
                    ->required()
                    ->default(Report::STATUS_PENDING),
                Forms\Components\Textarea::make('user_agent')
                    ->label('User Agent')
                    ->rows(3)
                    ->columnSpanFull(),
                Forms\Components\DateTimePicker::make('reported_at')
                    ->label('Waktu Laporan')
                    ->required(),
                Forms\Components\Textarea::make('admin_notes')
                    ->label('Catatan Admin')
                    ->rows(4)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('resolved_by')
                    ->label('Diselesaikan Oleh')
                    ->numeric(),
                Forms\Components\DateTimePicker::make('resolved_at')
                    ->label('Waktu Penyelesaian'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('site_name')
                    ->label('Nama Situs')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('report_type')
                    ->label('Jenis Masalah')
                    ->formatStateUsing(fn(string $state): string => Report::REPORT_TYPES[$state] ?? $state)
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'login_issue', 'site_down', 'security_issue' => 'danger',
                        'subscription_expired', 'slow_loading', 'broken_features' => 'warning',
                        'content_issue', 'other' => 'secondary',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('custom_message')
                    ->label('Pesan Kustom')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) > 50) {
                            return $state;
                        }
                        return null;
                    })
                    ->visible(fn($record) => !empty($record->custom_message)),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn(string $state): string => Report::STATUSES[$state] ?? $state)
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        Report::STATUS_PENDING => 'warning',
                        Report::STATUS_INVESTIGATING => 'info',
                        Report::STATUS_RESOLVED => 'success',
                        Report::STATUS_DISMISSED => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('reported_at')
                    ->label('Waktu Laporan')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('resolved_at')
                    ->label('Diselesaikan')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options(Report::STATUSES),
                SelectFilter::make('report_type')
                    ->label('Jenis Masalah')
                    ->options(Report::REPORT_TYPES),
            ])
            ->actions([
                Action::make('investigate')
                    ->label('Selidiki')
                    ->icon('heroicon-o-magnifying-glass')
                    ->color('primary')
                    ->visible(fn(Report $record): bool => $record->status === Report::STATUS_PENDING)
                    ->action(function (Report $record): void {
                        $record->update(['status' => Report::STATUS_INVESTIGATING]);
                        Notification::make()
                            ->title('Status diperbarui')
                            ->body('Laporan sedang diselidiki')
                            ->success()
                            ->send();
                    }),
                Action::make('resolve')
                    ->label('Selesaikan')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn(Report $record): bool => in_array($record->status, [Report::STATUS_PENDING, Report::STATUS_INVESTIGATING]))
                    ->form([
                        Textarea::make('admin_notes')
                            ->label('Catatan Penyelesaian')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (array $data, Report $record): void {
                        $record->markAsResolved(Auth::id(), $data['admin_notes']);
                        Notification::make()
                            ->title('Laporan diselesaikan')
                            ->body('Laporan telah ditandai sebagai selesai')
                            ->success()
                            ->send();
                    }),
                Action::make('dismiss')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn(Report $record): bool => in_array($record->status, [Report::STATUS_PENDING, Report::STATUS_INVESTIGATING]))
                    ->form([
                        Textarea::make('admin_notes')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (array $data, Report $record): void {
                        $record->markAsDismissed(Auth::id(), $data['admin_notes']);
                        Notification::make()
                            ->title('Laporan ditolak')
                            ->body('Laporan telah ditolak')
                            ->warning()
                            ->send();
                    }),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
            'create' => Pages\CreateReport::route('/create'),
            'view' => Pages\ViewReport::route('/{record}'),
            'edit' => Pages\EditReport::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', Report::STATUS_PENDING)->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getModel()::where('status', Report::STATUS_PENDING)->count() > 0 ? 'warning' : null;
    }
}
