<?php

namespace App\Filament\Resources\ApiV1\AnyoneResource\Pages;

use App\Filament\Resources\ApiV1\AnyoneResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAnyones extends ListRecords
{
    protected static string $resource = AnyoneResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
