<?php

namespace App\Filament\Resources\ApiV2;

use App\Filament\Resources\ApiV2\ImportantInfoResource\Pages;
use App\Models\ImportantInfo;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;

class ImportantInfoResource extends Resource
{
    protected static ?string $model = ImportantInfo::class;

    protected static ?string $navigationIcon = 'heroicon-o-information-circle';
    
    protected static ?string $navigationLabel = 'Informasi Penting';
    
    protected static ?string $modelLabel = 'Informasi Penting';
    
    protected static ?string $pluralModelLabel = 'Informasi Penting';
    
    protected static ?string $navigationGroup = 'API V2';
    
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        TextInput::make('title')
                            ->label('Judul')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Textarea::make('content')
                            ->label('Konten')
                            ->required()
                            ->rows(4)
                            ->columnSpanFull(),

                        Select::make('type')
                            ->label('Tipe')
                            ->options([
                                'info' => 'Informasi',
                                'warning' => 'Peringatan',
                                'error' => 'Error',
                                'success' => 'Sukses',
                            ])
                            ->default('info')
                            ->required(),

                        TextInput::make('priority')
                            ->label('Prioritas')
                            ->numeric()
                            ->default(0)
                            ->helperText('Semakin tinggi angka, semakin prioritas ditampilkan')
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Pengaturan Tampilan')
                    ->schema([
                        Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true)
                            ->helperText('Centang untuk menampilkan informasi ini'),

                        DateTimePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->helperText('Kosongkan jika ingin langsung aktif'),

                        DateTimePicker::make('end_date')
                            ->label('Tanggal Berakhir')
                            ->helperText('Kosongkan jika tidak ada batas waktu'),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label('Judul')
                    ->searchable()
                    ->sortable(),

                BadgeColumn::make('type')
                    ->label('Tipe')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'info' => 'Informasi',
                        'warning' => 'Peringatan',
                        'error' => 'Error',
                        'success' => 'Sukses',
                        default => $state,
                    })
                    ->colors([
                        'info' => 'info',
                        'warning' => 'warning',
                        'danger' => 'error',
                        'success' => 'success',
                    ]),

                TextColumn::make('priority')
                    ->label('Prioritas')
                    ->sortable(),

                BooleanColumn::make('is_active')
                    ->label('Status')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                TextColumn::make('start_date')
                    ->label('Mulai')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->placeholder('Langsung aktif'),

                TextColumn::make('end_date')
                    ->label('Berakhir')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->placeholder('Tidak terbatas'),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label('Tipe')
                    ->options([
                        'announcement' => 'Pengumuman',
                        'warning' => 'Peringatan',
                        'info' => 'Informasi',
                        'success' => 'Sukses',
                    ]),

                Filter::make('is_active')
                    ->label('Hanya yang Aktif')
                    ->query(fn(Builder $query): Builder => $query->where('is_active', true)),

                Filter::make('currently_valid')
                    ->label('Sedang Berlaku')
                    ->query(fn(Builder $query): Builder => $query->currentlyValid()),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('priority', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListImportantInfos::route('/'),
            'create' => Pages\CreateImportantInfo::route('/create'),
            'view' => Pages\ViewImportantInfo::route('/{record}'),
            'edit' => Pages\EditImportantInfo::route('/{record}/edit'),
        ];
    }
}
