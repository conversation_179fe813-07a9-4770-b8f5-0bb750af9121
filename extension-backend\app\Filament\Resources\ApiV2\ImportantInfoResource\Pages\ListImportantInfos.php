<?php

namespace App\Filament\Resources\ApiV2\ImportantInfoResource\Pages;

use App\Filament\Resources\ApiV2\ImportantInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\CreateAction;

class ListImportantInfos extends ListRecords
{
    protected static string $resource = ImportantInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Tambah Informasi Baru'),
        ];
    }
    
    public function getTitle(): string
    {
        return 'Kelola Informasi Penting';
    }
    
    protected function getHeaderWidgets(): array
    {
        return [
            // Bisa ditambahkan widget statistik di sini
        ];
    }
}