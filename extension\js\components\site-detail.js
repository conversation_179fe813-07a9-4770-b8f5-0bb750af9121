/**
 * Site Detail Component
 * Displays detailed information about a site and its multiple cookies
 */

class SiteDetailComponent {
    constructor() {
        this.container = null;
        this.site = null;
        this.cookies = [];
        this.onClose = null;
        this.onCookieSelect = null;
    }

    /**
     * Initialize the component
     */
    init() {
        // Create container if it doesn't exist
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'site-detail-container';
            this.container.style.display = 'none';
            document.body.appendChild(this.container);

            // Add styles
            const style = document.createElement('style');
            style.textContent = `
                .site-detail-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
                    backdrop-filter: blur(15px);
                    z-index: 1000;
                    display: flex;
                    flex-direction: column;
                    color: white;
                    transition: opacity 0.3s ease-in-out;
                    opacity: 0;
                    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                    overflow-x: hidden;
                    width: 100%;
                    box-sizing: border-box;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                
                .site-detail-container.visible {
                    opacity: 1;
                }
                
                .site-detail-header {
                    display: flex;
                    align-items: center;
                    padding: 16px;
                    background: rgba(0, 0, 0, 0.4);
                    border-radius: 16px 16px 0 0;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-bottom: none;
                    gap: 12px;
                    margin: 16px 16px 0 16px;
                    backdrop-filter: blur(15px);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    width: calc(100% - 32px);
                    box-sizing: border-box;
                    overflow: hidden;
                }
                
                .site-detail-back {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: white;
                    cursor: pointer;
                    font-size: 16px;
                    padding: 6px 10px;
                    border-radius: 6px;
                    transition: all 0.2s ease;
                    flex-shrink: 0;
                }
                
                .site-detail-back:hover {
                    background: rgba(255, 255, 255, 0.2);
                    transform: scale(1.05);
                }
                
                .site-detail-logo {
                    width: 46px;
                    height: 46px;
                    border-radius: 10px;
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    overflow: hidden;
                    font-size: 22px;
                    font-weight: bold;
                    color: rgba(255, 255, 255, 0.9);
                }
                
                .site-detail-logo img {
                    width: 90%;
                    height: 90%;
                    object-fit: contain;
                    border-radius: 8px;
                }
                
                .site-detail-title {
                    font-size: 18px;
                    font-weight: 700;
                    flex: 1;
                    background: linear-gradient(45deg, #fff, #e0e7ff);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }
                
                .site-detail-content {
                    flex: 1;
                    overflow-y: auto;
                    overflow-x: hidden;
                    padding: 0 16px 16px 16px;
                    background: rgba(0, 0, 0, 0.4);
                    border-radius: 0 0 16px 16px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-top: none;
                    margin: 0 16px 16px 16px;
                    backdrop-filter: blur(15px);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    position: relative;
                    margin-bottom: 160px; /* Further reduced space for fixed description */
                    box-sizing: border-box;
                    width: calc(100% - 32px);
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                
                .site-detail-content::-webkit-scrollbar {
                    width: 8px;
                }
                
                .site-detail-content::-webkit-scrollbar-track {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 4px;
                }
                
                .site-detail-content::-webkit-scrollbar-thumb {
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
                    border-radius: 4px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }
                
                .site-detail-content::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2));
                }
                
                .site-description {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 20px;
                    margin-top: 20px;
                    font-size: 14px;
                    line-height: 1.6;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(8px);
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                    // width: 100%;
                    box-sizing: border-box;
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                }
                
                .site-description h3 {
                    margin: 0 0 12px 0;
                    font-size: 18px;
                    font-weight: 700;
                    color: rgba(255, 255, 255, 0.95);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    background: linear-gradient(45deg, #fff, #e0e7ff);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }
                
                .site-description p {
                    margin: 0;
                    color: rgba(255, 255, 255, 0.8);
                    text-align: justify;
                    white-space: normal;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    hyphens: auto;
                    max-width: 100%;
                }
                
                .cookie-list {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 12px;
                    margin: 15px 0;
                    padding: 0;
                    width: 100%;
                    box-sizing: border-box;
                    overflow-x: hidden;
                    overflow-y: auto;
                }
                
                .cookie-card {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 14px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(8px);
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                    height: 100px;
                    justify-content: center;
                    box-sizing: border-box;
                    width: 100%;
                    // max-width: 100%;
                }
                
                .cookie-card:hover {
                    background: rgba(255, 255, 255, 0.2);
                    transform: translateY(-3px);
                    box-shadow: 0 10px 28px rgba(0, 0, 0, 0.25);
                    border-color: rgba(255, 255, 255, 0.4);
                }
                
                .cookie-card:active {
                    transform: translateY(0);
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                    transition: all 0.1s ease;
                }
                
                .cookie-logo {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 20px;
                    margin-bottom: 10px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    overflow: hidden;
                    font-weight: bold;
                    color: rgba(255, 255, 255, 0.9);
                }
                
                .cookie-logo img {
                    width: 90%;
                    height: 90%;
                    object-fit: contain;
                    border-radius: 8px;
                }
                
                .site-description {
                    position: fixed;
                    bottom: 16px;
                    left: 16px;
                    right: 16px;
                    background: rgba(0, 0, 0, 0.85);
                    border-radius: 16px;
                    padding: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    backdrop-filter: blur(20px);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
                    z-index: 1001;
                    max-height: 160px;
                    overflow-y: auto;
                    transition: all 0.3s ease;
                }
                
                .site-description h3 {
                    margin: 0 0 8px 0;
                    font-size: 15px;
                    font-weight: 600;
                    color: rgba(255, 255, 255, 0.95);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }
                
                .site-description p {
                    margin: 0;
                    color: rgba(255, 255, 255, 0.9);
                    text-align: justify;
                    line-height: 1.5;
                    font-size: 13px;
                    letter-spacing: 0.2px;
                }
                
                .site-description::-webkit-scrollbar {
                    width: 6px;
                }
                
                .site-description::-webkit-scrollbar-track {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 3px;
                }
                
                .site-description::-webkit-scrollbar-thumb {
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
                    border-radius: 3px;
                }
                
                .site-description::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2));
                }
                
                .cookie-name {
                    font-weight: 600;
                    margin-bottom: 4px;
                    font-size: 10px;
                    color: rgba(255, 255, 255, 0.95);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    line-height: 1.2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: normal;
                    width: 100%;
                    max-width: 100%;
                    letter-spacing: 0.2px;
                    box-sizing: border-box;
                    word-wrap: break-word;
                    hyphens: auto;
                }
                

            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Generate site logo using the same logic as main popup
     */
    getSiteLogo(site) {
        const API_BASE_URL = "http://127.0.0.1:8000/api/v2";
        
        // Priority 1: Use logo_path from API v2 if available
        if (site.logo_path && site.logo_path.trim() !== "") {
            // Construct full URL if logo_path is a relative path
            const logoUrl = site.logo_path.startsWith("http")
                ? site.logo_path
                : `${API_BASE_URL.replace("/api/v2", "/storage")}/${site.logo_path.replace(/^\//,"")}`;            
            return `<img src="${logoUrl}" alt="${site.name}">`;
        }
        
        // Priority 2: Use thumbnail from database if available (legacy support)
        if (site.thumbnail && site.thumbnail.trim() !== "") {
            // Construct full URL if thumbnail is a relative path
            const thumbnailUrl = site.thumbnail.startsWith("http")
                ? site.thumbnail
                : `${API_BASE_URL.replace("/api/v2", "/storage")}/${site.thumbnail.replace(/^\//,"")}`;
            
            return `<img src="${thumbnailUrl}" alt="${site.name}">`;
        }
        
        // Priority 3: Use first letter as fallback
        return this.getLetterLogo(site.name);
    }
    
    /**
     * Generate letter logo when no image is available
     */
    getLetterLogo(siteName) {
        const firstLetter = siteName.charAt(0).toUpperCase();
        return `<div style="font-size: 22px; font-weight: bold; color: rgba(255,255,255,0.9); text-shadow: 0 2px 4px rgba(0,0,0,0.3);">${firstLetter}</div>`;
    }

    /**
     * Show site detail
     * @param {Object} site - The site object
     * @param {Array} cookies - Array of cookie objects
     * @param {Function} onClose - Callback when detail is closed
     * @param {Function} onCookieSelect - Callback when a cookie is selected
     */
    show(site, cookies, onClose, onCookieSelect) {
        this.site = site;
        this.cookies = cookies || [];
        this.onClose = onClose;
        this.onCookieSelect = onCookieSelect;
        
        this.init();
        
        // Generate main site logo for header
        const mainLogo = this.getSiteLogo(site);
        
        // Update content
        this.container.innerHTML = `
            <div class="site-detail-header">
                <button class="site-detail-back">←</button>
                <div class="site-detail-logo">
                    ${mainLogo}
                </div>
                <div class="site-detail-title">${site.name}</div>
            </div>
            <div class="site-detail-content">
                <div class="cookie-list">
                    ${this.cookies.map(jsonFile => `
                        <div class="cookie-card" data-index="${jsonFile.index}">
                            <div class="cookie-logo">
                                ${mainLogo}
                            </div>
                            <div class="cookie-name">${site.name} ${jsonFile.index}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            ${site.description ? `
                <div class="site-description">
                    <h3>Catatan:</h3>
                    <p>${site.description}</p>
                </div>
             ` : ''}
         `;
        
        // Show container
        this.container.style.display = 'flex';
        setTimeout(() => {
            this.container.classList.add('visible');
        }, 10);
        
        // Add event listeners
        const backButton = this.container.querySelector('.site-detail-back');
        if (backButton) {
            backButton.addEventListener('click', () => this.hide());
        }
        
        const cookieCards = this.container.querySelectorAll('.cookie-card');
        cookieCards.forEach(card => {
            card.addEventListener('click', () => {
                const index = parseInt(card.dataset.index, 10) - 1;
                if (this.onCookieSelect && this.cookies[index]) {
                    this.onCookieSelect(this.site, this.cookies[index]);
                    this.hide();
                }
            });
        });
    }

    /**
     * Hide the site detail
     */
    hide() {
        if (!this.container) return;
        
        this.container.classList.remove('visible');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            this.container.style.display = 'none';
            if (this.onClose) this.onClose();
        }, 300);
    }
}

// Create and export a singleton instance
const siteDetail = new SiteDetailComponent();
export default siteDetail;