<?php $__env->startSection('title', 'Pembayaran Berhasil - Satu Pintu'); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Header -->
        <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Pembayaran Berhasil Dikirim</h1>
            <p class="text-gray-600">Terima kasih! Pembayaran Anda sedang diproses oleh admin.</p>
        </div>

        <!-- Payment Details -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Detail Pembayaran</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">ID Pembayaran</h3>
                        <p class="mt-1 text-lg text-gray-900">#<?php echo e($payment->id); ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Jumlah</h3>
                        <p class="mt-1 text-lg text-gray-900">Rp <?php echo e(number_format($payment->amount, 0, ',', '.')); ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Metode Pembayaran</h3>
                        <p class="mt-1 text-lg text-gray-900">
                            <?php switch($payment->payment_method):
                                case ('manual_transfer'): ?>
                                    Transfer Bank
                                    <?php break; ?>
                                <?php case ('cash'): ?>
                                    Tunai
                                    <?php break; ?>
                                <?php case ('ewallet'): ?>
                                    E-Wallet
                                    <?php break; ?>
                                <?php default: ?>
                                    <?php echo e(ucfirst($payment->payment_method)); ?>

                            <?php endswitch; ?>
                        </p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Status</h3>
                        <p class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <?php echo e($payment->getStatusLabel()); ?>

                            </span>
                        </p>
                    </div>
                </div>

                <?php if($payment->notes): ?>
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-500">Catatan</h3>
                    <p class="mt-1 text-gray-900"><?php echo e($payment->notes); ?></p>
                </div>
                <?php endif; ?>

                <?php if($payment->payment_proof): ?>
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">Bukti Pembayaran</h3>
                    <img src="<?php echo e(Storage::url($payment->payment_proof)); ?>" alt="Bukti Pembayaran" class="rounded-lg max-w-full h-auto" style="max-width: 300px;">
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-blue-50 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-medium text-blue-900 mb-4">Langkah Selanjutnya</h3>
            <div class="space-y-3 text-sm text-blue-800">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p><strong>Tunggu Konfirmasi:</strong> Admin akan memverifikasi pembayaran Anda dalam 1x24 jam.</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p><strong>Kontak Admin:</strong> Jika ada pertanyaan, silakan hubungi admin via WhatsApp.</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p><strong>Cek Status:</strong> Anda dapat mengecek status pembayaran di halaman ini atau dashboard.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- WhatsApp Contact -->
        <div class="flex space-x-4 justify-center">
            <button type="button" onclick="goToDashboard()"
               class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Kembali ke Dashboard
            </button>
            <button type="button" onclick="contactAdmin()"
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91 0 1.75.46 3.45 1.32 4.95L2.03 22l5.25-1.38c1.45.79 3.08 1.21 4.76 1.21 5.46 0 9.91-4.45 9.91-9.91 0-5.46-4.45-9.91-9.91-9.91zm5.05 12.94c-.18.51-1.02.91-1.74 1.02-.46.07-1.04.1-1.66.08-1.22-.05-2.84-.57-4.13-1.85-2.33-2.3-2.76-5.2-2.78-5.39-.02-.19-.02-.36.08-.49.11-.13.29-.17.52-.17.11 0 .22.02.32.05.09.02.19.05.28.07.19.05.38.1.57.16.23.08.46.16.68.25.21.09.42.18.63.28.2.1.39.2.57.31.18.11.35.22.51.33.16.11.31.22.45.33.14.11.27.22.39.33.12.11.23.22.33.33.1.11.19.22.27.33.08.11.15.22.21.33.06.11.11.22.15.33.04.11.07.22.09.33.02.11.03.22.03.33 0 .11-.01.22-.03.33-.02.11-.05.22-.09.33-.04.11-.09.22-.15.33-.06.11-.13.22-.21.33-.08.11-.17.22-.27.33-.1.11-.21.22-.33.33-.12.11-.25.22-.39.33-.14.11-.29.22-.45.33-.16.11-.33.22-.51.33-.18.11-.37.21-.57.31-.21.1-.42.19-.63.28-.22.09-.45.17-.68.25-.19.06-.38.11-.57.16-.09.02-.19.05-.28.07-.1.03-.21.05-.32.05-.23 0-.41-.04-.52-.17-.1-.13-.1-.3-.08-.49.02-.19.45-3.09 2.78-5.39 1.29-1.28 2.91-1.8 4.13-1.85.62-.02 1.2.01 **********.11 1.56.51 1.74 **********-.25 1.03-.91 1.42-.66.39-1.49.66-2.04.71-.55.05-1.12.08-1.7.08-.58 0-1.15-.03-1.7-.08-.55-.05-1.38-.32-2.04-.71-.66-.39-1.09-.91-.91-1.42z"/>
                </svg>
                Hubungi Admin via WhatsApp
            </button>
        </div>


    </div>
</div>

<!-- SweetAlert2 Script -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Auto show success message
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            icon: 'success',
            title: 'Pembayaran Terkirim!',
            html: `
                <div class="text-center">
                    <p class="text-lg mb-4">Pembayaran Anda telah berhasil dikirim!</p>
                    <div class="bg-green-50 p-4 rounded-lg mb-4">
                        <p class="text-green-800 font-semibold">ID Pembayaran: #<?php echo e($payment->id); ?></p>
                        <p class="text-green-600 text-sm">Admin akan memverifikasi dalam 1x24 jam</p>
                    </div>
                </div>
            `,
            confirmButtonText: 'OK',
            confirmButtonColor: '#10B981'
        });
    });

    // Enhanced WhatsApp contact
    function contactAdmin() {
        const message = `Halo admin, saya ingin konfirmasi pembayaran manual dengan ID #<?php echo e($payment->id); ?> untuk akun <?php echo e(auth()->user()->email); ?>. Terima kasih!`;
        const whatsappUrl = `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`;

        Swal.fire({
            title: 'Hubungi Admin',
            html: `
                <div class="text-center">
                    <p class="mb-4">Anda akan diarahkan ke WhatsApp admin untuk konfirmasi pembayaran</p>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <p class="text-sm text-green-800">Pesan otomatis akan dikirim dengan ID pembayaran Anda</p>
                    </div>
                </div>
            `,
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'Chat Sekarang',
            cancelButtonText: 'Nanti',
            confirmButtonColor: '#10B981',
            cancelButtonColor: '#6B7280'
        }).then((result) => {
            if (result.isConfirmed) {
                window.open(whatsappUrl, '_blank');
            }
        });
    }

    // Copy payment details
    function copyPaymentDetails() {
        const details = `ID Pembayaran: #<?php echo e($payment->id); ?>\nJumlah: Rp <?php echo e(number_format($payment->amount, 0, ',', '.')); ?>\nStatus: <?php echo e($payment->getStatusLabel()); ?>\nTanggal: <?php echo e($payment->created_at->format('d M Y H:i')); ?>`;

        navigator.clipboard.writeText(details).then(function() {
            Swal.fire({
                icon: 'success',
                title: 'Tersalin!',
                text: 'Detail pembayaran telah disalin ke clipboard',
                timer: 1500,
                showConfirmButton: false
            });
        });
    }

    // Back to dashboard with confirmation
    function goToDashboard() {
        Swal.fire({
            title: 'Kembali ke Dashboard',
            text: 'Anda akan diarahkan ke halaman dashboard',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya',
            cancelButtonText: 'Tetap di sini',
            confirmButtonColor: '#3B82F6',
            cancelButtonColor: '#6B7280'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?php echo e(route('user.dashboard')); ?>';
            }
        });
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/payment/success.blade.php ENDPATH**/ ?>