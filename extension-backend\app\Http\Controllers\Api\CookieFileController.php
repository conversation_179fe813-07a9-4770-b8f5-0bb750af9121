<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class CookieFileController extends Controller
{
    /**
     * Upload cookie file untuk site tertentu
     */
    public function uploadCookieFile(Request $request, $siteId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'cookie_file' => 'required|file|mimes:json|max:2048', // Max 2MB
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = $request->user();
            $site = Site::active()->find($siteId);

            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site tidak ditemukan'
                ], 404);
            }

            // Cek akses berdasarkan visibility
            $hasAccess = false;
            if ($user->role === 'elite') {
                $hasAccess = in_array($site->visibility, ['elite', 'both']);
            } elseif ($user->role === 'premium') {
                $hasAccess = in_array($site->visibility, ['premium', 'both']);
            }

            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses ke site ini'
                ], 403);
            }

            $file = $request->file('cookie_file');
            
            // Validasi format JSON
            $fileContent = file_get_contents($file->getPathname());
            $cookiesData = json_decode($fileContent, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    'success' => false,
                    'message' => 'File JSON tidak valid: ' . json_last_error_msg()
                ], 422);
            }
            
            // Validasi struktur cookies
            if (!is_array($cookiesData)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File harus berisi array cookies'
                ], 422);
            }
            
            // Validasi setiap cookie
            foreach ($cookiesData as $index => $cookie) {
                if (!is_array($cookie) || !isset($cookie['name']) || !isset($cookie['value'])) {
                    return response()->json([
                        'success' => false,
                        'message' => "Cookie pada index {$index} tidak memiliki struktur yang valid (harus memiliki 'name' dan 'value')"
                    ], 422);
                }
            }

            // Hapus file lama jika ada
            if ($site->cookie_file_path && Storage::exists($site->cookie_file_path)) {
                Storage::delete($site->cookie_file_path);
            }

            // Simpan file baru
            $fileName = 'cookies/' . $site->domain . '_' . time() . '.json';
            $filePath = $file->storeAs('cookies', $site->domain . '_' . time() . '.json');

            // Update site record
            $site->update([
                'cookie_file_path' => $filePath,
                'cookie_file_uploaded_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File cookies berhasil diupload',
                'data' => [
                    'site_id' => $site->id,
                    'site_name' => $site->name,
                    'domain' => $site->domain,
                    'cookie_file_path' => $filePath,
                    'cookies_count' => count($cookiesData),
                    'uploaded_at' => $site->cookie_file_uploaded_at->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengupload file: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Download cookie file untuk site tertentu
     */
    public function downloadCookieFile(Request $request, $siteId)
    {
        try {
            $user = $request->user();
            $site = Site::active()->find($siteId);

            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site tidak ditemukan'
                ], 404);
            }

            // Cek akses berdasarkan visibility
            $hasAccess = false;
            if ($user->role === 'elite') {
                $hasAccess = in_array($site->visibility, ['elite', 'both']);
            } elseif ($user->role === 'premium') {
                $hasAccess = in_array($site->visibility, ['premium', 'both']);
            }

            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses ke site ini'
                ], 403);
            }

            if (!$site->cookie_file_path || !Storage::exists($site->cookie_file_path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File cookies tidak ditemukan'
                ], 404);
            }

            return Storage::download($site->cookie_file_path, $site->domain . '_cookies.json');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendownload file: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Hapus cookie file untuk site tertentu
     */
    public function deleteCookieFile(Request $request, $siteId)
    {
        try {
            $user = $request->user();
            $site = Site::active()->find($siteId);

            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site tidak ditemukan'
                ], 404);
            }

            // Cek akses berdasarkan visibility
            $hasAccess = false;
            if ($user->role === 'elite') {
                $hasAccess = in_array($site->visibility, ['elite', 'both']);
            } elseif ($user->role === 'premium') {
                $hasAccess = in_array($site->visibility, ['premium', 'both']);
            }

            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses ke site ini'
                ], 403);
            }

            if ($site->cookie_file_path && Storage::exists($site->cookie_file_path)) {
                Storage::delete($site->cookie_file_path);
            }

            // Update site record
            $site->update([
                'cookie_file_path' => null,
                'cookie_file_uploaded_at' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File cookies berhasil dihapus'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus file: ' . $e->getMessage()
            ], 500);
        }
    }
}