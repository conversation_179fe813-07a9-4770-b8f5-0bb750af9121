<?php

namespace App\Filament\Resources\ApiV1\SiteResource\Pages;

use App\Filament\Resources\ApiV1\SiteResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSites extends ListRecords
{
    protected static string $resource = SiteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
