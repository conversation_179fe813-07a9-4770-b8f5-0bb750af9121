<?php

namespace App\Http\Middleware;

use Illuminate\Cookie\Middleware\EncryptCookies as Middleware;

class EncryptCookies extends Middleware
{
    /**
     * The names of the cookies that should not be encrypted.
     *
     * @var array<int, string>
     */
    protected $except = [
        // Exclude session cookies from encryption for extension compatibility
        'satu_pintu_session',
        'XSRF-TOKEN',
        'laravel_session',
    ];
}