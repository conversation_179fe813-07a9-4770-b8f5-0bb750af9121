<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cookie File Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍪 Test Cookie File Upload</h1>
        
        <!-- Login Section -->
        <div class="form-group">
            <h3>1. Login</h3>
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
            
            <label for="password">Password:</label>
            <input type="password" id="password" value="password">
            
            <button onclick="login()">Login</button>
            <div id="loginResult"></div>
        </div>

        <!-- Create Site with Cookie File -->
        <div class="form-group">
            <h3>2. Create Site with Cookie File</h3>
            <label for="siteName">Nama Situs:</label>
            <input type="text" id="siteName" value="Test Site">
            
            <label for="siteUrl">URL:</label>
            <input type="url" id="siteUrl" value="https://example.com">
            
            <label for="siteDomain">Domain:</label>
            <input type="text" id="siteDomain" value="example.com">
            
            <label for="siteCategory">Kategori:</label>
            <select id="siteCategory">
                <option value="elite">Elite</option>
                <option value="premium">Premium</option>
            </select>
            
            <label for="siteVisibility">Visibilitas:</label>
            <select id="siteVisibility">
                <option value="both">Elite & Premium</option>
                <option value="elite">Hanya Elite</option>
                <option value="premium">Hanya Premium</option>
            </select>
            
            <label for="cookieFile">Cookie File (JSON):</label>
            <input type="file" id="cookieFile" accept=".json,application/json">
            <div class="file-info">
                <strong>Format JSON yang diharapkan:</strong><br>
                <code>[
  {
    "name": "session_id",
    "value": "abc123",
    "domain": ".example.com",
    "path": "/",
    "secure": true,
    "httpOnly": true
  }
]</code>
            </div>
            
            <button onclick="createSiteWithFile()">Create Site</button>
            <div id="createResult"></div>
        </div>

        <!-- Test Get Cookies -->
        <div class="form-group">
            <h3>3. Test Get Cookies</h3>
            <label for="testDomain">Domain untuk test:</label>
            <input type="text" id="testDomain" value="example.com">
            
            <button onclick="testGetCookies()">Get Cookies</button>
            <div id="cookiesResult"></div>
        </div>

        <!-- List Sites -->
        <div class="form-group">
            <h3>4. List All Sites</h3>
            <button onclick="listSites()">List Sites</button>
            <div id="sitesResult"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        let currentToken = '';
        let createdSiteId = null;

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
        }

        async function login() {
            try {
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    currentToken = data.token;
                    showResult('loginResult', `✅ Login berhasil!\nToken: ${data.token.substring(0, 20)}...\nUser: ${data.user.name} (${data.user.role})`, 'success');
                } else {
                    showResult('loginResult', `❌ Login gagal: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function createSiteWithFile() {
            if (!currentToken) {
                showResult('createResult', '❌ Silakan login terlebih dahulu', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('name', document.getElementById('siteName').value);
                formData.append('url', document.getElementById('siteUrl').value);
                formData.append('domain', document.getElementById('siteDomain').value);
                formData.append('category', document.getElementById('siteCategory').value);
                formData.append('visibility', document.getElementById('siteVisibility').value);
                formData.append('is_active', '1');
                
                const fileInput = document.getElementById('cookieFile');
                if (fileInput.files[0]) {
                    formData.append('cookie_file_path', fileInput.files[0]);
                    formData.append('cookie_file_uploaded_at', new Date().toISOString());
                }
                
                // First create the site
                const createResponse = await fetch(`${API_BASE_URL}/sites`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Accept': 'application/json'
                    },
                    body: formData
                });
                
                const createData = await createResponse.json();
                
                if (createResponse.ok && createData.success) {
                    createdSiteId = createData.site.id;
                    showResult('createResult', `✅ Site berhasil dibuat!\nID: ${createData.site.id}\nNama: ${createData.site.name}\nCookie File: ${createData.site.cookie_file_path ? 'Uploaded' : 'None'}`, 'success');
                } else {
                    showResult('createResult', `❌ Gagal membuat site: ${createData.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('createResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testGetCookies() {
            if (!currentToken) {
                showResult('cookiesResult', '❌ Silakan login terlebih dahulu', 'error');
                return;
            }

            try {
                const domain = document.getElementById('testDomain').value;
                
                const response = await fetch(`${API_BASE_URL}/sites/cookies`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ domain })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const cookiesInfo = data.cookies ? 
                        `Cookies found: ${data.cookies.length} items\n${JSON.stringify(data.cookies, null, 2)}` :
                        'No cookies found';
                    
                    showResult('cookiesResult', 
                        `✅ Response berhasil!\nDomain: ${data.domain}\nHas Cookie File: ${data.has_cookie_file ? 'Yes' : 'No'}\nFile Uploaded: ${data.cookie_file_uploaded_at || 'N/A'}\n\n${cookiesInfo}`, 
                        'success'
                    );
                } else {
                    showResult('cookiesResult', `❌ Gagal mengambil cookies: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('cookiesResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function listSites() {
            if (!currentToken) {
                showResult('sitesResult', '❌ Silakan login terlebih dahulu', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/sites`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const sitesInfo = data.sites.map(site => 
                        `ID: ${site.id} | ${site.name} (${site.domain}) | Category: ${site.category} | Cookie File: ${site.has_cookie_file ? 'Yes' : 'No'}`
                    ).join('\n');
                    
                    showResult('sitesResult', 
                        `✅ Sites found: ${data.sites.length}\n\n${sitesInfo}`, 
                        'success'
                    );
                } else {
                    showResult('sitesResult', `❌ Gagal mengambil sites: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('sitesResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Create sample JSON file for testing
        function createSampleCookieFile() {
            const sampleCookies = [
                {
                    "name": "session_id",
                    "value": "abc123def456",
                    "domain": ".example.com",
                    "path": "/",
                    "secure": true,
                    "httpOnly": true
                },
                {
                    "name": "user_token",
                    "value": "xyz789uvw012",
                    "domain": ".example.com",
                    "path": "/",
                    "secure": true,
                    "httpOnly": false
                }
            ];
            
            const blob = new Blob([JSON.stringify(sampleCookies, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'sample-cookies.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // Add download sample button
        document.addEventListener('DOMContentLoaded', function() {
            const fileInfo = document.querySelector('.file-info');
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = 'Download Sample JSON';
            downloadBtn.onclick = createSampleCookieFile;
            downloadBtn.style.marginTop = '10px';
            fileInfo.appendChild(downloadBtn);
        });
    </script>
</body>
</html>