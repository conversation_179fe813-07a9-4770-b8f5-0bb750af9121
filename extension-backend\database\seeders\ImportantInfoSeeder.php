<?php

namespace Database\Seeders;

use App\Models\ImportantInfo;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ImportantInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hapus data lama jika ada
        ImportantInfo::truncate();
        
        ImportantInfo::create([
            'title' => 'Selamat Datang di Satu Pintu v2',
            'content' => 'Versi baru dengan fitur halaman detail untuk situs dengan multiple cookies. Klik pada situs untuk melihat detail dan pilih cookie yang ingin digunakan.',
            'type' => 'info',
            'is_active' => true,
            'start_date' => now(),
            'end_date' => now()->addDays(30),
            'priority' => 5,
        ]);

        ImportantInfo::create([
            'title' => 'Pemeliharaan Sistem Terjadwal',
            'content' => 'Sistem akan mengalami pemeliharaan rutin pada hari <PERSON>gu pukul 02:00 - 04:00 WIB. Layanan mungkin terganggu sementara.',
            'type' => 'warning',
            'is_active' => true,
            'start_date' => now(),
            'end_date' => now()->addDays(7),
            'priority' => 4,
        ]);
        
        ImportantInfo::create([
            'title' => 'Fitur Baru: Auto-Login',
            'content' => 'Kini tersedia fitur auto-login yang memungkinkan Anda masuk otomatis ke situs favorit. Aktifkan di pengaturan ekstensi.',
            'type' => 'success',
            'is_active' => true,
            'start_date' => now(),
            'end_date' => now()->addDays(14),
            'priority' => 3,
        ]);
        
        ImportantInfo::create([
            'title' => 'Masalah Koneksi Database',
            'content' => 'Kami sedang mengalami masalah intermiten dengan koneksi database. Tim teknis sedang bekerja untuk memperbaikinya.',
            'type' => 'error',
            'is_active' => true,
            'start_date' => now(),
            'end_date' => now()->addDays(1),
            'priority' => 2,
        ]);
        
        ImportantInfo::create([
            'title' => 'Tips Keamanan',
            'content' => 'Selalu logout dari akun penting setelah selesai menggunakan. Jangan simpan password di browser umum atau komputer publik.',
            'type' => 'info',
            'is_active' => true,
            'start_date' => now(),
            'end_date' => null, // Tidak ada batas waktu
            'priority' => 1,
        ]);
    }
}