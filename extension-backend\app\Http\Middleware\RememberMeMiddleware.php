<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RememberMeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Jika user sudah login dan remember me tidak dicentang, tetap pertahankan session
        if (Auth::guard('anyone')->check() && !$request->has('remember')) {
            // Set remember me secara otomatis untuk session yang sudah ada
            $request->session()->put('remember_me', true);
        }

        return $next($request);
    }
}