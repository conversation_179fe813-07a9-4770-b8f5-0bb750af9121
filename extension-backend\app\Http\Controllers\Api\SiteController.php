<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SiteController extends Controller
{
    /**
     * Mendapatkan daftar sites berdasarkan role pengguna
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Ambil sites berdasarkan role dan visibility pengguna dengan relasi kategori
        $sites = Site::with('categoryModel')
            ->active()
            ->visibleToRole($user->role)
            ->orderBy('name')
            ->get();
        
        return response()->json([
            'success' => true,
            'sites' => $sites->map(function ($site) {
                return [
                    'id' => $site->id,
                    'name' => $site->name,
                    'url' => $site->url,
                    'domain' => $site->domain,
                    'category' => $site->category,
                    'category_info' => [
                        'name' => $site->getCategoryName(),
                        'icon' => $site->getCategoryIcon(),
                        'color' => $site->getCategoryColor(),
                        'slug' => $site->category,
                    ],
                    'visibility' => $site->visibility,
                    'thumbnail' => $site->thumbnail,
                    'description' => $site->description,
                ];
            })
        ]);
    }
    
    /**
     * Mendapatkan detail site beserta cookies
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->user();
            
            $site = Site::with('categoryModel')->active()->find($id);
            
            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site tidak ditemukan'
                ], 404);
            }
            
            // Cek apakah user memiliki akses ke site ini berdasarkan visibility
            $hasAccess = false;
            if ($user->role === 'elite') {
                $hasAccess = in_array($site->visibility, ['elite', 'both']);
            } elseif ($user->role === 'premium') {
                $hasAccess = in_array($site->visibility, ['premium', 'both']);
            }
            
            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses ke site ini'
                ], 403);
            }
            
            return response()->json([
                'success' => true,
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'url' => $site->url,
                    'domain' => $site->domain,
                    'category' => $site->category,
                    'category_info' => [
                        'name' => $site->getCategoryName(),
                        'icon' => $site->getCategoryIcon(),
                        'color' => $site->getCategoryColor(),
                        'slug' => $site->category,
                    ],
                    'thumbnail' => $site->thumbnail,
                    'description' => $site->description,
                    'cookies' => $site->getAllCookies(),
                    'has_cookie_file' => !empty($site->cookie_file_path),
                    'cookie_file_uploaded_at' => $site->cookie_file_uploaded_at?->toISOString(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil detail site'
            ], 500);
        }
    }
    
    /**
     * Mendapatkan cookies untuk domain tertentu
     */
    public function getCookies(Request $request)
    {
        $request->validate([
            'domain' => 'required|string'
        ]);
        
        $user = $request->user();
        $domain = $request->domain;
        
        // Query sites berdasarkan domain dan visibility
        $query = Site::with('categoryModel')->active()->where('domain', $domain)->visibleToRole($user->role);
        
        $site = $query->first();
        
        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site tidak ditemukan atau Anda tidak memiliki akses'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'domain' => $site->domain,
                'cookies' => $site->getAllCookies(),
                'has_cookie_file' => !empty($site->cookie_file_path),
                'cookie_file_uploaded_at' => $site->cookie_file_uploaded_at?->toISOString(),
            ]
        ]);
    }

    /**
     * Mendapatkan cookies dari file JSON untuk site tertentu
     */
    public function getSiteCookies(Request $request, $id)
    {
        try {
            $user = $request->user();
            
            $site = Site::with('categoryModel')->active()->find($id);
            
            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site tidak ditemukan'
                ], 404);
            }
            
            // Cek apakah user memiliki akses ke site ini berdasarkan visibility
            $hasAccess = false;
            if ($user->role === 'elite') {
                $hasAccess = in_array($site->visibility, ['elite', 'both']);
            } elseif ($user->role === 'premium') {
                $hasAccess = in_array($site->visibility, ['premium', 'both']);
            }
            
            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses ke site ini'
                ], 403);
            }
            
            // Ambil cookies dari file JSON
            $cookies = $site->getCookiesFromFile();
            
            if (empty($cookies)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cookies tidak ditemukan untuk site ini'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => [
                    'site_id' => $site->id,
                    'site_name' => $site->name,
                    'domain' => $site->domain,
                    'cookies' => $cookies,
                    'cookie_file_uploaded_at' => $site->cookie_file_uploaded_at?->toISOString(),
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil cookies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created site
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:255',
            'domain' => 'required|string|max:255',
            'category' => 'required|in:elite,premium',
            'visibility' => 'required|in:elite,premium,both',
            'description' => 'nullable|string',
            'cookie_file_path' => 'nullable|file|mimes:json|max:2048',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        
        // Handle cookie file upload
        if ($request->hasFile('cookie_file_path')) {
            $file = $request->file('cookie_file_path');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('cookie-files', $filename, 'private');
            
            $data['cookie_file_path'] = $path;
            $data['cookie_file_uploaded_at'] = now();
        }

        $site = Site::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Site created successfully',
            'site' => [
                'id' => $site->id,
                'name' => $site->name,
                'url' => $site->url,
                'domain' => $site->domain,
                'category' => $site->category,
                'visibility' => $site->visibility,
                'description' => $site->description,
                'cookie_file_path' => $site->cookie_file_path,
                'cookie_file_uploaded_at' => $site->cookie_file_uploaded_at,
                'has_cookie_file' => !empty($site->cookie_file_path),
                'is_active' => $site->is_active,
                'created_at' => $site->created_at,
            ]
        ], 201);
    }
}
