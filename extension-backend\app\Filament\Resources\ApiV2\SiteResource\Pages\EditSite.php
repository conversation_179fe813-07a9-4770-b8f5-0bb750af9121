<?php

namespace App\Filament\Resources\ApiV2\SiteResource\Pages;

use App\Filament\Resources\ApiV2\SiteResource;
use App\Models\SiteJsonFile;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

class EditSite extends EditRecord
{
    protected static string $resource = SiteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Refresh the record to ensure we get the latest data
        $this->record->refresh();
        
        // Load existing JSON files into the form
        $jsonFiles = $this->record->jsonFiles()->pluck('file_path')->toArray();
        $data['json_files'] = $jsonFiles;
        
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Extract json_files from data before updating the site
        $jsonFiles = $data['json_files'] ?? [];
        unset($data['json_files']);
        
        // Log for debugging
        Log::info('EditSite: Updating site', [
            'site_id' => $record->id,
            'new_json_files' => $jsonFiles,
            'current_json_files_count' => $record->jsonFiles->count()
        ]);

        // Update the site record
        $record->update($data);

        // Get current JSON files for this site
        $currentJsonFiles = $record->jsonFiles;
        $currentFilePaths = $currentJsonFiles->pluck('file_path')->toArray();
        
        // Find files to delete (files that exist in database but not in form)
        $filesToDelete = array_diff($currentFilePaths, $jsonFiles);
        
        // Delete removed files from database and storage
        foreach ($filesToDelete as $filePathToDelete) {
            $fileToDelete = $currentJsonFiles->where('file_path', $filePathToDelete)->first();
            if ($fileToDelete) {
                // Delete file from storage
                if (Storage::disk('public')->exists($filePathToDelete)) {
                    Storage::disk('public')->delete($filePathToDelete);
                }
                // Delete record from database
                $fileToDelete->delete();
            }
        }

        // Handle JSON file uploads - add new files
        if (!empty($jsonFiles)) {
            foreach ($jsonFiles as $index => $filePath) {
                if ($filePath && Storage::disk('public')->exists($filePath)) {
                    // Check if this file path already exists for this site
                    $existingFile = SiteJsonFile::where('site_id', $record->id)
                        ->where('file_path', $filePath)
                        ->first();
                    
                    if (!$existingFile) {
                        // Get original filename for name
                        $originalName = pathinfo($filePath, PATHINFO_FILENAME);
                        
                        // Create new SiteJsonFile record
                        SiteJsonFile::create([
                            'site_id' => $record->id,
                            'name' => $originalName ?: 'JSON File ' . ($index + 1),
                            'file_path' => $filePath,
                            'description' => 'Uploaded JSON file',
                            'is_active' => true,
                        ]);
                    }
                }
            }
        }

        // Clear any relevant cache
        Cache::forget('site_' . $record->id . '_json_files');
        Cache::forget('site_' . $record->id);
        
        // Refresh the record to ensure updated relationships are loaded
        $record->refresh();
        $record->load('jsonFiles');
        
        return $record;
    }
}