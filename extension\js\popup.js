// Configuration
// const API_BASE_URL = "https://satupintu.lik.my.id/api";
const API_BASE_URL = "http://127.0.0.1:8000/api/v2";
const WEB_BASE_URL = "http://127.0.0.1:8000";

// Function to redirect to dashboard
function redirectToDashboard() {
  chrome.tabs.create({ url: `${WEB_BASE_URL}/dashboard` });
}

// Keep service worker alive while popup is open
let backgroundPort;

function connectToBackground() {
  try {
    backgroundPort = chrome.runtime.connect({ name: "popup" });
    console.log("🔗 Connected to background service worker");

    backgroundPort.onDisconnect.addListener(() => {
      console.log("🔌 Disconnected from background service worker");
      console.warn("⚠️ Service worker may have been suspended or crashed");
      // Attempt to reconnect if needed
      setTimeout(() => {
        if (!backgroundPort || backgroundPort.disconnected) {
          console.log("🔄 Attempting to reconnect to service worker...");
          connectToBackground();
        }
      }, 1000);
    });
  } catch (error) {
    console.error("❌ Failed to connect to background:", error);
  }
}
// Connect immediately when popup loads
connectToBackground();

// Cleanup on popup close
window.addEventListener("beforeunload", () => {
  if (backgroundPort) {
    backgroundPort.disconnect();
  }
});

// Import components
import importantInfo from "./components/important-info.js";
import siteDetail from "./components/site-detail.js";

// Global variables
let currentUser = null;
let currentToken = null;
let availableSites = [];
let isInitializing = false;
let isInitialized = false;
let isProcessingLogin = false;
let initTimeout = null;
let importantInfoData = null;

// Function to check extension version
async function checkExtensionVersion() {
  try {
    const manifest = chrome.runtime.getManifest();
    const currentVersion = manifest.version;

    console.log(`[POPUP] Current extension version: ${currentVersion}`);

    const response = await fetch(`${API_BASE_URL}/extension/check-version?version=${currentVersion}`);
    const data = await response.json();

    if (data.needs_update) {
      console.log(`[POPUP] New version available: ${data.latest_version}`);
      document.getElementById('main-content').innerHTML = `
        <div class="flex flex-col items-center justify-center p-4 text-center">
          <h2 class="text-xl font-bold text-red-600 mb-2">Pembaruan Ekstensi Tersedia!</h2>
          <p class="text-gray-700 mb-4">Versi terbaru (${data.latest_version}) tersedia. Silakan perbarui ekstensi Anda untuk melanjutkan.</p>
          <button id="updateExtensionBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Perbarui Sekarang
          </button>
        </div>
      `;
      document.getElementById('updateExtensionBtn').addEventListener('click', () => {
        redirectToDashboard();
      });
      return true; // Update needed
    } else {
      console.log('[POPUP] Extension is up to date.');
      return false; // No update needed
    }
  } catch (error) {
    console.error('[POPUP] Error checking extension version:', error);
    // Allow popup to load even if version check fails
    return false;
  }
}


// Function to get localStorage auth data from content script
async function getLocalStorageAuthFromContentScript() {
  try {
    console.log(
      "📡 [POPUP] Requesting localStorage auth data from content script..."
    );

    // Get current active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      console.log("❌ [POPUP] No active tab found");
      return null;
    }

    const activeTab = tabs[0];
    console.log(
      "🔍 [POPUP] Active tab:",
      activeTab.url,
      "Tab ID:",
      activeTab.id
    );

    // Check if tab is on the correct domain
    if (!activeTab.url || !activeTab.url.includes("127.0.0.1:8000")) {
      console.log(
        "❌ [POPUP] Active tab is not on the user dashboard domain. Current URL:",
        activeTab.url
      );
      console.log(
        "❌ [POPUP] Please navigate to http://127.0.0.1:8000/dashboard first"
      );
      return null;
    }

    console.log(
      "✅ [POPUP] Tab is on correct domain, sending message to content script..."
    );

    // Send message to content script
    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => {
        console.log(
          "⏰ [POPUP] Content script communication timeout after 5 seconds"
        );
        resolve(null);
      }, 5000);

      chrome.tabs.sendMessage(
        activeTab.id,
        { action: "getLocalStorageAuth" },
        (response) => {
          clearTimeout(timeoutId);

          if (chrome.runtime.lastError) {
            console.log(
              "❌ [POPUP] Error communicating with content script:",
              chrome.runtime.lastError.message
            );
            console.log(
              "❌ [POPUP] This usually means content script is not injected or tab is not ready"
            );
            resolve(null);
            return;
          }

          console.log(
            "📥 [POPUP] Received response from content script:",
            response
          );

          if (response && response.success && response.authData) {
            console.log(
              "✅ [POPUP] Received auth data from content script:",
              response.authData
            );
            resolve(response.authData);
          } else {
            console.log(
              "❌ [POPUP] No auth data received from content script. Response:",
              response
            );
            resolve(null);
          }
        }
      );
    });
  } catch (error) {
    console.error(
      "❌ [POPUP] Error getting localStorage auth from content script:",
      error
    );
    return null;
  }
}

// Function to check authentication using localStorage data from user dashboard
async function checkLocalStorageAuth() {
  try {
    console.log(
      "🔍 [POPUP] Starting localStorage authentication check via content script..."
    );

    // Get auth data from content script (which can access localStorage from web page)
    const userData = await getLocalStorageAuthFromContentScript();

    if (!userData) {
      console.log(
        "❌ [POPUP] No localStorage auth data found via content script"
      );
      return null;
    }

    console.log(
      "✅ [POPUP] Found localStorage auth data via content script:",
      userData
    );

    // Map user dashboard data structure to backend expected structure
    const mappedData = {
      user_id: userData.id || userData.user_id, // user dashboard uses 'id', backend expects 'user_id'
      email: userData.email,
      session_key:
        userData.session_key ||
        userData.login_timestamp?.toString() ||
        Date.now().toString(),
      // Tambahkan field tambahan untuk validasi lebih lengkap
      role: userData.role,
      subscription_active: userData.subscription_active,
      subscription_status: userData.subscription_status,
      name: userData.name,
    };

    // Validate required fields dengan lebih fleksibel
    const requiredFields = ["user_id", "email"];
    const missingFields = requiredFields.filter((field) => !mappedData[field]);

    if (missingFields.length > 0) {
      console.log(
        "❌ Invalid localStorage auth data - missing required fields:",
        missingFields
      );
      console.log("📄 Available fields in userData:", Object.keys(userData));
      console.log("📄 Mapped data:", mappedData);
      return null;
    }

    // Validasi tambahan: cek apakah user memiliki role yang valid
    if (mappedData.role === "none" || !mappedData.subscription_active) {
      console.log("⚠️ User belum berlangganan atau role tidak valid:", {
        role: mappedData.role,
        subscription_active: mappedData.subscription_active,
      });
      // Tetap lanjutkan untuk redirect ke dashboard
      return {
        success: true,
        authenticated: false,
        user: {
          role: mappedData.role,
          subscription_active: mappedData.subscription_active,
        },
        redirect_url: `${WEB_BASE_URL}/dashboard?message=no_subscription`,
      };
    }

    console.log(
      "✅ All required fields present, sending to backend for validation..."
    );

    // Send data to backend for validation
    const requestBody = mappedData;

    console.log(
      "📤 Sending request to:",
      `${API_BASE_URL}/extension/check-localstorage-auth`
    );
    console.log("📤 Request body:", requestBody);

    const response = await fetch(
      `${API_BASE_URL}/extension/check-localstorage-auth`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        credentials: "include",
        body: JSON.stringify(requestBody),
      }
    );

    console.log("📥 Response status:", response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(
        "❌ localStorage auth validation failed with status:",
        response.status
      );
      console.log("❌ Error response:", errorText);
      return null;
    }

    const result = await response.json();
    console.log("✅ localStorage auth validation result:", result);

    // Store auth data to chrome.storage.local for background script
    if (result && result.success && result.authenticated && result.user) {
      const authDataToStore = {
        token: null, // No token for localStorage auth
        user: result.user,
        timestamp: Date.now(),
        source: "localStorage",
      };

      try {
        // Store with multiple keys for compatibility
        const dataToStore = {
          satuPintuAuth: authDataToStore,
          satu_pintu_user_auth: JSON.stringify(result.user),
          satu_pintu_logged_in: "true",
        };

        await new Promise((resolve, reject) => {
          chrome.storage.local.set(dataToStore, () => {
            if (chrome.runtime.lastError) {
              console.error(
                "❌ Error storing auth data:",
                chrome.runtime.lastError
              );
              reject(chrome.runtime.lastError);
            } else {
              console.log(
                "✅ Auth data stored with all keys:",
                Object.keys(dataToStore)
              );
              resolve();
            }
          });
        });
      } catch (storageError) {
        console.error("❌ Failed to store auth data:", storageError);
      }
    }

    return result;
  } catch (error) {
    console.error("❌ Error in checkLocalStorageAuth:", error);
    console.error("❌ Error stack:", error.stack);
    return null;
  }
}

// DOM elements
let loginForm,
  loginBtn,
  emailInput,
  passwordInput,
  logoutBtn,
  loginScreen,
  mainScreen,
  userInfo,
  sitesList,
  statusDiv,
  userName,
  userRole,
  loginStatus,
  troubleshootingDiv,
  fullPageLoader,
  subscriptionInfo,
  filterTabs,
  searchInput,
  appVersion;

// Categories data
let availableCategories = [];

// Initialize when DOM is loaded with debouncing
function debouncedInit() {
  if (initTimeout) {
    clearTimeout(initTimeout);
  }
  initTimeout = setTimeout(init, 100); // 100ms debounce
}

// Enable smooth scrolling for sites section
function enableSmoothScrolling() {
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    // Ensure the sites section is scrollable
    sitesSection.style.overflowY = "auto";
    sitesSection.style.height = "calc(100% - 120px)";
    sitesSection.style.maxHeight = "calc(100% - 120px)";
    sitesSection.style.flex = "1";

    // Add wheel event listener for smooth scrolling
    sitesSection.addEventListener("wheel", function (e) {
      // Prevent the event from propagating to parent elements
      e.stopPropagation();
    });

    // Force layout recalculation
    sitesSection.style.display = "none";
    setTimeout(() => {
      sitesSection.style.display = "block";

      // Add MutationObserver to ensure scrolling works when content changes
      const observer = new MutationObserver(() => {
        sitesSection.style.overflowY = "auto";
      });

      observer.observe(sitesSection, { childList: true, subtree: true });
    }, 0);
  }
}

// Event listeners
document.addEventListener("DOMContentLoaded", function () {
  // Get DOM elements
  loginForm = document.getElementById("loginForm");
  loginBtn = document.getElementById("loginBtn");
  emailInput = document.getElementById("email");
  passwordInput = document.getElementById("password");
  logoutBtn = document.getElementById("logoutBtn");
  loginScreen = document.getElementById("loginScreen");
  mainScreen = document.getElementById("mainScreen");
  userInfo = document.getElementById("userInfo");
  sitesList = document.getElementById("sitesList");
  statusDiv = document.getElementById("status");
  userName = document.getElementById("userName");
  userRole = document.getElementById("userRole");
  loginStatus = document.getElementById("loginStatus");
  troubleshootingDiv = document.getElementById("troubleshooting");
  fullPageLoader = document.getElementById("fullPageLoader");
  subscriptionInfo = document.getElementById("subscriptionInfo");
  filterTabs = document.querySelectorAll(".filter-tab");
  searchInput = document.getElementById("searchInput");
  appVersion = document.getElementById("appVersion");

  // Tambahkan event listener untuk efek ripple pada tombol login
  if (loginBtn) {
    loginBtn.addEventListener("mousedown", function (e) {
      const ripple = loginBtn.querySelector(".btn-ripple");
      if (ripple) {
        // Reset animasi
        ripple.style.animation = "none";
        ripple.offsetHeight; // Trigger reflow

        // Posisikan ripple pada posisi klik
        const rect = loginBtn.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        ripple.style.top = y + "px";
        ripple.style.left = x + "px";

        // Mulai animasi
        ripple.style.animation = "ripple 0.6s ease-out forwards";
      }
    });
  }

  // Fix scrolling issues
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    // Prevent scrolling on body when scrolling in sites section
    sitesSection.addEventListener("wheel", function (e) {
      e.stopPropagation();
    });
  }

  // Login form event listeners removed - authentication now handled through website session

  if (logoutBtn) {
    logoutBtn.addEventListener("click", handleLogout);
  }

  // Add search input event listener
  if (searchInput) {
    searchInput.addEventListener("input", (e) => {
      const searchTerm = e.target.value;
      const activeTab = document.querySelector(".filter-tab.active");
      const category = activeTab ? activeTab.dataset.category : "all";
      renderSites(category, searchTerm);
    });
  }

  // Add filter tab event listeners (will be updated when categories are loaded)
  if (document.getElementById("filterTabs")) {
    setupFilterTabListeners();
  }

  // Message listener removed - authentication now handled through website session

  // Initialize with debouncing
  debouncedInit();

  // Enable smooth scrolling
  enableSmoothScrolling();

  // Setup footer handlers
  setupFooterHandlers();
});

async function init() {
  console.log("🚀 [POPUP] Init function called at:", new Date().toISOString());

  // Clear any pending init timeout
  if (initTimeout) {
    clearTimeout(initTimeout);
    initTimeout = null;
  }

  // Prevent multiple initialization
  if (isInitializing || isInitialized) {
    console.log(
      "⚠️ [POPUP] Init already in progress or completed, skipping..."
    );
    return;
  }

  // Set popup size
  try {
    document.body.style.width = "420px";
    document.body.style.height = "600px";
    document.body.style.minWidth = "420px";
    document.body.style.minHeight = "600px";
    document.body.style.maxWidth = "420px";
    document.body.style.maxHeight = "600px";
    document.body.style.overflow = "hidden"; // Ensure body doesn't scroll
  } catch (error) {
    console.log("Could not set popup size:", error);
  }

  isInitializing = true;
  console.log("Initializing popup...", {
    timestamp: new Date().toISOString(),
    flags: { isInitializing, isInitialized, isProcessingLogin },
  });

  try {
    showFullPageLoading(
      true,
      "Memulai aplikasi...",
      "Memeriksa status pengguna"
    );

    // STRICT SUBSCRIPTION VALIDATION - Check subscription status first before allowing any access
    let subscriptionData = null;
    try {
      console.log("Performing strict subscription validation...");
      const subscriptionResponse = await fetch(
        `${API_BASE_URL}/extension/check-session`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          credentials: "include",
        }
      );

      subscriptionData = await subscriptionResponse.json();
      console.log("Subscription validation result:", subscriptionData);

      // If session-based auth fails, try localStorage fallback
      if (
        !subscriptionResponse.ok ||
        !subscriptionData.success ||
        !subscriptionData.authenticated
      ) {
        console.log(
          "🔄 [POPUP] Session-based auth failed, trying localStorage fallback..."
        );
        console.log(
          "🔄 [POPUP] Session response status:",
          subscriptionResponse.status
        );
        console.log("🔄 [POPUP] Session data:", subscriptionData);

        subscriptionData = await checkLocalStorageAuth();
        console.log("🔄 [POPUP] localStorage auth result:", subscriptionData);

        if (
          !subscriptionData ||
          !subscriptionData.success ||
          !subscriptionData.authenticated
        ) {
          console.log("❌ [POPUP] Both session and localStorage auth failed");

          // Check if it's a subscription issue or authentication issue
          if (
            subscriptionData &&
            subscriptionData.user &&
            subscriptionData.user.role === "none"
          ) {
            // User exists but has no subscription
            showFullPageLoading(
              true,
              "Belum Berlangganan",
              "Anda belum memiliki langganan aktif"
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));
            window.open(
              subscriptionData?.redirect_url ||
                `${WEB_BASE_URL}/dashboard?message=no_subscription`,
              "_blank"
            );
          } else if (
            subscriptionData &&
            subscriptionData.user &&
            (subscriptionData.user.subscription?.is_expired ||
              !subscriptionData.subscription_active)
          ) {
            // User exists but subscription is expired
            showFullPageLoading(
              true,
              "Masa langganan anda telah habis",
              "Mengarahkan ke dashboard"
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));
            window.open(
              subscriptionData?.redirect_url ||
                `${WEB_BASE_URL}/dashboard?message=subscription_expired`,
              "_blank"
            );
          } else {
            // No authentication data found - user needs to login
            showFullPageLoading(
              true,
              "Akses ditolak",
              "Sesi Anda telah berakhir. Silakan login kembali."
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));
            window.open(
              subscriptionData?.redirect_url || `${WEB_BASE_URL}/login`,
              "_blank"
            );
          }
          window.close();
          return;
        }

        console.log(
          "✅ [POPUP] localStorage auth successful, continuing with initialization"
        );
      } else {
        console.log("✅ [POPUP] Session-based auth successful");
      }

      // Enhanced subscription and role validation
      const userRole = subscriptionData.user?.role || "none";
      const subscriptionStatus =
        subscriptionData.user?.subscription?.status || "inactive";
      const isSubscriptionExpired =
        subscriptionData.user?.subscription?.is_expired || false;

      console.log("🔍 [POPUP] User validation details:", {
        role: userRole,
        subscription_active: subscriptionData.subscription_active,
        subscription_status: subscriptionStatus,
        is_expired: isSubscriptionExpired,
      });

      // Check if user role is 'none' (no subscription)
      if (userRole === "none") {
        showFullPageLoading(
          true,
          "Belum Berlangganan",
          "Anda belum memiliki langganan aktif"
        );
        await new Promise((resolve) => setTimeout(resolve, 2500));

        showFullPageLoading(
          true,
          "Mengarahkan ke Dashboard",
          "Silakan berlangganan untuk menggunakan ekstensi"
        );
        await new Promise((resolve) => setTimeout(resolve, 2000));

        window.open(
          subscriptionData.redirect_url ||
            `${WEB_BASE_URL}/dashboard?message=no_subscription`,
          "_blank"
        );
        window.close();
        return;
      }

      // Check if subscription is expired
      if (isSubscriptionExpired || !subscriptionData.subscription_active) {
        showFullPageLoading(
          true,
          "Langganan Berakhir",
          "Langganan Anda telah berakhir"
        );
        await new Promise((resolve) => setTimeout(resolve, 2500));

        showFullPageLoading(
          true,
          "Mengarahkan ke Dashboard",
          "Silakan perpanjang langganan untuk melanjutkan"
        );
        await new Promise((resolve) => setTimeout(resolve, 2000));

        window.open(
          subscriptionData.redirect_url ||
            `${WEB_BASE_URL}/dashboard?message=subscription_expired`,
          "_blank"
        );
        window.close();
        return;
      }

      // If subscription is active, store user data and continue
      currentUser = {
        ...subscriptionData.user,
        subscription_expires_at: subscriptionData.user.subscription?.expires_at,
        subscription_active: !subscriptionData.user.subscription?.is_expired,
        subscription_status: subscriptionData.user.subscription?.status,
        subscription_plan: subscriptionData.user.subscription?.name,
      };
      currentToken = null;

      console.log(
        "✅ Subscription validation passed. User has active subscription:",
        currentUser.subscription_plan
      );

      // Store auth data to chrome.storage.local for background script
      const authDataToStore = {
        token: null, // No token for session auth
        user: currentUser,
        timestamp: Date.now(),
        source: "session",
      };

      try {
        // Store with multiple keys for compatibility
        const dataToStore = {
          satuPintuAuth: authDataToStore,
          satu_pintu_user_auth: JSON.stringify(currentUser),
          satu_pintu_logged_in: "true",
        };

        await new Promise((resolve, reject) => {
          chrome.storage.local.set(dataToStore, () => {
            if (chrome.runtime.lastError) {
              console.error(
                "❌ Error storing session auth data:",
                chrome.runtime.lastError
              );
              reject(chrome.runtime.lastError);
            } else {
              console.log(
                "✅ Session auth data stored with all keys:",
                Object.keys(dataToStore)
              );
              resolve();
            }
          });
        });
      } catch (storageError) {
        console.error("❌ Failed to store session auth data:", storageError);
      }
    } catch (subscriptionError) {
      console.error("Subscription validation failed:", subscriptionError);

      // Try localStorage fallback even if there's an error
      console.log("Trying localStorage fallback due to error...");
      try {
        subscriptionData = await checkLocalStorageAuth();

        if (
          subscriptionData &&
          subscriptionData.success &&
          subscriptionData.authenticated
        ) {
          console.log("✅ localStorage fallback successful!");

          // Enhanced subscription and role validation for localStorage auth
          const userRole = subscriptionData.user?.role || "none";
          const subscriptionStatus =
            subscriptionData.user?.subscription?.status || "inactive";
          const isSubscriptionExpired =
            subscriptionData.user?.subscription?.is_expired || false;

          console.log("🔍 [POPUP] localStorage auth validation details:", {
            role: userRole,
            subscription_active: subscriptionData.subscription_active,
            subscription_status: subscriptionStatus,
            is_expired: isSubscriptionExpired,
          });

          // Check if user role is 'none' (no subscription)
          if (userRole === "none") {
            showFullPageLoading(
              true,
              "Belum Berlangganan",
              "Anda belum memiliki langganan aktif"
            );
            await new Promise((resolve) => setTimeout(resolve, 2500));

            showFullPageLoading(
              true,
              "Mengarahkan ke Dashboard",
              "Silakan berlangganan untuk menggunakan ekstensi"
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));

            window.open(
              subscriptionData.redirect_url ||
                `${WEB_BASE_URL}/dashboard?message=no_subscription`,
              "_blank"
            );
            window.close();
            return;
          }

          // Check if subscription is expired
          if (isSubscriptionExpired || !subscriptionData.subscription_active) {
            showFullPageLoading(
              true,
              "Langganan Berakhir",
              "Langganan Anda telah berakhir"
            );
            await new Promise((resolve) => setTimeout(resolve, 2500));

            showFullPageLoading(
              true,
              "Mengarahkan ke Dashboard",
              "Silakan perpanjang langganan untuk melanjutkan"
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));

            window.open(
              subscriptionData.redirect_url ||
                `${WEB_BASE_URL}/dashboard?message=subscription_expired`,
              "_blank"
            );
            window.close();
            return;
          }

          // Store user data and continue
          currentUser = {
            ...subscriptionData.user,
            subscription_expires_at:
              subscriptionData.user.subscription?.expires_at,
            subscription_active:
              !subscriptionData.user.subscription?.is_expired,
            subscription_status: subscriptionData.user.subscription?.status,
            subscription_plan: subscriptionData.user.subscription?.name,
          };
          currentToken = null;

          console.log(
            "✅ localStorage auth successful. User has active subscription:",
            currentUser.subscription_plan
          );

          // Store auth data to chrome.storage.local for background script
          const authDataToStore = {
            token: null, // No token for localStorage auth
            user: currentUser,
            timestamp: Date.now(),
            source: "localStorage_fallback",
          };

          try {
            await new Promise((resolve, reject) => {
              chrome.storage.local.set(
                { satuPintuAuth: authDataToStore },
                () => {
                  if (chrome.runtime.lastError) {
                    console.error(
                      "❌ Error storing localStorage fallback auth data:",
                      chrome.runtime.lastError
                    );
                    reject(chrome.runtime.lastError);
                  } else {
                    console.log(
                      "✅ localStorage fallback auth data stored to chrome.storage.local for background script"
                    );
                    resolve();
                  }
                }
              );
            });
          } catch (storageError) {
            console.error(
              "❌ Failed to store localStorage fallback auth data:",
              storageError
            );
          }
        } else {
          throw new Error("localStorage auth also failed");
        }
      } catch (localStorageError) {
        console.error("localStorage fallback also failed:", localStorageError);

        // Check if we have any user data to determine the appropriate message
        if (subscriptionData && subscriptionData.user) {
          if (subscriptionData.user.role === "none") {
            showFullPageLoading(
              true,
              "Belum Berlangganan",
              "Anda belum memiliki langganan aktif"
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));
            window.open(
              `${WEB_BASE_URL}/dashboard?message=no_subscription`,
              "_blank"
            );
          } else if (
            subscriptionData.user.subscription?.is_expired ||
            !subscriptionData.subscription_active
          ) {
            showFullPageLoading(
              true,
              "Masa langganan anda telah habis",
              "Mengarahkan ke dashboard"
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));
            window.open(
              `${WEB_BASE_URL}/dashboard?message=subscription_expired`,
              "_blank"
            );
          } else {
            showFullPageLoading(
              true,
              "Kesalahan validasi",
              "Tidak dapat memverifikasi langganan. Mengarahkan ke login..."
            );
            await new Promise((resolve) => setTimeout(resolve, 2000));
            window.open(`${WEB_BASE_URL}/login`, "_blank");
          }
        } else {
          // No user data at all - redirect to login
          showFullPageLoading(
            true,
            "Akses ditolak",
            "Sesi Anda telah berakhir. Silakan login kembali."
          );
          await new Promise((resolve) => setTimeout(resolve, 2000));
          window.open(`${WEB_BASE_URL}/login`, "_blank");
        }
        window.close();
        return;
      }
    }

    // Test server connection after subscription validation
    showFullPageLoading(
      true,
      "Memulai aplikasi...",
      "Memeriksa koneksi server"
    );

    try {
      console.log("Testing server connection to:", API_BASE_URL);
      const testResponse = await fetch(`${API_BASE_URL}/sites`, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        credentials: "include",
      });
      console.log(
        "Server connection test result:",
        testResponse.status,
        testResponse.statusText
      );
      if (!testResponse.ok && testResponse.status !== 401) {
        console.warn(
          "Server may not be running properly. Status:",
          testResponse.status
        );
      }
    } catch (connectionError) {
      console.error("Server connection test failed:", connectionError);
    }

    showFullPageLoading(true, "Memuat aplikasi...", "Menyiapkan antarmuka");

    // Clear any existing badge when popup opens
    try {
      await chrome.action.setBadgeText({ text: "" });
    } catch (error) {
      console.log("Could not clear badge:", error);
    }

    // Load sites and show main screen since subscription is already validated
    showFullPageLoading(
      true,
      "Memuat daftar situs...",
      "Mengambil data dari server"
    );
    await loadSites();
    updateUI();
    showMainScreen();
  } catch (error) {
    console.error("Initialization error:", error);
    showFullPageLoading(
      true,
      "Terjadi kesalahan...",
      "Menampilkan halaman login"
    );
    await new Promise((resolve) => setTimeout(resolve, 1000));
    // Redirect to website login instead of showing login screen
    window.open(`${WEB_BASE_URL}/login`, "_blank");
    window.close();
  } finally {
    showFullPageLoading(false);
    isInitializing = false;
    isInitialized = true;
    console.log("Popup initialization completed successfully", {
      timestamp: new Date().toISOString(),
      flags: { isInitializing, isInitialized, isProcessingLogin },
    });
  }
}

// checkAuthStatus function removed - authentication and subscription validation now handled in init() function

// Login function removed - authentication now handled through website session
// Users must login through the website first before using the extension

// setLoginLoading function removed - no longer needed with website-based authentication

// handleLoginSuccess function removed - authentication now handled through website session

// Function removed - login now handled through web page

async function handleLogout() {
  try {
    // Show logout loading
    showFullPageLoading(true, "Melakukan logout...", "Mengarahkan ke website");

    // Clear local data
    await clearStoredAuth();

    // Reset global variables
    currentUser = null;
    currentToken = null;
    availableSites = [];

    // Reset initialization flags
    isInitialized = false;
    isProcessingLogin = false;

    // Send logout message to background script to clear cached data
    try {
      chrome.runtime.sendMessage(
        {
          action: "logout",
        },
        (response) => {
          if (response && response.success) {
            console.log("Cached data cleared successfully");
          } else {
            console.error("Error clearing cached data:", response?.error);
          }
        }
      );
    } catch (bgError) {
      console.log("Background script message failed:", bgError);
    }

    // Show completion message
    showFullPageLoading(
      true,
      "Logout berhasil!",
      "Mengarahkan ke website logout"
    );
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Redirect to website logout page
    chrome.tabs.create({ url: `${WEB_BASE_URL}/logout` });

    // Close popup
    window.close();
  } catch (error) {
    console.error("Logout error:", error);
    showFullPageLoading(false);
    showStatus("Terjadi kesalahan saat logout", "error");
  }
}

async function getCurrentUser() {
  const response = await fetch(`${API_BASE_URL}/auth/me`, {
    headers: {
      Authorization: `Bearer ${currentToken}`,
      Accept: "application/json",
    },
  });

  if (!response.ok) {
    throw new Error("Failed to get user info");
  }

  const data = await response.json();
  // Map nested subscription data to flat structure for compatibility
  return {
    ...data.user,
    subscription_expires_at: data.user.subscription?.expires_at,
    subscription_active: !data.user.subscription?.is_expired,
    subscription_status: data.user.subscription?.status,
    subscription_plan: data.user.subscription?.name,
  };
}

async function loadCategories() {
  // Extract categories from available sites instead of API call
  const categoriesSet = new Set();
  const categoriesMap = new Map();

  availableSites.forEach((site) => {
    if (site.category_model && site.category_model.slug) {
      const category = site.category_model;
      if (!categoriesMap.has(category.slug)) {
        categoriesMap.set(category.slug, {
          id: category.id,
          name: category.name,
          slug: category.slug,
          color: category.color,
        });
      }
    } else if (site.category) {
      // Fallback for old category format
      const categorySlug = site.category;
      if (!categoriesMap.has(categorySlug)) {
        categoriesMap.set(categorySlug, {
          id: categorySlug,
          name: categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1),
          slug: categorySlug,
        });
      }
    }
  });

  // Convert map to array
  availableCategories = Array.from(categoriesMap.values());

  // Sort categories by name
  availableCategories.sort((a, b) => a.name.localeCompare(b.name));

  renderFilterTabs();
}

function renderFilterTabs() {
  const filterTabsContainer = document.getElementById("filterTabs");
  if (!filterTabsContainer) return;

  // Clear existing tabs except "Semua"
  filterTabsContainer.innerHTML =
    '<button class="filter-tab active" data-category="all">Semua</button>';

  // Add category tabs
  availableCategories.forEach((category) => {
    const tab = document.createElement("button");
    tab.className = "filter-tab";
    tab.dataset.category = category.slug;
    tab.textContent = category.name;
    filterTabsContainer.appendChild(tab);
  });

  // Setup event listeners for new tabs
  setupFilterTabListeners();
}

function setupFilterTabListeners() {
  const tabs = document.querySelectorAll(".filter-tab");
  tabs.forEach((tab) => {
    // Remove existing listeners
    tab.replaceWith(tab.cloneNode(true));
  });

  // Add new listeners
  const newTabs = document.querySelectorAll(".filter-tab");
  newTabs.forEach((tab) => {
    tab.addEventListener("click", (e) => {
      // Remove active class from all tabs
      newTabs.forEach((t) => t.classList.remove("active"));
      // Add active class to clicked tab
      e.target.classList.add("active");
      // Filter sites
      const category = e.target.dataset.category;
      const searchTerm = searchInput ? searchInput.value : "";
      renderSites(category, searchTerm);
    });
  });
}

async function loadSites() {
  // Set timeout for auto-logout after 1 minute
  const timeoutId = setTimeout(() => {
    showFullPageLoading(false);
    showStatus(
      "Timeout: Proses memuat data terlalu lama. Logout otomatis...",
      "error"
    );
    setTimeout(() => {
      handleLogout();
    }, 2000);
  }, 60000); // 60 seconds

  // Ensure sites section is scrollable
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    sitesSection.style.overflowY = "auto";
    sitesSection.style.height = "calc(100% - 120px)";
    sitesSection.style.maxHeight = "calc(100% - 120px)";
  }

  try {
    // Use session-based authentication instead of token
    const response = await fetch(`${API_BASE_URL}/sites`, {
      headers: {
        Accept: "application/json",
      },
      credentials: "include", // Include cookies for session
    });

    // Clear timeout if request succeeds
    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      console.log("Sites API response:", data);

      if (data.success) {
        availableSites = data.sites || [];
        importantInfoData = data.important_info || null;

        console.log(`Loaded ${availableSites.length} sites from API`);

        // Update loading message while rendering
        showFullPageLoading(
          true,
          "Menyiapkan daftar situs...",
          `Menampilkan ${availableSites.length} situs tersedia`
        );

        // Add small delay to show the message
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Load categories if not already loaded
        try {
          await loadCategories();
        } catch (categoryError) {
          console.warn(
            "Failed to load categories in loadSites:",
            categoryError
          );
        }

        // Show important info if available
        if (importantInfoData) {
          console.log("Showing important info:", importantInfoData);
          importantInfo.show(importantInfoData);
        }

        // Pastikan sitesList ada sebelum merender
        if (!sitesList) {
          sitesList = document.getElementById("sitesList");
          if (!sitesList) {
            console.error(
              "Element sitesList tidak ditemukan saat akan merender"
            );
            // Coba buat elemen jika tidak ditemukan
            const sitesSection = document.getElementById("sitesSection");
            if (sitesSection) {
              sitesSection.innerHTML =
                '<div class="sites-grid" id="sitesList"></div>';
              sitesList = document.getElementById("sitesList");
              console.log("Created new sitesList element");
            }
          }
        }

        // Log untuk debugging
        console.log("Before rendering sites:", {
          availableSites: availableSites.length,
          sitesList: sitesList ? "exists" : "missing",
          sitesListElement: sitesList,
        });

        // Render sites
        renderSites();

        // Update UI components
        updateUI();

        // Hide loading after sites are rendered
        showFullPageLoading(false);

        showStatus(`${availableSites.length} situs berhasil dimuat`, "success");

        console.log("Sites rendering completed successfully");
      } else {
        console.error("Sites API returned success=false:", data);
        showFullPageLoading(false);
        showStatus("Gagal memuat daftar situs", "error");
      }
    } else if (response.status === 401) {
      // Handle unauthorized - session expired/logout
      console.log("Session expired or unauthorized, redirecting to dashboard");
      showFullPageLoading(
        true,
        "Sesi telah berakhir...",
        "Mengarahkan ke dashboard"
      );
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Clear any stored auth data
      await clearStoredAuth();
      currentToken = null;
      currentUser = null;

      // Redirect to dashboard (will redirect to login if needed)
      window.open(`${WEB_BASE_URL}/dashboard`, "_blank");
      window.close();
      return;
    } else if (response.status === 403) {
      // Handle subscription expired
      const data = await response.json();
      if (data.subscription_expired) {
        showFullPageLoading(
          true,
          "Akun tidak aktif...",
          "Masa aktif akun telah berakhir"
        );
        await new Promise((resolve) => setTimeout(resolve, 2000));

        await clearStoredAuth();
        currentToken = null;
        currentUser = null;
        showFullPageLoading(false);
        // Redirect to website login instead of showing login screen
        window.open(`${WEB_BASE_URL}/login`, "_blank");
        window.close();
        return;
      } else {
        // Handle subscription expired - redirect to dashboard
        showFullPageLoading(
          true,
          "Masa langganan anda telah habis",
          "Mengarahkan ke dashboard"
        );
        await new Promise((resolve) => setTimeout(resolve, 2000));

        await clearStoredAuth();
        currentToken = null;
        currentUser = null;

        // Redirect to dashboard
        window.open(`${WEB_BASE_URL}/dashboard`, "_blank");
        window.close();
        return;
      }
    } else {
      // Handle other HTTP errors - likely session issues
      console.log(
        `HTTP Error ${response.status}, likely session issue, redirecting to dashboard`
      );
      showFullPageLoading(
        true,
        "Gagal memuat data...",
        "Mengarahkan ke dashboard"
      );
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Clear any stored auth data
      await clearStoredAuth();
      currentToken = null;
      currentUser = null;

      // Redirect to dashboard
      window.open(`${WEB_BASE_URL}/dashboard`, "_blank");
      window.close();
      return;
    }
  } catch (error) {
    // Clear timeout if request fails
    clearTimeout(timeoutId);
    console.error("Error loading sites:", error);

    // Network errors or other fetch failures likely indicate session issues
    console.log(
      "Network error or fetch failure, likely session issue, redirecting to dashboard"
    );
    showFullPageLoading(
      true,
      "Koneksi bermasalah...",
      "Mengarahkan ke dashboard"
    );
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Clear any stored auth data
    await clearStoredAuth();
    currentToken = null;
    currentUser = null;

    // Redirect to dashboard
    window.open(`${WEB_BASE_URL}/dashboard`, "_blank");
    window.close();
    return;
  }
}

function renderSites(filterCategory = "all", searchTerm = "") {
  // Pastikan sitesList ada
  if (!sitesList) {
    sitesList = document.getElementById("sitesList");
    if (!sitesList) {
      console.error("Element sitesList tidak ditemukan");
      return;
    }
  }

  // Kosongkan konten sitesList
  sitesList.innerHTML = "";

  // Log untuk debugging
  console.log("Rendering sites:", {
    availableSites: availableSites.length,
    filterCategory,
    searchTerm,
  });

  // Ensure sites section is scrollable
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    sitesSection.style.overflowY = "auto";
    sitesSection.style.height = "calc(100% - 120px)";
    sitesSection.style.maxHeight = "calc(100% - 120px)";
    sitesSection.style.flex = "1";

    // Ensure parent container has proper overflow settings
    const mainContent = document.querySelector(".main-content");
    if (mainContent) {
      mainContent.style.overflow = "hidden";
    }
  }

  // Tampilkan pesan jika tidak ada situs tersedia
  if (availableSites.length === 0) {
    sitesSection.innerHTML = `
            <div class="no-sites">
                <div class="no-sites-icon">🚪</div>
                <div class="no-sites-title">Tidak ada aplikasi tersedia</div>
                <div class="no-sites-subtitle">Silakan hubungi administrator untuk menambahkan aplikasi</div>
            </div>
        `;
    return;
  }

  // Reset sitesSection content jika sebelumnya menampilkan pesan "Tidak ada aplikasi tersedia"
  if (sitesSection.querySelector(".no-sites")) {
    sitesSection.innerHTML = '<div class="sites-grid" id="sitesList"></div>';
    sitesList = document.getElementById("sitesList");
  }

  // Pastikan sitesList ada dan kosong
  if (!sitesList) {
    sitesList = document.getElementById("sitesList");
    if (!sitesList) {
      console.error("Element sitesList tidak ditemukan");
      // Coba buat elemen jika tidak ditemukan
      sitesSection.innerHTML = '<div class="sites-grid" id="sitesList"></div>';
      sitesList = document.getElementById("sitesList");
      if (!sitesList) {
        console.error("Gagal membuat element sitesList");
        return;
      }
    }
  }

  let filteredSites = availableSites;

  // Filter by category
  if (filterCategory !== "all") {
    filteredSites = availableSites.filter((site) => {
      // Check if site has categoryModel relationship
      if (site.category_model && site.category_model.slug) {
        return site.category_model.slug === filterCategory;
      }
      // Fallback to old category field
      return site.category === filterCategory;
    });
  }

  // Filter by search term
  if (searchTerm.trim() !== "") {
    const searchLower = searchTerm.toLowerCase();
    filteredSites = filteredSites.filter((site) => {
      return (
        site.name.toLowerCase().includes(searchLower) ||
        site.domain.toLowerCase().includes(searchLower) ||
        (site.description &&
          site.description.toLowerCase().includes(searchLower))
      );
    });
  }

  if (filteredSites.length === 0) {
    const message =
      searchTerm.trim() !== ""
        ? `Tidak ada aplikasi yang ditemukan untuk "${searchTerm}"`
        : "Tidak ada aplikasi untuk kategori ini";
    sitesList.innerHTML = `
            <div class="no-sites">
                <div class="no-sites-icon">🔍</div>
                <div class="no-sites-title">${message}</div>
                <div class="no-sites-subtitle">Coba gunakan kata kunci yang berbeda atau pilih kategori lain</div>
            </div>
        `;
    return;
  }

  filteredSites.forEach((site) => {
    const siteCard = document.createElement("div");
    siteCard.className = "site-card";
    siteCard.dataset.category = site.category;

    // Generate logo based on site name
    const logoContent = getSiteLogo(site);

    // Get category info
    let categoryClass = site.category || "general";
    let categoryName = site.category_model
      ? site.category_model.name
      : categoryClass;

    // Generate description
    let description = site.description || `Akses ke ${site.domain}`;
    if (description.length > 50) {
      description = description.substring(0, 47) + "...";
    }

    // Get status based on category
    let statusClass = "general";
    if (site.category_model) {
      statusClass = site.category_model.slug;
    } else if (site.category) {
      statusClass = site.category;
    }

    siteCard.innerHTML = `
            <div class="site-logo">${logoContent}</div>
            <div class="site-info">
                <div class="site-name">${site.name}</div>
            </div>
        `;

    siteCard.addEventListener("click", () => openSiteWithCookies(site));
    sitesList.appendChild(siteCard);
  });
}

function getSiteLogo(site) {
  // Priority 1: Use logo_path from API v2 if available
  if (site.logo_path && site.logo_path.trim() !== "") {
    // Construct full URL if logo_path is a relative path
    const logoUrl = site.logo_path.startsWith("http")
      ? site.logo_path
      : `${API_BASE_URL.replace(
          "/api/v2",
          "/storage"
        )}/${site.logo_path.replace(/^\//, "")}`;

    return `<img src="${logoUrl}" alt="${site.name}">`;
  }

  // Priority 2: Use thumbnail from database if available (legacy support)
  if (site.thumbnail && site.thumbnail.trim() !== "") {
    // Construct full URL if thumbnail is a relative path
    const thumbnailUrl = site.thumbnail.startsWith("http")
      ? site.thumbnail
      : `${API_BASE_URL.replace(
          "/api/v2",
          "/storage"
        )}/${site.thumbnail.replace(/^\//, "")}`;

    return `<img src="${thumbnailUrl}" alt="${site.name}">`;
  }

  // Priority 3: Try to get favicon from URL or use fallback
  return getSiteLogoFallback(site.name, site.url);
}

function getSiteLogoFallback(siteName, siteUrl) {
  const domain = new URL(siteUrl).hostname;
  const firstLetter = siteName.charAt(0).toUpperCase();

  // Common site logos mapping with better emojis
  const logoMap = {
    "youtube.com": "📺",
    "netflix.com": "🎬",
    "spotify.com": "🎵",
    "github.com": "💻",
    "google.com": "🔍",
    "facebook.com": "📘",
    "twitter.com": "🐦",
    "instagram.com": "📷",
    "linkedin.com": "💼",
    "amazon.com": "🛒",
    "apple.com": "🍎",
    "microsoft.com": "🪟",
    "discord.com": "💬",
    "reddit.com": "🤖",
    "twitch.tv": "🎮",
    "tiktok.com": "🎭",
    "whatsapp.com": "💬",
    "telegram.org": "✈️",
    "zoom.us": "📹",
    "dropbox.com": "📦",
    "canva.com": "🎨",
    "figma.com": "🎨",
    "notion.so": "📝",
    "slack.com": "💼",
  };

  // Check if we have a custom emoji for this domain
  for (const [siteDomain, emoji] of Object.entries(logoMap)) {
    if (domain.includes(siteDomain)) {
      return `<div style="font-size: 32px; font-weight: bold;">${emoji}</div>`;
    }
  }

  // Try to load favicon with better fallback
  const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
  return `<img src="${faviconUrl}" alt="${siteName}" onerror="this.onerror=null; this.parentElement.innerHTML='<div style=\"font-size: 28px; font-weight: bold; color: rgba(255,255,255,0.9); text-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${firstLetter}</div>';">`;
}

async function openSiteWithCookies(site) {
  try {
    console.log("🚀 MANUAL COOKIE IMPORT - Opening site with cookies:", site);
    console.log(
      "🔄 Auto-inject disabled - importing cookies only on manual click"
    );

    // Validate site data
    if (!site || !site.id || !site.url) {
      throw new Error("Invalid site data");
    }

    // Show initial loading
    showFullPageLoading(
      true,
      `Menyiapkan ${site.name}...`,
      "Menyiapkan aplikasi"
    );
    showStatus(`Memuat data untuk ${site.name}...`, "success");

    // Check for custom redirect first (this doesn't depend on JSON files)
    if (site.enable_custom_redirect && site.redirect_url) {
      console.log("🔄 Custom redirect detected for site:", site.name);

      const redirectDelay = site.redirect_delay || 5;
      let countdown = redirectDelay;

      showFullPageLoading(
        true,
        site.redirect_title || `Menyiapkan ${site.name}...`,
        site.redirect_content || `Mengalihkan dalam ${countdown} detik`
      );
      showStatus(`Mengalihkan ke ${site.name}...`, "success");

      // Start countdown
      const countdownInterval = setInterval(() => {
        countdown--;
        if (countdown > 0) {
          showFullPageLoading(
            true,
            site.redirect_title || `Menyiapkan ${site.name}...`,
            site.redirect_content || `Mengalihkan dalam ${countdown} detik`
          );
        } else {
          clearInterval(countdownInterval);
          showFullPageLoading(
            true,
            "Menyiapkan URL...",
            "Membuka halaman tujuan"
          );

          // Open the redirect URL
          setTimeout(async () => {
            await chrome.tabs.create({
              url: site.redirect_url,
              active: true,
            });
            showFullPageLoading(false);
            showStatus(`Berhasil mengalihkan ke ${site.name}`, "success");
          }, 500);
        }
      }, 1000);

      return;
    }

    let cookiesData = null;

    // First try to get cookies from cache via background script
    try {
      const cachedCookiesResponse = await new Promise((resolve) => {
        chrome.runtime.sendMessage(
          {
            action: "getCachedCookiesForSite",
            siteId: site.id,
          },
          resolve
        );
      });

      if (
        cachedCookiesResponse &&
        cachedCookiesResponse.success &&
        cachedCookiesResponse.data
      ) {
        console.log(
          `Using cached cookies for site: ${site.name} (${cachedCookiesResponse.data.cookies.length} cookies)`
        );
        cookiesData = {
          success: true,
          data: {
            cookies: cachedCookiesResponse.data.cookies,
            site: {
              id: cachedCookiesResponse.data.siteId,
              name: cachedCookiesResponse.data.siteName,
              domain: cachedCookiesResponse.data.domain,
            },
          },
        };
      }
    } catch (error) {
      console.warn("Error getting cached cookies:", error);
    }

    // Fallback to API if not in cache
    if (!cookiesData) {
      console.log("Fetching site details from API for site:", site.name);
      showFullPageLoading(
        true,
        `Mengambil data ${site.name}...`,
        "Menyiapkan aplikasi"
      );

      try {
        const response = await fetch(
          `${API_BASE_URL}/site-details/${site.id}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${currentToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const siteDetailData = await response.json();
          console.log("Fetched site details from API:", siteDetailData);

          // Transform the data to match expected format
          if (siteDetailData.success && siteDetailData.data.json_files) {
            cookiesData = {
              success: true,
              data: {
                site: siteDetailData.data.site,
                json_files: siteDetailData.data.json_files,
              },
            };
          } else {
            cookiesData = { success: true, data: { json_files: [] } };
          }
        } else {
          console.warn(
            "Failed to fetch site details from API, proceeding without cookies"
          );
          cookiesData = { success: true, data: { json_files: [] } };
        }
      } catch (apiError) {
        console.warn("API error, proceeding without cookies:", apiError);
        cookiesData = { success: true, data: { json_files: [] } };
      }
    }

    if (!cookiesData || !cookiesData.success) {
      console.warn(
        "Invalid site details response, proceeding without JSON files"
      );
      cookiesData = { success: true, data: { json_files: [] } };
    }

    // Combine site info with JSON files data
    const siteData = {
      ...site,
      json_files: cookiesData.data ? cookiesData.data.json_files || [] : [],
    };

    // Update site info if available from API
    if (cookiesData.data && cookiesData.data.site) {
      Object.assign(siteData, {
        name: cookiesData.data.site.name || siteData.name,
        url: cookiesData.data.site.url || siteData.url,
        description: cookiesData.data.site.description || siteData.description,
        logo_path: cookiesData.data.site.logo_path || siteData.logo_path,
      });
    }

    console.log("Site data with JSON files:", siteData);

    // Prepare JSON files with index for display
    const jsonFilesWithIndex = [];
    if (siteData.json_files && Array.isArray(siteData.json_files)) {
      siteData.json_files.forEach((jsonFile, index) => {
        jsonFilesWithIndex.push({
          ...jsonFile,
          index: index + 1,
          display_name: jsonFile.name || `File JSON #${index + 1}`,
        });
      });
    }

    // If there are multiple JSON files, show the site detail page
    if (jsonFilesWithIndex.length > 1) {
      console.log(
        `📁 Site has multiple JSON files (${jsonFilesWithIndex.length}), showing selection screen`
      );
      showFullPageLoading(false);

      // Show site detail with JSON files
      siteDetail.show(
        siteData,
        jsonFilesWithIndex,
        () => {
          // On close callback
          console.log("Site detail closed");
        },
        async (site, selectedJsonFile) => {
          // On JSON file select callback
          console.log(`Selected JSON file: ${selectedJsonFile.display_name}`);

          // Fetch cookies from the selected JSON file
          try {
            showFullPageLoading(
              true,
              `Memuat ${site.name} ${selectedJsonFile.index}`,
              "Menyiapkan data"
            );

            const response = await fetch(
              `${API_BASE_URL}/json-files/${selectedJsonFile.id}/cookies`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${currentToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (response.ok) {
              const jsonFileData = await response.json();
              if (jsonFileData.success && jsonFileData.data.cookies) {
                // Update loading message with cookie count
                showFullPageLoading(
                  true,
                  `Memuat ${site.name} ${selectedJsonFile.index}`,
                  `Menyiapkan ${jsonFileData.data.cookies.length} Data`
                );

                // Create a new siteData with cookies from selected JSON file
                const siteWithSelectedCookies = {
                  ...site,
                  cookies: jsonFileData.data.cookies,
                  selectedJsonFile: selectedJsonFile,
                };
                await injectCookieAndOpenSite(siteWithSelectedCookies);
              } else {
                showStatus(
                  `File JSON ${selectedJsonFile.display_name} tidak memiliki cookie yang valid`,
                  "warning"
                );
                showFullPageLoading(false);
              }
            } else {
              showStatus(
                `Gagal memuat cookie dari ${selectedJsonFile.display_name}`,
                "error"
              );
              showFullPageLoading(false);
            }
          } catch (error) {
            console.error("Error loading JSON file cookies:", error);
            showStatus(`Error: ${error.message}`, "error");
            showFullPageLoading(false);
          }
        }
      );
      return;
    }

    // Check for custom redirect after JSON files are loaded
    if (
      siteData.redirect_url &&
      siteData.redirect_title &&
      siteData.redirect_content
    ) {
      console.log("🔄 Custom redirect detected for site:", siteData.name);

      // Create redirect page
      const redirectTab = await chrome.tabs.create({
        url: chrome.runtime.getURL("redirect.html"),
        active: true,
      });

      // Send redirect data to the page
      setTimeout(() => {
        chrome.tabs.sendMessage(redirectTab.id, {
          type: "SETUP_REDIRECT",
          data: {
            title: siteData.redirect_title,
            content: siteData.redirect_content,
            targetUrl: siteData.redirect_url,
            siteName: siteData.name,
          },
        });
      }, 500);

      showStatus(`Mengalihkan ke ${siteData.name}`, "success");
      showFullPageLoading(false);
      return;
    }

    // If there's only one JSON file, load its cookies directly
    if (jsonFilesWithIndex.length === 1) {
      console.log(`📁 Site has one JSON file, loading cookies directly`);
      showFullPageLoading(
        true,
        `Memuat ${siteData.name} ${jsonFilesWithIndex[0].index}`,
        "Menyiapkan data"
      );

      try {
        const response = await fetch(
          `${API_BASE_URL}/json-files/${jsonFilesWithIndex[0].id}/cookies`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${currentToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const jsonFileData = await response.json();
          if (jsonFileData.success && jsonFileData.data.cookies) {
            // Update loading message with cookie count
            showFullPageLoading(
              true,
              `Memuat ${siteData.name}`,
              `Menyiapkan ${jsonFileData.data.cookies.length} Data`
            );

            siteData.cookies = jsonFileData.data.cookies;
            siteData.selectedJsonFile = jsonFilesWithIndex[0];
          } else {
            console.warn("JSON file has no valid cookies");
            siteData.cookies = [];
          }
        } else {
          console.warn("Failed to load cookies from JSON file");
          siteData.cookies = [];
        }
      } catch (error) {
        console.error("Error loading JSON file cookies:", error);
        siteData.cookies = [];
      }
    } else if (jsonFilesWithIndex.length === 0) {
      console.log(`📁 Site has no JSON files`);
      siteData.cookies = [];

      // Check for custom redirect first (when no JSON files)
      if (siteData.enable_custom_redirect && siteData.redirect_url) {
        console.log(
          "🔄 Custom redirect detected for site (no JSON files):",
          siteData.name
        );
        showFullPageLoading(
          true,
          `Mengalihkan ke ${siteData.name}...`,
          "Menyiapkan pengalihan kustom"
        );

        // Create custom redirect page
        const redirectTab = await chrome.tabs.create({
          url: chrome.runtime.getURL("redirect.html"),
          active: true,
        });

        // Send redirect data to the new tab
        setTimeout(() => {
          chrome.tabs.sendMessage(redirectTab.id, {
            action: "setupCustomRedirect",
            data: {
              title: siteData.redirect_title || siteData.name,
              content:
                siteData.redirect_content ||
                `Mengalihkan ke ${siteData.name}...`,
              redirectUrl: siteData.redirect_url,
              delay: siteData.redirect_delay || 3000,
            },
          });
        }, 1000);

        showFullPageLoading(false);
        showStatus(`Berhasil mengalihkan ke ${siteData.name}`, "success");
        return;
      }

      // Check for account injection (sites without JSON files but with login injection)
      if (
        siteData.login_url &&
        siteData.email_selector &&
        siteData.password_selector &&
        siteData.submit_selector
      ) {
        try {
          console.log("\n=== METODE INJEKSI: ACCOUNT INJECTION ===");
          console.log("🔑 Account injection detected for site:", siteData.name);
          console.log(
            "📋 Alasan: Tidak ada JSON files, menggunakan injeksi akun"
          );
          console.log("📊 Status JSON files:", {
            json_files_count: jsonFilesWithIndex.length,
            has_cookies: false,
            injection_method: "ACCOUNT_INJECTION",
          });
          console.log("📋 Injection config:", {
            login_url: siteData.login_url,
            email_selector: siteData.email_selector,
            password_selector: siteData.password_selector,
            submit_selector: siteData.submit_selector,
            has_additional_script: !!siteData.additional_script,
            has_js_after_submit: !!siteData.js_after_submit,
          });
          showFullPageLoading(
            true,
            `Melakukan injeksi akun ${siteData.name}...`,
            "Menyiapkan login otomatis"
          );

          // Open login URL and inject account
          const loginTab = await chrome.tabs.create({
            url: siteData.login_url,
            active: true,
          });

          // Get account credentials from API first
          let accountCredentials = null;

          try {
            const accountResponse = await fetch(
              `${API_BASE_URL}/site-accounts/${siteData.id}`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${currentToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (accountResponse.ok) {
              const accountData = await accountResponse.json();

              if (
                accountData.success &&
                accountData.data &&
                accountData.data.accounts &&
                accountData.data.accounts.length > 0
              ) {
                // Use the first available alternative account
                const firstAccount = accountData.data.accounts[0];

                if (firstAccount.email && firstAccount.password) {
                  accountCredentials = {
                    email: firstAccount.email,
                    password: firstAccount.password,
                  };
                  console.log("🔑 Using alternative account credentials");
                }
              }
            }
          } catch (error) {
            console.warn(
              "Error getting alternative account credentials:",
              error
            );
          }

          // Fallback to main site account if no alternative accounts
          if (!accountCredentials) {
            if (siteData.email && siteData.password) {
              accountCredentials = {
                email: siteData.email,
                password: siteData.password,
              };
              console.log("🔑 Using main site account credentials");
            } else {
              throw new Error(
                "No account credentials available (neither alternative nor main account)"
              );
            }
          }

          // Wait for page to load then inject login script
          setTimeout(async () => {
            try {
              await chrome.scripting.executeScript({
                target: { tabId: loginTab.id },
                func: injectAccountLogin,
                args: [
                  {
                    emailSelector: siteData.email_selector,
                    passwordSelector: siteData.password_selector,
                    submitSelector: siteData.submit_selector,
                    additionalScript: siteData.additional_script,
                    jsAfterSubmit: siteData.js_after_submit,
                    siteName: siteData.name,
                    email: accountCredentials.email,
                    password: accountCredentials.password,
                  },
                ],
              });

              showStatus(
                `Berhasil melakukan injeksi akun untuk ${siteData.name}`,
                "success"
              );
            } catch (error) {
              console.error("Error injecting account login:", error);
              showStatus(
                `Gagal melakukan injeksi akun untuk ${siteData.name}`,
                "error"
              );
            }
          }, 2000);
        } catch (error) {
          console.error("Error during account injection setup:", error);
          showStatus(
            `Gagal menyiapkan injeksi akun untuk ${siteData.name}: ${error.message}`,
            "error"
          );
          showFullPageLoading(false);
          return;
        }

        showFullPageLoading(false);
        return;
      } else {
        // Check if partial injection data exists
        const hasPartialInjection =
          siteData.login_url ||
          siteData.email_selector ||
          siteData.password_selector ||
          siteData.submit_selector;

        console.log("\n=== METODE INJEKSI: DIRECT SITE ACCESS ===");
        if (hasPartialInjection) {
          console.log(
            "⚠️ Partial injection data detected for site:",
            siteData.name
          );
          console.log("📋 Alasan: Konfigurasi injeksi akun tidak lengkap");
          console.log("📊 Status injeksi:", {
            json_files_count: jsonFilesWithIndex.length,
            has_cookies: false,
            injection_method: "DIRECT_ACCESS_PARTIAL_CONFIG",
            available_fields: {
              login_url: !!siteData.login_url,
              email_selector: !!siteData.email_selector,
              password_selector: !!siteData.password_selector,
              submit_selector: !!siteData.submit_selector,
            },
          });
          showStatus(
            `${siteData.name} memiliki konfigurasi injeksi yang tidak lengkap. Membuka situs langsung.`,
            "warning"
          );
        } else {
          console.log(
            "📄 No JSON files or injection data, opening site directly:",
            siteData.name
          );
          console.log(
            "📋 Alasan: Tidak ada JSON files dan tidak ada konfigurasi injeksi akun"
          );
          console.log("📊 Status injeksi:", {
            json_files_count: jsonFilesWithIndex.length,
            has_cookies: false,
            injection_method: "DIRECT_ACCESS_NO_CONFIG",
          });
          showStatus(`Membuka ${siteData.name}`, "success");
        }

        chrome.tabs.create({ url: siteData.url, active: true });
        showFullPageLoading(false);
        return;
      }
    }

    // Proceed with cookie injection if we have cookies
    if (
      siteData.cookies &&
      Array.isArray(siteData.cookies) &&
      siteData.cookies.length > 0
    ) {
      console.log("\n=== METODE INJEKSI: COOKIE INJECTION ===");
      console.log("🍪 Cookie injection detected for site:", siteData.name);
      console.log("📋 Alasan: JSON files tersedia dengan cookies");
      console.log("📊 Status cookies:", {
        cookies_count: siteData.cookies.length,
        has_json_files: jsonFilesWithIndex.length > 0,
        injection_method: "COOKIE_INJECTION",
      });

      // Validate and clean cookies data
      siteData.cookies = siteData.cookies.filter((cookie) => {
        // Basic validation
        if (!cookie.name || cookie.value === undefined) {
          console.warn(
            "Skipping invalid cookie (missing name or value):",
            cookie
          );
          return false;
        }

        // Ensure domain is set
        if (!cookie.domain && siteData.domain) {
          cookie.domain = siteData.domain;
          console.log(`Set domain for cookie ${cookie.name}: ${cookie.domain}`);
        }

        return true;
      });
      console.log(
        `🍪 Starting direct cookie overwrite for ${siteData.name} (${siteData.cookies.length} cookies)`
      );
      showFullPageLoading(
        true,
        `Menyiapkan aplikasi...`,
        `Memproses ${siteData.cookies.length} data untuk ${siteData.name}`
      );
      showStatus(
        `Memproses ${siteData.cookies.length} data untuk ${siteData.name}...`,
        "success"
      );

      // DISABLED: Cookie clearing to prevent looping
      // Using direct overwrite strategy instead
      console.log("🔄 Using direct overwrite strategy - no clearing needed");
      console.log("📋 Cookies to set:", siteData.cookies);

      // Set cookies
      let successCount = 0;
      let errorCount = 0;

      // Try to use background script for better performance
      try {
        console.log(
          "📤 Sending cookies to background script for bulk setting..."
        );
        const response = await chrome.runtime.sendMessage({
          action: "setCookies",
          cookies: siteData.cookies,
          domain: siteData.domain,
        });

        if (response && response.success) {
          successCount = response.result.success;
          errorCount = response.result.failed;
          console.log("✅ Bulk cookie setting result:", response.result);
        } else {
          throw new Error(response?.error || "Background script failed");
        }
      } catch (error) {
        console.warn(
          "⚠️ Background script failed, falling back to individual setting:",
          error
        );

        // Fallback to individual cookie setting
        console.log(
          "🔄 Using fallback method - setting cookies individually..."
        );
        for (let i = 0; i < siteData.cookies.length; i++) {
          const cookie = siteData.cookies[i];
          console.log(
            `🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`,
            cookie
          );

          try {
            await setCookieWithExtensionAPI(cookie, siteData.domain);
            successCount++;
            console.log(`✅ Cookie ${cookie.name} set successfully`);
          } catch (error) {
            errorCount++;
            console.error("❌ Cookie set error:", error);
            console.warn("📋 Cookie data:", cookie);
          }
        }
      }

      // Show detailed results
      if (successCount > 0) {
        const statusMessage = `${successCount}/${
          siteData.cookies.length
        } data berhasil diproses${
          errorCount > 0 ? ` (${errorCount} gagal)` : ""
        }. Membuka ${siteData.name}...`;
        showStatus(statusMessage, errorCount > 0 ? "warning" : "success");
        console.log(
          `🎯 Manual cookie import completed: ${successCount} success, ${errorCount} failed`
        );
      } else {
        showStatus(
          `Gagal memproses semua data. Membuka ${siteData.name} tanpa data tambahan...`,
          "error"
        );
        console.error("❌ All cookies failed to set");
      }

      // Add small delay before opening site to ensure cookies are properly set
      console.log(
        "⏳ Waiting 300ms before opening site to ensure cookies are set..."
      );
      await new Promise((resolve) => setTimeout(resolve, 300));
    } else {
      showStatus(`Membuka ${siteData.name}...`, "success");
      console.log("ℹ️ No cookies to set for this site - opening directly");
    }

    // Open site in new tab
    showFullPageLoading(
      true,
      `Membuka ${siteData.name}...`,
      "Menyiapkan tab baru"
    );

    try {
      console.log(`🌐 Opening site in new tab: ${siteData.url}`);
      chrome.tabs.create({ url: siteData.url });
      console.log("✅ Site opened successfully");

      // Add small delay to show completion
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.warn("⚠️ Chrome tabs API error:", error);
      // Fallback: open in current window
      console.log("🔄 Using fallback method to open site...");
      window.open(siteData.url, "_blank");
    }

    // Call the function to inject cookies and open site
    await injectCookieAndOpenSite(siteData);
  } catch (error) {
    console.error("❌ Manual cookie import error:", error);

    // Hide loading on error
    showFullPageLoading(false);

    if (
      error.message &&
      error.message.includes("Extension context invalidated")
    ) {
      console.error("🔄 Extension context invalidated - reload required");
      showStatus("Ekstensi perlu di-reload untuk membuka situs.", "error");
      showTroubleshooting();
    } else {
      console.error(`💥 Unexpected error: ${error.message}`);
      showStatus(`Error: ${error.message}`, "error");
    }
  }
}

/**
 * Inject cookies and open site
 * @param {Object} siteData - Site data with cookies
 */
async function injectCookieAndOpenSite(siteData) {
  try {
    // Set cookies
    let successCount = 0;
    let errorCount = 0;

    // Try to use background script for better performance
    try {
      console.log(
        "📤 Sending cookies to background script for bulk setting..."
      );
      const response = await chrome.runtime.sendMessage({
        action: "setCookies",
        cookies: siteData.cookies,
        domain: siteData.domain,
      });

      if (response && response.success) {
        successCount = response.result.success;
        errorCount = response.result.failed;
        console.log("✅ Bulk cookie setting result:", response.result);
      } else {
        throw new Error(response?.error || "Background script failed");
      }
    } catch (error) {
      console.warn(
        "⚠️ Background script failed, falling back to individual setting:",
        error
      );

      // Fallback to individual cookie setting
      console.log("🔄 Using fallback method - setting cookies individually...");
      for (let i = 0; i < siteData.cookies.length; i++) {
        const cookie = siteData.cookies[i];
        console.log(
          `🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`,
          cookie
        );

        try {
          await setCookieWithExtensionAPI(cookie, siteData.domain);
          successCount++;
          console.log(`✅ Cookie ${cookie.name} set successfully`);
        } catch (error) {
          errorCount++;
          console.error("❌ Cookie set error:", error);
          console.warn("📋 Cookie data:", cookie);
        }
      }
    }

    // Show detailed results
    if (successCount > 0) {
      const statusMessage = `${successCount}/${
        siteData.cookies.length
      } data berhasil diproses${
        errorCount > 0 ? ` (${errorCount} gagal)` : ""
      }. Membuka ${siteData.name}...`;
      showStatus(statusMessage, errorCount > 0 ? "warning" : "success");
      console.log(
        `🎯 Manual cookie import completed: ${successCount} success, ${errorCount} failed`
      );
    } else {
      showStatus(
        `Gagal memproses semua data. Membuka ${siteData.name} tanpa data tambahan...`,
        "error"
      );
      console.error("❌ All cookies failed to set");
    }

    // Add small delay before opening site to ensure cookies are properly set
    console.log(
      "⏳ Waiting 300ms before opening site to ensure cookies are set..."
    );
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Open site in new tab
    showFullPageLoading(
      true,
      `Membuka ${siteData.name}...`,
      "Menyiapkan tab baru"
    );

    try {
      console.log(`🌐 Opening site in new tab: ${siteData.url}`);
      chrome.tabs.create({ url: siteData.url });
      console.log("✅ Site opened successfully");

      // Add small delay to show completion
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.warn("⚠️ Chrome tabs API error:", error);
      // Fallback: open in current window
      console.log("🔄 Using fallback method to open site...");
      window.open(siteData.url, "_blank");
    }

    // Hide loading
    showFullPageLoading(false);
  } catch (error) {
    console.error("❌ Error injecting cookies and opening site:", error);
    showFullPageLoading(false);
    showStatus(`Error: ${error.message}`, "error");
  }
}

// showLoginScreen function removed - authentication now handled through website

function showMainScreen() {
  // Pastikan main screen ditampilkan dengan display flex
  mainScreen.style.display = "flex";

  // Hanya ubah display loginScreen jika opasitasnya bukan 0
  // (jika tidak sedang dalam animasi fade-out)
  if (loginScreen.style.opacity !== "0") {
    loginScreen.style.display = "none";
  }

  // Update user info
  if (currentUser) {
    userName.textContent = currentUser.name;
    userRole.textContent = currentUser.role;
    userRole.className = `role-badge ${currentUser.role}`;
  }

  // Pastikan elemen-elemen UI diinisialisasi
  if (!sitesList) {
    sitesList = document.getElementById("sitesList");
  }

  if (!filterTabs) {
    filterTabs = document.getElementById("filterTabs");
  }

  if (!searchInput) {
    searchInput = document.getElementById("searchInput");
  }

  // Update app version
  updateAppVersion();

  // Load important info for scrolling text
  loadImportantInfo();

  // Update subscription info
  updateSubscriptionInfo();

  console.log("Main screen displayed, UI elements initialized");
}

// showLoginStatus function removed - no longer needed with website-based authentication

function showStatus(message, type) {
  if (statusDiv) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = "block";

    setTimeout(() => {
      statusDiv.style.display = "none";
    }, 3000);
  }
}

function showTroubleshooting() {
  if (troubleshootingDiv) {
    troubleshootingDiv.style.display = "block";
  }
}

function hideTroubleshooting() {
  if (troubleshootingDiv) {
    troubleshootingDiv.style.display = "none";
  }
}

// Update UI based on authentication status
function updateUI() {
  if (currentUser && currentToken) {
    // Show main screen
    if (loginScreen) loginScreen.style.display = "none";
    if (mainScreen) mainScreen.style.display = "block";

    // Update user info
    if (userName) userName.textContent = currentUser.name;
    if (userRole) {
      userRole.textContent =
        currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);
      userRole.className = `role-badge ${currentUser.role}`;
    }

    // Update subscription info
    updateSubscriptionInfo();

    // Update app version
    updateAppVersion();

    // Pastikan elemen sitesList ada
    if (!sitesList) {
      sitesList = document.getElementById("sitesList");
      if (!sitesList) {
        console.error("Element sitesList tidak ditemukan di updateUI");
        const sitesSection = document.getElementById("sitesSection");
        if (sitesSection) {
          sitesSection.innerHTML =
            '<div class="sites-grid" id="sitesList"></div>';
          sitesList = document.getElementById("sitesList");
        }
      }
    }

    // Load sites jika belum ada data
    if (!availableSites || availableSites.length === 0) {
      loadSites();
    } else {
      // Jika sudah ada data, langsung render
      console.log("Rendering existing sites:", availableSites.length);
      renderSites();
    }

    // Ensure scrolling works properly
    enableSmoothScrolling();
  } else {
    // Show login screen
    if (loginScreen) loginScreen.style.display = "block";
    if (mainScreen) mainScreen.style.display = "none";
  }
}

function updateSubscriptionInfo() {
  if (!currentUser || !subscriptionInfo) return;

  if (currentUser.subscription_expires_at) {
    // Parse tanggal dengan format yang benar dari server (YYYY-MM-DD HH:mm:ss)
    const expiryDate = new Date(
      currentUser.subscription_expires_at.replace(" ", "T")
    );
    const now = new Date();

    // Validasi apakah tanggal valid
    if (isNaN(expiryDate.getTime())) {
      console.error(
        "Invalid subscription expiry date:",
        currentUser.subscription_expires_at
      );
      subscriptionInfo.textContent = "Tanggal tidak valid";
      subscriptionInfo.className = "subscription-info danger";
      return;
    }

    // Hitung selisih hari dengan lebih akurat
    const timeDiff = expiryDate.getTime() - now.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    let subscriptionText = "";
    let className = "";

    if (daysLeft < 0) {
      subscriptionText = "Langganan berakhir";
      className = "danger";
    } else if (daysLeft === 0) {
      subscriptionText = "Berakhir hari ini";
      className = "danger";
    } else if (daysLeft <= 7) {
      subscriptionText = `${daysLeft} hari lagi`;
      className = "warning";
    } else if (daysLeft > 365) {
      // Jika lebih dari 1 tahun, tampilkan sebagai unlimited dengan tanggal berakhir
      const options = { year: "numeric", month: "short", day: "numeric" };
      const formattedDate = expiryDate.toLocaleDateString("id-ID", options);
      subscriptionText = `Unlimited (${formattedDate})`;
      className = "";
    } else {
      subscriptionText = `${daysLeft} hari lagi`;
      className = "";
    }

    subscriptionInfo.textContent = subscriptionText;
    subscriptionInfo.className = `subscription-info ${className}`;
  } else {
    // Jika tidak ada tanggal berakhir, cek status langganan
    if (
      currentUser.subscription_status === "active" ||
      currentUser.subscription_plan
    ) {
      subscriptionInfo.textContent = `Unlimited (${
        currentUser.subscription_plan || "Active"
      })`;
    } else {
      subscriptionInfo.textContent = "Unlimited";
    }
    subscriptionInfo.className = "subscription-info";
  }
}

function updateAppVersion() {
  if (!appVersion) return;

  // Get version from manifest
  const manifest = chrome.runtime.getManifest();
  if (manifest && manifest.version) {
    appVersion.textContent = `Versi ${manifest.version}`;
  }
}

// Storage functions
// saveAuth and getStoredAuth functions removed - authentication now handled through website session

async function clearStoredAuth() {
  return new Promise((resolve, reject) => {
    try {
      // Clear all possible auth data keys for compatibility
      const authKeysToRemove = [
        "satuPintuAuth", // From popup.js (backward compatibility)
        "satu_pintu_user_auth", // From website localStorage
        "satu_pintu_logged_in", // From website localStorage
      ];

      console.log("🗑️ [POPUP] Clearing auth data keys:", authKeysToRemove);

      chrome.storage.local.remove(authKeysToRemove, () => {
        if (chrome.runtime.lastError) {
          console.warn(
            "Storage clear error:",
            chrome.runtime.lastError.message
          );
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          console.log(
            "✅ [POPUP] Auth data cleared successfully:",
            authKeysToRemove
          );
          resolve();
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      reject(new Error("Extension context invalidated"));
    }
  });
}

// Full page loading animation
function showFullPageLoading(
  show,
  message = "Memuat data situs...",
  subtext = "Mohon tunggu sebentar"
) {
  if (fullPageLoader) {
    if (show) {
      // Update loading text
      const loaderText = fullPageLoader.querySelector(".loader-text");
      const loaderSubtext = fullPageLoader.querySelector(".loader-subtext");
      if (loaderText) loaderText.textContent = message;
      if (loaderSubtext) loaderSubtext.textContent = subtext;

      fullPageLoader.style.display = "flex";
      // Disable all interactive elements
      if (loginBtn) loginBtn.disabled = true;
      if (logoutBtn) logoutBtn.disabled = true;
      if (emailInput) emailInput.disabled = true;
      if (passwordInput) passwordInput.disabled = true;
    } else {
      fullPageLoader.style.display = "none";
      // Re-enable interactive elements
      if (loginBtn) loginBtn.disabled = false;
      if (logoutBtn) logoutBtn.disabled = false;
      if (emailInput) emailInput.disabled = false;
      if (passwordInput) passwordInput.disabled = false;
    }
  }
}

// Get cached data from background script
async function getCachedData() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(
      {
        action: "getCachedData",
      },
      (response) => {
        if (response && response.userSession) {
          console.log(
            "Retrieved cached session:",
            response.userSession.sessionId
          );
          console.log(
            "Cached sites count:",
            response.sitesData?.data?.length || 0
          );
          console.log(
            "Cached cookies files:",
            Object.keys(response.cookiesFolder || {}).length
          );
          resolve(response);
        } else {
          resolve(null);
        }
      }
    );
  });
}

// Footer button handlers
function setupFooterHandlers() {
  const helpBtn = document.getElementById("helpBtn");
  const infoFaqBtn = document.getElementById("infoFaqBtn");

  if (helpBtn) {
    helpBtn.addEventListener("click", function () {
      showHelpModal();
    });
  }

  if (infoFaqBtn) {
    infoFaqBtn.addEventListener("click", function () {
      showInfoFaqModal();
    });
  }

  // Load important info for scrolling text
  loadImportantInfo();
}

// Load important info from API
async function loadImportantInfo() {
  console.log("Loading important info from API...");

  try {
    const url = `${API_BASE_URL}/important-info`;
    console.log("Fetching from URL:", url);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    console.log("API Response status:", response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log("API Response data:", data);

      if (data.success && data.data) {
        console.log("Found important info data, updating scrolling text");
        updateScrollingText(data.data);
        return;
      } else {
        console.warn("API response success but no data found:", data);
      }
    } else {
      console.warn(
        "Failed to load important info - HTTP",
        response.status,
        response.statusText
      );

      // Only log error text if it's not HTML (to avoid long HTML dumps)
      try {
        const errorText = await response.text();
        if (errorText.length < 500 && !errorText.includes("<!DOCTYPE")) {
          console.warn("Error response:", errorText);
        } else {
          console.warn("Error response: HTML error page (truncated)");
        }
      } catch (e) {
        console.warn("Could not read error response text");
      }
    }
  } catch (error) {
    console.error("Error loading important info:", error);
  }

  // Use default text if API fails or no data
  console.log("Using default text for scrolling info");
  updateScrollingText(null);
}

// Update scrolling text content
function updateScrollingText(importantInfoData) {
  const scrollingTextElement = document.getElementById("scrollingText");
  if (!scrollingTextElement) return;

  let scrollingContent = "";

  // Try to find active important info from database
  if (importantInfoData && Array.isArray(importantInfoData)) {
    const activeInfo = importantInfoData.find((info) => info.is_active);

    if (activeInfo) {
      // Format the content for scrolling text
      if (activeInfo.title) {
        scrollingContent += `📢 ${activeInfo.title}`;
      }

      if (activeInfo.content) {
        if (scrollingContent) scrollingContent += ": ";
        scrollingContent += activeInfo.content;
      }

      // Add additional info if available
      if (activeInfo.additional_info) {
        scrollingContent += ` • ${activeInfo.additional_info}`;
      }

      console.log(
        "Updated scrolling text with important info:",
        activeInfo.title
      );
    }
  }

  // Use default text if no active info found
  if (!scrollingContent) {
    scrollingContent =
      "📢 Informasi Penting: Pastikan ekstensi selalu diperbarui untuk mendapatkan fitur terbaru dan keamanan optimal • Hubungi support jika mengalami kendala • Gunakan fitur dengan bijak";
    console.log("Using default scrolling text");
  }

  // Update the scrolling text
  scrollingTextElement.textContent = scrollingContent;
}

function showHelpModal() {
  const modal = createModal(
    "Bantuan",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">🚀 Cara Menggunakan Ekstensi</h4>
      <ol style="margin: 10px 0; padding-left: 20px;">
        <li>Login dengan akun Satu Pintu Anda</li>
        <li>Pilih aplikasi yang ingin diakses</li>
        <li>Klik untuk otomatis login dan buka aplikasi</li>
      </ol>

      <h4 style="color: #4f46e5;">🔧 Troubleshooting</h4>
      <ul style="margin: 10px 0; padding-left: 20px;">
        <li>Jika login gagal, periksa koneksi internet</li>
        <li>Pastikan ekstensi memiliki izin yang diperlukan</li>
        <li>Coba reload ekstensi jika tidak responsif</li>
        <li>Hubungi support jika masalah berlanjut</li>
      </ul>

      <h4 style="color: #4f46e5;">📞 Kontak Support</h4>
      <p style="margin: 10px 0;">Email: <EMAIL><br>
      WhatsApp: +62 xxx-xxxx-xxxx</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function showInfoFaqModal() {
  const modal = createModal(
    "Informasi & FAQ",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">ℹ️ Tentang Ekstensi</h4>
      <p style="margin: 10px 0;">Ekstensi Satu Pintu memungkinkan Anda mengakses berbagai aplikasi dengan satu kali login. Ekstensi ini menggunakan teknologi cookie injection untuk memberikan pengalaman seamless.</p>

      <h4 style="color: #4f46e5;">❓ FAQ</h4>
      <div style="margin: 10px 0;">
        <strong>Q: Apakah data saya aman?</strong><br>
        A: Ya, semua data dienkripsi dan disimpan secara lokal di browser Anda.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Mengapa perlu login ulang?</strong><br>
        A: Untuk keamanan, session akan expired setelah periode tertentu.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Aplikasi tidak muncul?</strong><br>
        A: Pastikan Anda memiliki akses ke aplikasi tersebut dan langganan masih aktif.
      </div>

      <h4 style="color: #4f46e5;">🔒 Keamanan</h4>
      <p style="margin: 10px 0;">Ekstensi ini menggunakan enkripsi end-to-end dan tidak menyimpan password Anda. Semua komunikasi menggunakan HTTPS.</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function createModal(title, content) {
  const modal = document.createElement("div");
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  `;

  const modalContent = document.createElement("div");
  modalContent.style.cssText = `
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 16px;
    padding: 24px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    color: white;
    position: relative;
  `;

  modalContent.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h3 style="margin: 0; font-size: 18px; font-weight: 600;">${title}</h3>
      <button id="closeModal" style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        width: 32px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      ">×</button>
    </div>
    <div>${content}</div>
  `;

  modal.appendChild(modalContent);

  // Close modal handlers
  const closeBtn = modalContent.querySelector("#closeModal");
  closeBtn.addEventListener("click", () => modal.remove());

  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });

  return modal;
}

// Get site cookies
async function getSiteCookies(siteId) {
  try {
    // First try to get cookies from cache via background script
    const cachedCookiesResponse = await new Promise((resolve) => {
      chrome.runtime.sendMessage(
        {
          action: "getCachedCookiesForSite",
          siteId: siteId,
        },
        resolve
      );
    });

    if (
      cachedCookiesResponse &&
      cachedCookiesResponse.success &&
      cachedCookiesResponse.data
    ) {
      console.log(
        `Using cached cookies for site ID: ${siteId} (${cachedCookiesResponse.data.cookies.length} cookies)`
      );
      return {
        cookies: cachedCookiesResponse.data.cookies,
        domain: cachedCookiesResponse.data.domain,
        name: cachedCookiesResponse.data.siteName,
      };
    }

    // Fallback to API if not in cache
    console.log("Fetching site details from API for site ID:", siteId);

    const response = await fetch(`${API_BASE_URL}/site-details/${siteId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch site details: ${response.status}`);
    }

    const siteDetailData = await response.json();
    console.log("Fetched site details from API:", siteDetailData);

    if (!siteDetailData.success || !siteDetailData.data) {
      throw new Error("Invalid site details response");
    }

    const site = siteDetailData.data.site;
    const jsonFiles = siteDetailData.data.json_files || [];

    // If no JSON files, return empty cookies
    if (jsonFiles.length === 0) {
      return {
        cookies: [],
        domain: site.domain || site.url,
        name: site.name,
      };
    }

    // If only one JSON file, get its cookies directly
    if (jsonFiles.length === 1) {
      const jsonFile = jsonFiles[0];
      if (jsonFile.cookies && Array.isArray(jsonFile.cookies)) {
        return {
          cookies: jsonFile.cookies,
          domain: site.domain || site.url,
          name: site.name,
        };
      }
    }

    // If multiple JSON files, throw error to indicate selection needed
    throw new Error("Multiple JSON files found - selection required");
  } catch (error) {
    console.error("Error getting site cookies:", error);
    throw error;
  }
}

async function injectCookiesAndOpenSite(site) {
  try {
    // Validate site data
    if (!site || !site.id || !site.url) {
      throw new Error("Invalid site data");
    }

    // Show initial loading
    showFullPageLoading(
      true,
      `Menyiapkan ${site.name}...`,
      "Mengambil data cookies"
    );
    showStatus(`Memuat data untuk ${site.name}...`, "success");

    // Get cookies for the site
    const siteData = await getSiteCookies(site.id);

    if (!siteData || !siteData.cookies || !siteData.domain) {
      throw new Error("Tidak dapat memuat data cookies");
    }

    // Set cookies
    let successCount = 0;
    let errorCount = 0;

    // Try to use background script for better performance
    try {
      console.log(
        "📤 Sending cookies to background script for bulk setting..."
      );
      const response = await chrome.runtime.sendMessage({
        action: "setCookies",
        cookies: siteData.cookies,
        domain: siteData.domain,
      });

      if (response && response.success) {
        successCount = response.result.success;
        errorCount = response.result.failed;
        console.log("✅ Bulk cookie setting result:", response.result);
      } else {
        throw new Error(response?.error || "Background script failed");
      }
    } catch (error) {
      console.warn(
        "⚠️ Background script failed, falling back to individual setting:",
        error
      );

      // Fallback to individual cookie setting
      console.log("🔄 Using fallback method - setting cookies individually...");
      for (let i = 0; i < siteData.cookies.length; i++) {
        const cookie = siteData.cookies[i];
        console.log(
          `🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`,
          cookie
        );

        try {
          await setCookieWithExtensionAPI(cookie, siteData.domain);
          successCount++;
          console.log(`✅ Cookie ${cookie.name} set successfully`);
        } catch (error) {
          errorCount++;
          console.error("❌ Cookie set error:", error);
          console.warn("📋 Cookie data:", cookie);
        }
      }
    }

    // Show detailed results
    if (successCount > 0) {
      const statusMessage = `${successCount}/${
        siteData.cookies.length
      } data berhasil diproses${
        errorCount > 0 ? ` (${errorCount} gagal)` : ""
      }. Membuka ${site.name}...`;
      showStatus(statusMessage, errorCount > 0 ? "warning" : "success");
      console.log(
        `🎯 Cookie import completed: ${successCount} success, ${errorCount} failed`
      );
    } else {
      showStatus(
        `Gagal memproses semua data. Membuka ${site.name} tanpa data tambahan...`,
        "error"
      );
      console.error("❌ All cookies failed to set");
    }

    // Add small delay before opening site to ensure cookies are properly set
    console.log(
      "⏳ Waiting 300ms before opening site to ensure cookies are set..."
    );
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Open site in new tab
    showFullPageLoading(true, `Membuka ${site.name}...`, "Menyiapkan tab baru");

    try {
      console.log(`🌐 Opening site in new tab: ${site.url}`);
      chrome.tabs.create({ url: site.url });
      console.log("✅ Site opened successfully");

      // Add small delay to show completion
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.warn("⚠️ Chrome tabs API error:", error);
      // Fallback: open in current window
      console.log("🔄 Using fallback method to open site...");
      window.open(site.url, "_blank");
    }

    // Hide loading
    showFullPageLoading(false);
  } catch (error) {
    console.error("Error injecting cookies and opening site:", error);
    showFullPageLoading(false);
    showStatus(`Error: ${error.message}`, "error");
  }
}

// Test server connection function
// testServerConnectionBeforeLogin function removed - no longer needed with website-based authentication

async function testServerConnection() {
  const testBtn = document.getElementById("testServerBtn");
  const resultDiv = document.getElementById("serverTestResult");

  if (!testBtn || !resultDiv) return;

  // Disable button and show loading
  testBtn.disabled = true;
  resultDiv.innerHTML =
    '<div style="color: #fbbf24;">🔄 Menguji koneksi ke server...</div>';

  try {
    // Test server connection directly without using removed function
    const response = await fetch(`${API_BASE_URL}/test-connection`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    });

    if (response.ok) {
      resultDiv.innerHTML = `<div style="color: #10b981;">✅ Server connection successful</div>`;
    } else {
      resultDiv.innerHTML = `<div style="color: #ef4444;">❌ Server connection failed</div>`;
    }
  } catch (error) {
    console.error("Server test error:", error);
    resultDiv.innerHTML = `<div style="color: #ef4444;">❌ Error: ${error.message}</div>`;
  } finally {
    // Re-enable button
    testBtn.disabled = false;
  }
}

async function setCookieWithExtensionAPI(cookie, siteDomain) {
  return new Promise((resolve, reject) => {
    // Validate required fields
    if (!cookie.name || cookie.value === undefined) {
      reject(new Error("Cookie name and value are required"));
      return;
    }

    // Clean siteDomain (remove protocol if present)
    const cleanSiteDomain = siteDomain
      .replace(/^https?:\/\//, "")
      .replace(/\/.*$/, "");

    // Determine protocol based on secure flag
    const protocol = cookie.secure !== false ? "https" : "http";

    // Prepare cookie for Chrome API
    const cookieDetails = {
      url: `${protocol}://${cleanSiteDomain}`,
      name: cookie.name,
      value: cookie.value,
    };

    console.log(`Using site domain: ${cleanSiteDomain}, protocol: ${protocol}`);

    // Handle domain - prioritize cookie.domain, fallback to siteDomain
    if (cookie.domain) {
      // Clean domain (remove protocol if present)
      let cleanDomain = cookie.domain
        .replace(/^https?:\/\//, "")
        .replace(/\/.*$/, "");

      // If domain doesn't start with dot and is not the exact siteDomain, add dot for subdomain support
      if (!cleanDomain.startsWith(".") && cleanDomain !== cleanSiteDomain) {
        cookieDetails.domain = `.${cleanDomain}`;
      } else {
        cookieDetails.domain = cleanDomain;
      }
    } else {
      cookieDetails.domain = cleanSiteDomain;
    }

    console.log(
      `Domain mapping: ${cookie.domain || "undefined"} -> ${
        cookieDetails.domain
      }`
    );

    // Handle path
    cookieDetails.path = cookie.path || "/";

    // Handle secure flag
    if (cookie.secure !== undefined) {
      cookieDetails.secure = cookie.secure;
    } else {
      // Default to true for HTTPS sites
      cookieDetails.secure = true;
    }

    // Handle httpOnly flag
    if (cookie.httpOnly !== undefined) {
      cookieDetails.httpOnly = cookie.httpOnly;
    }

    // Handle sameSite
    if (cookie.sameSite) {
      const validSameSite = ["unspecified", "no_restriction", "lax", "strict"];
      if (validSameSite.includes(cookie.sameSite)) {
        cookieDetails.sameSite = cookie.sameSite;
      } else if (cookie.sameSite === "none") {
        cookieDetails.sameSite = "no_restriction";
      } else {
        // Default mapping for common values
        cookieDetails.sameSite = "lax";
      }
    }

    // Handle expiration date
    if (cookie.expirationDate) {
      // Handle both Unix timestamp and Date object
      if (typeof cookie.expirationDate === "number") {
        cookieDetails.expirationDate = cookie.expirationDate;
      } else if (cookie.expirationDate instanceof Date) {
        cookieDetails.expirationDate = Math.floor(
          cookie.expirationDate.getTime() / 1000
        );
      }
    } else {
      // Set expiration to 1 year from now if not specified
      cookieDetails.expirationDate =
        Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60;
    }

    console.log("Setting cookie:", cookieDetails);

    // Function to attempt setting cookie
    const attemptSetCookie = (details, isRetry = false) => {
      try {
        chrome.cookies.set(details, (result) => {
          if (chrome.runtime.lastError) {
            console.warn("Cookie set error:", chrome.runtime.lastError.message);
            console.warn("Cookie details:", details);

            // If HTTPS failed and this is not a retry, try with HTTP
            if (!isRetry && details.url.startsWith("https://")) {
              console.log("Retrying with HTTP...");
              const httpDetails = { ...details };
              httpDetails.url = details.url.replace("https://", "http://");
              httpDetails.secure = false;
              attemptSetCookie(httpDetails, true);
            } else {
              reject(new Error(chrome.runtime.lastError.message));
            }
          } else {
            console.log("Cookie set successfully:", result);
            resolve(result);
          }
        });
      } catch (error) {
        console.warn("Chrome cookies API error:", error);
        reject(new Error("Extension context invalidated"));
      }
    };

    // Start the attempt
    attemptSetCookie(cookieDetails);
  });
}
