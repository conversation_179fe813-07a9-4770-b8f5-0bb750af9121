<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        /* Reset dan Base Styles */
        * {
            box-sizing: border-box;
        }

        .text-red-600 {
            color: #DC2626;
        }

        .text-yellow-600 {
            color: #F59E0B;
        }

        .text-green-600 {
            color: #22C55E;
        }

        /* Container Utama */
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Header Section */
        .dashboard-header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            text-align: center;
        }

        .header-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .logout-btn {
            background: #DC2626;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .logout-btn:hover {
            background: #B91C1C;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        /* Grid Layout untuk Desktop */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        /* Extension Cards */
        .extension-card {
            background: linear-gradient(135deg, #462c2c, #705656);
            color: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .extension-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
        }

        .extension-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .extension-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .extension-card p {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .download-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            flex: 1;
            min-width: 150px;
            justify-content: center;
        }

        .download-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .extension-version {
            font-size: 0.875rem;
            opacity: 0.8;
            display: block;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            border-left: 3px solid rgba(255, 255, 255, 0.3);
        }

        .no-extension-message {
            font-style: italic;
            opacity: 0.8;
            text-align: center;
            padding: 2rem;
        }

        /* Status Cards Grid */
        .status-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), transparent);
        }

        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .status-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
            position: relative;
        }

        .status-icon svg {
            width: 1.5rem;
            height: 1.5rem;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }

        .status-icon .text-green-400 {
            color: #4ade80;
        }

        .status-icon .text-red-400 {
            color: #f87171;
        }

        .status-icon .text-blue-400 {
            color: #60a5fa;
        }

        .status-icon .text-orange-400 {
            color: #fb923c;
        }

        .status-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0.75rem;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .status-card:hover .status-icon::before {
            opacity: 1;
        }

        .status-info h4 {
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin: 0 0 0.5rem 0;
            opacity: 0.8;
        }

        .status-info .status-value {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        /* Actions Section */
        .actions-section {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .actions-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            color: white;
            padding: 1.25rem 1.5rem;
            border-radius: 1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.25);
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .action-btn svg {
            width: 1.25rem;
            height: 1.25rem;
            flex-shrink: 0;
        }

        /* Warning Alert */
        .warning-alert {
            background: linear-gradient(135deg, #ff7e5f, #feb47b);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 25px rgba(255, 126, 95, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .warning-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .warning-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        .warning-text {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        .warning-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: inline-block;
            text-align: center;
        }

        .warning-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (min-width: 640px) {
            .dashboard-container {
                padding: 0 2rem;
            }

            .header-content {
                flex-direction: row;
                justify-content: space-between;
                text-align: left;
            }

            .header-title {
                font-size: 2rem;
            }

            .download-buttons {
                flex-wrap: nowrap;
            }

            .download-btn {
                flex: none;
                min-width: auto;
            }
        }

        @media (min-width: 768px) {
            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .actions-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            /* Posisikan tombol keempat di tengah baris kedua */
            .actions-grid .action-btn:nth-child(4) {
                grid-column: 2 / 3;
            }
        }

        @media (min-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Mobile App Card Specific */
        .mobile-app-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 1rem 0;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dashboard-container>* {
            animation: fadeInUp 0.6s ease-out;
        }

        .dashboard-container>*:nth-child(2) {
            animation-delay: 0.1s;
        }

        .dashboard-container>*:nth-child(3) {
            animation-delay: 0.2s;
        }

        .dashboard-container>*:nth-child(4) {
            animation-delay: 0.3s;
        }
    </style>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function openInfoModal(id, title, content, type) {
            // Map type to SweetAlert2 icon
            let swalIcon = 'info';
            switch (type) {
                case 'info':
                    swalIcon = 'info';
                    break;
                case 'warning':
                    swalIcon = 'warning';
                    break;
                case 'error':
                    swalIcon = 'error';
                    break;
                case 'success':
                    swalIcon = 'success';
                    break;
                default:
                    swalIcon = 'info';
            }

            // Format content to handle line breaks
            const formattedContent = content.replace(/\\n/g, '<br>');

            // Show SweetAlert2 modal
            Swal.fire({
                title: title,
                html: `<div class="text-gray-700 leading-relaxed text-left">${formattedContent}</div>`,
                icon: swalIcon,
                confirmButtonText: 'Tutup',
                confirmButtonColor: '#6B7280',
                width: '600px',
                padding: '2rem',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp animate__faster'
                },
                customClass: {
                    popup: 'rounded-xl shadow-2xl',
                    title: 'text-xl font-bold text-gray-900 mb-4',
                    htmlContainer: 'text-base text-gray-700',
                    confirmButton: 'px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg'
                },
                backdrop: `
                rgba(0,0,0,0.4)
                url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23ffffff' fill-opacity='0.05'%3e%3ccircle cx='30' cy='30' r='4'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")
                center/100px 100px
            `,
                buttonsStyling: false
            });
        }

        // === EXTENSION AUTHENTICATION BRIDGE ===
        // Simpan informasi user untuk akses ekstensi
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Data user dari server dengan struktur yang konsisten untuk ekstensi
                const userData = {
                    id: <?php echo e($user->id); ?>,
                    user_id: <?php echo e($user->id); ?>, // Alias untuk konsistensi dengan backend
                    email: '<?php echo e($user->email); ?>',
                    name: '<?php echo e($user->name); ?>',
                    role: '<?php echo e($user->role); ?>',
                    subscription_status: '<?php echo e($user->subscription_status ?? 'inactive'); ?>',
                    subscription_active: <?php echo e($user->isSubscriptionActive() ? 'true' : 'false'); ?>,
                    subscription_plan: '<?php echo e($user->subscription_plan ?? 'none'); ?>',
                    subscription_expires_at: '<?php echo e($user->subscription_expires_at ?? null); ?>',
                    login_timestamp: new Date().getTime(),
                    session_key: '<?php echo e(session()->getId()); ?>',
                    session_token: '<?php echo e(csrf_token()); ?>',
                    timestamp: Date.now()
                };

                // Validasi data sebelum disimpan
                if (!userData.id || !userData.email) {
                    console.error('❌ Data user tidak lengkap');
                    return;
                }

                // Simpan ke localStorage untuk akses ekstensi
                localStorage.setItem('satu_pintu_user_auth', JSON.stringify(userData));

                // Only set extension version in localStorage if it doesn't exist yet
                if (!localStorage.getItem('satupintu_extension_known_version')) {
                    localStorage.setItem('satupintu_extension_known_version',
                        '<?php echo e($latestExtension->version ?? '0.0.0'); ?>');
                }

                // Simpan juga ke sessionStorage sebagai backup
                sessionStorage.setItem('satu_pintu_user_auth', JSON.stringify(userData));

                // Set flag bahwa user sudah login
                localStorage.setItem('satu_pintu_logged_in', 'true');
                sessionStorage.setItem('satu_pintu_logged_in', 'true');

                // Broadcast event untuk ekstensi yang mungkin sedang listening
                window.dispatchEvent(new CustomEvent('satuPintuUserLogin', {
                    detail: userData
                }));

                // Event tambahan untuk kompatibilitas dengan versi lama
                window.dispatchEvent(new CustomEvent('extensionUserLogin', {
                    detail: userData
                }));

                console.log('✅ Data autentikasi user berhasil disimpan untuk akses ekstensi');
                console.log('📋 Data yang disimpan:', userData);

            } catch (error) {
                console.error('❌ Gagal menyimpan data autentikasi:', error);
            }
        });

        // Cleanup saat logout (jika ada tombol logout di halaman ini)
        function handleLogout() {
            try {
                // Hapus data user dari storage
                localStorage.removeItem('satu_pintu_user_auth');
                localStorage.removeItem('satu_pintu_logged_in');
                localStorage.removeItem('extension_user_auth');
                localStorage.removeItem('extension_logged_in');
                sessionStorage.removeItem('satu_pintu_user_auth');
                sessionStorage.removeItem('satu_pintu_logged_in');
                sessionStorage.removeItem('extension_user_auth');
                sessionStorage.removeItem('extension_logged_in');

                // Broadcast logout event untuk ekstensi
                window.dispatchEvent(new CustomEvent('satuPintuUserLogout'));
                window.dispatchEvent(new CustomEvent('extensionUserLogout'));

                console.log('✅ Data autentikasi user berhasil dibersihkan');
            } catch (error) {
                console.error('❌ Gagal membersihkan data autentikasi:', error);
            }
        }

        // Attach logout handler ke tombol logout jika ada
        const logoutButton = document.querySelector('form[action*="logout"] button[type="submit"]');
        if (logoutButton) {
            logoutButton.addEventListener('click', handleLogout);
        }

        // --- PERUBAHAN: Pemeriksaan Token Pembayaran ---
        document.addEventListener('DOMContentLoaded', function() {
            // Handle payment token storage from session flash
            const paymentToken = localStorage.getItem('satupintu_payment_token');
            const paymentTokenCreatedAt = localStorage.getItem('satupintu_payment_token_created_at');
            const tokenRedirectUrl = '<?php echo e($token); ?>';
            const paymentSuccessToken = '<?php echo e($paymentSuccessToken ?? ''); ?>';

            // Check for webhook success token first (higher priority)
            if (paymentSuccessToken) {
                Swal.fire({
                    title: 'Pembayaran Berhasil!',
                    text: 'Terima kasih, pembayaran Anda telah berhasil diproses dan langganan telah diaktifkan.',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#22C55E'
                }).then(() => {
                    // Refresh halaman untuk memperbarui status langganan
                    window.location.reload();
                });
            }
            // Fallback to localStorage token check
            else if (tokenRedirectUrl && tokenRedirectUrl === paymentToken) {
                Swal.fire({
                    title: 'Pembayaran Berhasil!',
                    text: 'Terima kasih, pembayaran Anda telah berhasil diproses.',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#22C55E'
                });
                // Hapus token sebelumnya
                localStorage.removeItem('satupintu_payment_token');
                localStorage.removeItem('satupintu_payment_token_created_at');
            }
        });

        // Auto show warning if subscription is near expiry
        document.addEventListener('DOMContentLoaded', function() {
            <?php if($showWarning): ?>
                setTimeout(() => {
                    Swal.fire({
                        title: 'Peringatan Masa Aktif!',
                        html: `
                        <div class="text-center">
                            <p class="text-lg mb-4">Akun Anda akan berakhir dalam</p>
                            <div class="bg-red-100 text-red-800 text-2xl font-bold py-3 px-6 rounded-lg mb-4">
                                <?php echo e($daysLeft); ?> hari
                            </div>
                            <p class="text-gray-600 mb-4">Segera lakukan pembayaran untuk perpanjangan akun Anda.</p>
                        </div>
                    `,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Perpanjang Sekarang',
                        cancelButtonText: 'Nanti',
                        confirmButtonColor: '#EF4444'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '<?php echo e(route('payment.index')); ?>';
                        }
                    });
                }, 2000);
            <?php endif; ?>
        });

        // --- PERUBAHAN: Pemeriksaan Versi Ekstensi via localStorage ---
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk memeriksa versi ekstensi dan memicu notifikasi jika ada update
            function checkForExtensionUpdate() {
                // 1. Dapatkan versi terbaru dari server (dikirim dari Blade)
                const latestVersionFromServer = "<?php echo e($latestExtension->version ?? '0.0.0'); ?>";

                // Pastikan versi dari server valid
                if (!latestVersionFromServer || latestVersionFromServer === '0.0.0') {
                    console.log('[DASHBOARD] Versi ekstensi terbaru tidak tersedia dari server.');
                    return;
                }

                console.log('[DASHBOARD] Versi terbaru dari server:', latestVersionFromServer);

                // 2. Dapatkan versi yang terakhir diketahui oleh website dari localStorage
                const LOCAL_STORAGE_KEY = 'satupintu_extension_known_version';
                let knownVersion = localStorage.getItem(LOCAL_STORAGE_KEY);

                console.log('[DASHBOARD] Versi ekstensi yang diketahui website (dari localStorage):', knownVersion);

                // 3. Bandingkan versi
                if (knownVersion !== latestVersionFromServer) {
                    // Versi berbeda -> Ada update atau pertama kali kunjung
                    console.log('[DASHBOARD] Versi ekstensi berubah atau pertama kali. Menampilkan notifikasi...');

                    // 4. Tampilkan SweetAlert2
                    Swal.fire({
                        title: 'Update Ekstensi Tersedia!',
                        html: `
                    <p>Versi terbaru ekstensi browser Satu Pintu adalah <strong>${latestVersionFromServer}</strong>.</p>
                    <p class="mt-3">Silakan unduh dan pasang versi terbaru untuk mendapatkan fitur dan perbaikan terkini.</p>
                    `,
                        icon: 'info',
                        confirmButtonText: 'Mengerti',
                        confirmButtonColor: '#DB2777', // Warna aksen pink
                        allowOutsideClick: false, // Pengguna harus mengklik tombol
                        allowEscapeKey: false, // Pengguna harus mengklik tombol
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 5. Setelah pengguna mengklik "Mengerti", simpan versi terbaru ke localStorage
                            // Ini menandai bahwa pengguna sudah diberi tahu tentang versi ini.
                            try {
                                localStorage.setItem(LOCAL_STORAGE_KEY, latestVersionFromServer);
                                console.log('[DASHBOARD] Versi ekstensi terbaru disimpan ke localStorage:',
                                    latestVersionFromServer);
                            } catch (e) {
                                console.error('[DASHBOARD] Gagal menyimpan versi ke localStorage:', e);
                                // Jika gagal simpan, tidak apa-apa, notifikasi akan muncul lagi
                            }
                        }
                    });

                } else {
                    // Versi sama -> Tidak ada perubahan
                    console.log('[DASHBOARD] Versi ekstensi sudah yang terbaru (berdasarkan localStorage).');
                }
            }

            // Jalankan pengecekan setelah DOM siap
            // Kita bisa tambahkan sedikit jeda jika perlu
            setTimeout(checkForExtensionUpdate, 1000); // Tunggu 1 detik

        });
        // --- AKHIR PERUBAHAN ---
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section class="bg-darkPink-900 relative py-28 md:py-28" style="min-height: 100vh;">
        <div class="absolute top-0 left-0 w-full h-full"><img class="w-full h-full object-cover"
                src="<?php echo e(asset('assets/img/lines-dashboard.svg')); ?>" alt="" /></div>
        <div class="dashboard-container">
            <div class="mx-auto relative z-10">
                <div class="dashboard-header">
                    <div class="header-content">
                        <h1 class="header-title">
                            Selamat Datang, <?php echo e(auth()->user()->name); ?>

                        </h1>
                        <form action="<?php echo e(route('logout')); ?>" method="GET" class="m-0">
                            <button type="submit" class="logout-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                    <polyline points="16 17 21 12 16 7"></polyline>
                                    <line x1="21" y1="12" x2="9" y2="12"></line>
                                </svg>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>


                <!-- Status dan Informasi -->
                <div class="status-grid">
                    <!-- Status Langganan -->
                    <div class="status-card">
                        <div class="status-content">
                            <div class="status-icon">
                                <?php if(auth()->user()->subscription_status == 'active'): ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-green-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                <?php else: ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-red-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                <?php endif; ?>
                            </div>
                            <div class="status-info">
                                <h4>Status Langganan</h4>
                                <p
                                    class="status-value <?php echo e(auth()->user()->subscription_status == 'active' ? 'text-green-600' : 'text-red-600'); ?>">
                                    <?php if(auth()->user()->subscription_status == 'active'): ?>
                                        Aktif
                                    <?php else: ?>
                                        Non-Aktif
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Plan -->
                    <div class="status-card">
                        <div class="status-content">
                            <div class="status-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-blue-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            </div>
                            <div class="status-info">
                                <h4>Plan</h4>
                                <p class="status-value text-white">
                                    <?php echo e(auth()->user()->subscription_plan ?? 'Tidak Ada'); ?>

                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Status Pembayaran -->
                    <div class="status-card">
                        <div class="status-content">
                            <div class="status-icon">
                                <?php if(auth()->user()->subscription_status == 'active'): ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-green-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                <?php else: ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-red-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                <?php endif; ?>
                            </div>
                            <div class="status-info">
                                <h4>Pembayaran</h4>
                                <p
                                    class="status-value
                                <?php if(auth()->user()->payment_status === 'paid'): ?> text-green-600
                                <?php elseif(auth()->user()->payment_status === 'pending'): ?> text-yellow-600
                                <?php else: ?> text-red-600 <?php endif; ?>">
                                    <?php if(auth()->user()->payment_status === 'paid'): ?>
                                        Lunas
                                    <?php elseif(auth()->user()->payment_status === 'pending'): ?>
                                        Tidak Ada
                                    <?php else: ?>
                                        Belum Bayar
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Masa Berlaku -->
                    <div class="status-card">
                        <div class="status-content">
                            <div class="status-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-orange-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="status-info">
                                <h4>Masa Berlaku</h4>
                                <p class="status-value text-white">
                                    <?php if(auth()->user()->subscription_expires_at): ?>
                                        <?php
                                            $expiryDate = \Carbon\Carbon::parse(
                                                auth()->user()->subscription_expires_at,
                                            );
                                            $daysLeft = \Carbon\Carbon::now()
                                                ->startOfDay()
                                                ->diffInDays($expiryDate->startOfDay(), false);
                                        ?>
                                        <?php if($daysLeft < 7 && $daysLeft >= 0): ?>
                                            <?php echo e($daysLeft); ?> hari lagi
                                        <?php elseif($daysLeft < 0): ?>
                                            Sudah Berakhir
                                        <?php else: ?>
                                            <?php echo e($expiryDate->format('d/m/Y')); ?>

                                        <?php endif; ?>
                                    <?php else: ?>
                                        Tidak Ada
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="actions-section">
                    <h2 class="actions-title">Aksi Akun</h2>
                    <div class="actions-grid">
                        <a href="<?php echo e(route('user.profile.edit')); ?>" class="action-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            Edit Profile
                        </a>

                        <a href="<?php echo e(route('user.site-reports')); ?>" class="action-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 12l2 2 4-4"></path>
                                <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"></path>
                                <path d="M3 12v6c0 .552.448 1 1 1h16c.552 0 1-.448 1-1v-6"></path>
                            </svg>
                            Status Laporan Sites
                        </a>

                        <a href="<?php echo e(route('payment.index')); ?>" class="action-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                                <line x1="1" y1="10" x2="23" y2="10"></line>
                            </svg>
                            Pembelian
                        </a>

                        <a href="<?php echo e(route('payment.history')); ?>" class="action-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            Riwayat Pembelian
                        </a>
                    </div>
                </div>

                <?php if($showWarning): ?>
                    <div class="warning-alert">
                        <div class="warning-content">
                            <h3 class="warning-title">Pembayaran Hampir Kadaluarsa!</h3>
                            <p class="warning-text">Segera selesaikan pembayaran Anda untuk memperpanjang akses.</p>
                            <a href="<?php echo e(route('payment.index')); ?>" target="_blank" class="warning-btn">
                                Bayar Sekarang
                            </a>
                        </div>
                    </div>
                <?php endif; ?>


                
                <?php
                    $hasExtension = $latestExtension;
                    $hasMobileApp =
                        ($user->subscription_status === 'active' && $latestMobileApp) ||
                        $user->subscription_status !== 'active';
                    $bothAvailable = $hasExtension && $hasMobileApp;
                ?>

                <div class="dashboard-grid" style="margin-top: 4rem;">
                    <style>
                        .downloads-grid {
                            display: grid;
                            gap: 1.5rem;
                            align-items: stretch;
                        }

                        .downloads-grid.two-columns {
                            grid-template-columns: repeat(2, 1fr);
                        }

                        .downloads-grid.one-column {
                            grid-template-columns: 1fr;
                        }

                        .downloads-grid>div {
                            display: flex;
                            flex-direction: column;
                        }

                        .downloads-grid .extension-card {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                        }

                        @media (max-width: 768px) {
                            .downloads-grid.two-columns {
                                grid-template-columns: 1fr;
                            }
                        }
                    </style>
                    <div style="max-width: 72rem; margin: 0 auto;">
                        <div class="downloads-grid <?php echo e($bothAvailable ? 'two-columns' : 'one-column'); ?>">
                            
                            <?php if($latestExtension): ?>
                                <div style="<?php echo e($bothAvailable ? '' : 'max-width: 48rem; margin: 0 auto;'); ?>">
                                    <div class="extension-card">
                                        <h3>
                                            Ekstensi Browser
                                        </h3>
                                        <p>Unduh dan pasang ekstensi browser kami untuk akses cepat ke aplikasi premium Anda
                                            dengan
                                            keamanan terjamin.</p>
                                        <div class="download-buttons">
                                            <a href="<?php echo e(Storage::url($latestExtension->file_path)); ?>" target="_blank"
                                                download class="download-btn">
                                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                                                </svg>
                                                Unduh Ekstensi (v<?php echo e($latestExtension->version); ?>)
                                            </a>
                                            <a href="<?php echo e(Storage::url($latestExtension->file_guide)); ?>" target="_blank"
                                                class="download-btn">
                                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
                                                </svg>
                                                Panduan Instalasi
                                            </a>
                                        </div>
                                        <span class="extension-version">Versi terbaru: <?php echo e($latestExtension->version); ?> -
                                            <?php echo e($latestExtension->description ?? 'Ekstensi resmi Satu Pintu'); ?></span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div style="<?php echo e($bothAvailable ? '' : 'max-width: 48rem; margin: 0 auto;'); ?>">
                                    <div class="extension-card text-center">
                                        <h3>Ekstensi Browser</h3>
                                        <p class="no-extension-message">Ekstensi browser belum tersedia untuk diunduh saat
                                            ini.
                                            Silakan
                                            pilih paket langganan Anda untuk mendapatkan akses ekstensi.</p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            
                            <?php if($user->subscription_status === 'active' && $latestMobileApp): ?>
                                <div style="<?php echo e($bothAvailable ? '' : 'max-width: 48rem; margin: 0 auto;'); ?>">
                                    <div class="extension-card mobile-app-card">
                                        <h3>
                                            Aplikasi Mobile
                                        </h3>
                                        <p>Unduh aplikasi mobile kami untuk akses mudah di perangkat Android dan iOS Anda.
                                        </p>
                                        <?php if($latestMobileApp->android_file_path || $latestMobileApp->ios_file_path): ?>
                                            <div class="download-buttons">
                                                <?php if($latestMobileApp->android_file_path): ?>
                                                    <a href="<?php echo e(Storage::url($latestMobileApp->android_file_path)); ?>"
                                                        target="_blank" download class="download-btn">
                                                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                                            <path
                                                                d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                                                        </svg>
                                                        Unduh untuk Android
                                                    </a>
                                                <?php endif; ?>
                                                <?php if($latestMobileApp->ios_file_path): ?>
                                                    <a href="<?php echo e(Storage::url($latestMobileApp->ios_file_path)); ?>"
                                                        target="_blank" download class="download-btn">
                                                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                                            <path
                                                                d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z" />
                                                        </svg>
                                                        Unduh untuk iOS
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="download-buttons">
                                                <div class="download-btn" style="opacity: 0.6; cursor: not-allowed;">
                                                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                                        <path
                                                            d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                                                    </svg>
                                                    Android (Segera)
                                                </div>
                                                <div class="download-btn" style="opacity: 0.6; cursor: not-allowed;">
                                                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                                        <path
                                                            d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z" />
                                                    </svg>
                                                    iOS (Segera)
                                                </div>
                                            </div>
                                            <p
                                                style="margin: 1rem 0 0 0; color: #ffffff; font-style: italic; text-align: center;">
                                                Aplikasi mobile sedang dalam proses pengembangan dan akan segera tersedia.
                                            </p>
                                        <?php endif; ?>
                                        <span class="extension-version"><?php echo e($latestMobileApp->name ?? 'Aplikasi Mobile'); ?>

                                            - Versi
                                            terbaru: <?php echo e($latestMobileApp->version); ?>

                                            <?php if($latestMobileApp->description): ?>
                                                <br><?php echo e($latestMobileApp->description); ?>

                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php elseif($user->subscription_status !== 'active'): ?>
                                <div style="<?php echo e($bothAvailable ? '' : 'max-width: 48rem; margin: 0 auto;'); ?>">
                                    <div class="extension-card mobile-app-card text-center">
                                        <h3>Aplikasi Mobile</h3>
                                        <p class="no-extension-message">Aplikasi mobile belum tersedia untuk diunduh saat
                                            ini. Silakan
                                            pilih paket langganan Anda untuk mendapatkan akses aplikasi mobile.</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                
                <?php if($blogCallbacks->count() > 0): ?>
                    <div style="margin-top: 4rem;">
                        <h2 style="font-size: 1.5rem; font-weight: bold; color: white; margin-bottom: 1.5rem;">Informasi
                            Penting</h2>
                        <div style="display: grid; gap: 1rem;">
                            <?php $__currentLoopData = $blogCallbacks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $callback): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(8px); border-radius: 0.5rem; padding: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); cursor: pointer; transition: all 0.3s ease;"
                                    onclick="window.location.href='<?php echo e(route('callback.show', $callback->id)); ?>'"
                                    onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
                                    onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <div>
                                            <h3
                                                style="font-size: 1.125rem; font-weight: 600; color: white; margin-bottom: 0.25rem;">
                                                <?php echo e($callback->nama); ?></h3>
                                            <p style="color: #d1d5db; font-size: 0.875rem;">
                                                <?php echo e($callback->blogPost->title); ?></p>
                                        </div>
                                        <div style="color: rgba(255, 255, 255, 0.6);">
                                            <svg style="width: 1.25rem; height: 1.25rem;" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.appLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/dashboard/index.blade.php ENDPATH**/ ?>