<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section with Gradient Background -->
    <div class="gradient-bg relative overflow-hidden">
        <div class="absolute inset-0">
            <div
                class="absolute top-0 left-0 w-72 h-72 bg-white opacity-10 rounded-full -translate-x-1/2 -translate-y-1/2 floating">
            </div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full translate-x-1/2 translate-y-1/2 floating"
                style="animation-delay: -1s;"></div>
        </div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 mt-30">
            <div class="flex justify-between items-center mb-8">
                <div class="text-white">
                    <h1 class="text-4xl text-white font-bold mb-2">Dashboard</h1>
                    <p class="text-xl opacity-90">Selamat datang kembali, <span
                            class="font-semibold"><?php echo e($user->name); ?></span>!</p>
                    <p class="text-sm opacity-75 mt-1">Kelola akun dan akses aplikasi premium Anda</p>
                </div>
                <div>
                    <form method="GET" action="<?php echo e(route('logout')); ?>" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit"
                            class="glass-effect text-white font-medium py-3 px-6 rounded-xl transition duration-300 flex items-center space-x-2 hover:bg-white hover:bg-opacity-20 border border-white border-opacity-20">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50 -mt-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Status Akun -->
                <div class="bg-white rounded-2xl shadow-lg border-0 p-8 card-hover relative overflow-hidden">
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -translate-y-10 translate-x-10 opacity-10">
                    </div>
                    <div class="flex items-center relative z-10">
                        <div
                            class="p-4 rounded-2xl <?php echo e($user->is_active ? 'bg-gradient-to-br from-green-400 to-green-600' : 'bg-gradient-to-br from-red-400 to-red-600'); ?> shadow-lg">
                            <i class="fas <?php echo e($user->is_active ? 'fa-check' : 'fa-times'); ?> text-white text-xl"></i>
                        </div>
                        <div class="ml-6">
                            <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Status Akun</p>
                            <p class="text-2xl font-bold <?php echo e($user->is_active ? 'text-green-600' : 'text-red-600'); ?> mt-1">
                                <?php echo e($user->is_active ? 'Aktif' : 'Tidak Aktif'); ?>

                            </p>
                        </div>
                    </div>
                </div>

                <!-- Role/Plan -->
                <div class="bg-white rounded-2xl shadow-lg border-0 p-8 card-hover relative overflow-hidden">
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -translate-y-10 translate-x-10 opacity-10">
                    </div>
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Plan</p>
                            <p class="text-lg font-semibold text-blue-600 capitalize">
                                <?php echo e($user->role ?? 'Basic'); ?>

                            </p>
                        </div>
                    </div>
                </div>

                <!-- Status Pembayaran -->
                <div class="bg-white rounded-2xl shadow-lg border-0 p-8 card-hover relative overflow-hidden">
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -translate-y-10 translate-x-10 opacity-10">
                    </div>
                    <div class="flex items-center">
                        <div
                            class="p-3 rounded-full
                        <?php if($user->payment_status === 'paid'): ?> bg-green-100
                        <?php elseif($user->payment_status === 'pending'): ?> bg-yellow-100
                        <?php else: ?> bg-red-100 <?php endif; ?>">
                            <svg class="w-6 h-6
                            <?php if($user->payment_status === 'paid'): ?> text-green-600
                            <?php elseif($user->payment_status === 'pending'): ?> text-yellow-600
                            <?php else: ?> text-red-600 <?php endif; ?>"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Status Pembayaran</p>
                            <p
                                class="text-lg font-semibold
                            <?php if($user->payment_status === 'paid'): ?> text-green-600
                            <?php elseif($user->payment_status === 'pending'): ?> text-yellow-600
                            <?php else: ?> text-red-600 <?php endif; ?> capitalize">
                                <?php if($user->payment_status === 'paid'): ?>
                                    Lunas
                                <?php elseif($user->payment_status === 'pending'): ?>
                                    Tidak Ada
                                <?php else: ?>
                                    Belum Bayar
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Informasi Subscription -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Informasi Subscription</h2>

                        <div class="space-y-4">
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Plan Subscription</span>
                                <span
                                    class="font-medium text-gray-900 capitalize"><?php echo e($user->subscription_plan ?? 'Tidak ada'); ?></span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Status Subscription</span>
                                <span
                                    class="px-3 py-1 rounded-full text-sm font-medium
                                <?php if($user->subscription_status === 'active'): ?> bg-green-100 text-green-800
                                <?php elseif($user->subscription_status === 'expired'): ?> bg-red-100 text-red-800
                                <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                    <?php echo e($user->subscription_status ?? 'Tidak ada'); ?>

                                </span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Tanggal Aktivasi</span>
                                <span class="font-medium text-gray-900">
                                    <?php echo e($user->subscription_activated_at ? $user->subscription_activated_at->format('d M Y') : 'Belum diaktivasi'); ?>

                                </span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Tanggal Berakhir</span>
                                <span class="font-medium text-gray-900">
                                    <?php echo e($user->subscription_expires_at ? $user->subscription_expires_at->format('d M Y') : 'Tidak ada batas waktu'); ?>

                                </span>
                            </div>

                            <div class="flex justify-between items-center py-3">
                                <span class="text-gray-600">Status Aktif</span>
                                <span
                                    class="px-3 py-1 rounded-full text-sm font-medium
                                <?php echo e($user->isSubscriptionActive() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($user->isSubscriptionActive() ? 'Aktif' : 'Tidak Aktif'); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Info -->
                <div class="space-y-6">
                    <!-- Informasi Penting -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Penting</h3>

                        <div class="space-y-3">
                            <?php if($importantInfos->count() > 0): ?>
                                <?php $__currentLoopData = $importantInfos->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                                        onclick="openInfoModal('<?php echo e($info->id); ?>', '<?php echo e(addslashes($info->title)); ?>', '<?php echo e(addslashes($info->content)); ?>', '<?php echo e($info->type); ?>')">
                                        <div class="flex items-center space-x-3">
                                            <div
                                                class="p-2 rounded-full
                                                <?php if($info->type == 'info'): ?> bg-blue-100 text-blue-600
                                                <?php elseif($info->type == 'warning'): ?> bg-yellow-100 text-yellow-600
                                                <?php elseif($info->type == 'error'): ?> bg-red-100 text-red-600
                                                <?php elseif($info->type == 'success'): ?> bg-green-100 text-green-600
                                                <?php else: ?> bg-gray-100 text-gray-600 <?php endif; ?>">
                                                <?php if($info->type == 'info'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                        </path>
                                                    </svg>
                                                <?php elseif($info->type == 'warning'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                                        </path>
                                                    </svg>
                                                <?php elseif($info->type == 'error'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                <?php elseif($info->type == 'success'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                        </path>
                                                    </svg>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900"><?php echo e($info->title); ?></p>
                                                <p class="text-xs text-gray-500"><?php echo e(Str::limit($info->content, 50)); ?></p>
                                            </div>
                                        </div>
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <svg class="w-12 h-12 text-gray-300 mx-auto mb-2" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <p class="text-gray-500 text-sm">Tidak ada informasi penting saat ini</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Subscription Warning -->
                    <?php
                        $daysLeft = $user->getDaysUntilExpiry();
                        $showWarning = $daysLeft !== null && $daysLeft <= 6;
                    ?>

                    <?php if($showWarning): ?>
                        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
                            <div class="flex items-center mb-3">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                    </path>
                                </svg>
                                <h3 class="text-lg font-semibold text-red-800">Peringatan!</h3>
                            </div>
                            <p class="text-red-700 mb-4">
                                Akun Anda akan berakhir dalam
                                <span class="font-bold text-red-800"><?php echo e($daysLeft); ?> hari</span>.
                                Segera lakukan pembayaran untuk perpanjangan.
                            </p>
                            <a href="<?php echo e(route('payment.index')); ?>"
                                class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                                Perpanjang Sekarang
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

                        <div class="space-y-3">
                            <?php if($user->subscription_status === 'active'): ?>
                                <a href="<?php echo e(route('sites.index')); ?>"
                                    class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                                    Akses Situs Premium
                                </a>
                            <?php endif; ?>

                            <a href="<?php echo e(route('payment.index')); ?>"
                                class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                                Pembayaran Manual
                            </a>

                            <a href="<?php echo e(route('installation.guide')); ?>"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 block text-center">
                                Panduan Pemasangan
                            </a>
                        </div>
                    </div>

                    <!-- Kontak Support -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white">
                        <h3 class="text-lg text-white font-semibold mb-2">Butuh Bantuan?</h3>
                        <p class="text-blue-100 text-sm mb-4">Tim support kami siap membantu Anda 24/7</p>
                        <button
                            class="bg-white text-blue-600 font-medium py-2 px-4 rounded-lg hover:bg-gray-100 transition duration-200">
                            Hubungi Support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal untuk Detail Informasi Penting -->
    <div id="infoModal"
        class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-9999 mt-30">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header Modal -->
                <div class="flex items-center justify-between pb-3 border-b border-gray-200">
                    <h3 id="modalTitle" class="text-lg font-semibold text-gray-900"></h3>
                    <button onclick="closeInfoModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Body Modal -->
                <div class="mt-4">
                    <div id="modalIcon" class="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full">
                    </div>
                    <div id="modalContent" class="text-gray-700 leading-relaxed"></div>
                </div>

                <!-- Footer Modal -->
                <div class="flex justify-end pt-4 border-t border-gray-200 mt-6">
                    <button onclick="closeInfoModal()"
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 transition-colors">
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openInfoModal(id, title, content, type) {
            const modal = document.getElementById('infoModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalContent = document.getElementById('modalContent');
            const modalIcon = document.getElementById('modalIcon');

            // Set title dan content
            modalTitle.textContent = title;
            modalContent.innerHTML = content.replace(/\\n/g, '<br>');

            // Set icon berdasarkan type
            let iconHTML = '';
            let iconClass = '';

            switch (type) {
                case 'info':
                    iconClass = 'bg-blue-100 text-blue-600';
                    iconHTML =
                        '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                    break;
                case 'warning':
                    iconClass = 'bg-yellow-100 text-yellow-600';
                    iconHTML =
                        '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>';
                    break;
                case 'error':
                    iconClass = 'bg-red-100 text-red-600';
                    iconHTML =
                        '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                    break;
                case 'success':
                    iconClass = 'bg-green-100 text-green-600';
                    iconHTML =
                        '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                    break;
                default:
                    iconClass = 'bg-gray-100 text-gray-600';
                    iconHTML =
                        '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
            }

            modalIcon.className = `flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full ${iconClass}`;
            modalIcon.innerHTML = iconHTML;

            // Tampilkan modal
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeInfoModal() {
            const modal = document.getElementById('infoModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Tutup modal ketika klik di luar modal
        document.getElementById('infoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeInfoModal();
            }
        });

        // Tutup modal dengan tombol ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeInfoModal();
            }
        });

        // === EXTENSION AUTHENTICATION BRIDGE ===
        // Simpan informasi user untuk akses ekstensi
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Data user dari server dengan struktur yang konsisten untuk ekstensi
                const userData = {
                    id: <?php echo e($user->id); ?>,
                    user_id: <?php echo e($user->id); ?>, // Alias untuk konsistensi dengan backend
                    email: '<?php echo e($user->email); ?>',
                    name: '<?php echo e($user->name); ?>',
                    role: '<?php echo e($user->role); ?>',
                    subscription_status: '<?php echo e($user->subscription_status ?? 'inactive'); ?>',
                    subscription_active: <?php echo e($user->isSubscriptionActive() ? 'true' : 'false'); ?>,
                    subscription_plan: '<?php echo e($user->subscription_plan ?? 'none'); ?>',
                    subscription_expires_at: '<?php echo e($user->subscription_expires_at ?? null); ?>',
                    login_timestamp: new Date().getTime(),
                    session_key: '<?php echo e(session()->getId()); ?>',
                    session_token: '<?php echo e(csrf_token()); ?>',
                    timestamp: Date.now()
                };

                // Validasi data sebelum disimpan
                if (!userData.id || !userData.email) {
                    console.error('❌ Data user tidak lengkap');
                    return;
                }

                // Simpan ke localStorage untuk akses ekstensi
                localStorage.setItem('satu_pintu_user_auth', JSON.stringify(userData));

                // Simpan juga ke sessionStorage sebagai backup
                sessionStorage.setItem('satu_pintu_user_auth', JSON.stringify(userData));

                // Set flag bahwa user sudah login
                localStorage.setItem('satu_pintu_logged_in', 'true');
                sessionStorage.setItem('satu_pintu_logged_in', 'true');

                // Broadcast event untuk ekstensi yang mungkin sedang listening
                window.dispatchEvent(new CustomEvent('satuPintuUserLogin', {
                    detail: userData
                }));

                // Event tambahan untuk kompatibilitas dengan versi lama
                window.dispatchEvent(new CustomEvent('extensionUserLogin', {
                    detail: userData
                }));

                console.log('✅ Data autentikasi user berhasil disimpan untuk akses ekstensi');
                console.log('📋 Data yang disimpan:', userData);

            } catch (error) {
                console.error('❌ Gagal menyimpan data autentikasi:', error);
            }
        });

        // Cleanup saat logout (jika ada tombol logout di halaman ini)
        function handleLogout() {
            try {
                // Hapus data user dari storage
                localStorage.removeItem('satu_pintu_user_auth');
                localStorage.removeItem('satu_pintu_logged_in');
                localStorage.removeItem('extension_user_auth');
                localStorage.removeItem('extension_logged_in');
                sessionStorage.removeItem('satu_pintu_user_auth');
                sessionStorage.removeItem('satu_pintu_logged_in');
                sessionStorage.removeItem('extension_user_auth');
                sessionStorage.removeItem('extension_logged_in');

                // Broadcast logout event untuk ekstensi
                window.dispatchEvent(new CustomEvent('satuPintuUserLogout'));
                window.dispatchEvent(new CustomEvent('extensionUserLogout'));

                console.log('✅ Data autentikasi user berhasil dibersihkan');
            } catch (error) {
                console.error('❌ Gagal membersihkan data autentikasi:', error);
            }
        }

        // Attach logout handler ke tombol logout jika ada
        const logoutButton = document.querySelector('form[action*="logout"] button[type="submit"]');
        if (logoutButton) {
            logoutButton.addEventListener('click', handleLogout);
        }
    </script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showSupportModal() {
            Swal.fire({
                title: 'Hubungi Support',
                html: `
                    <div class="text-center space-y-4">
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">WhatsApp Support</h4>
                            <p class="text-green-700">Admin tersedia 24/7 untuk membantu Anda</p>
                            <a href="https://wa.me/6281234567890" target="_blank"
                               class="inline-block mt-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                Chat via WhatsApp
                            </a>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p>Atau kirim email ke: <strong><EMAIL></strong></p>
                        </div>
                    </div>
                `,
                icon: 'question',
                showConfirmButton: false
            });
        }

        // Auto show warning if subscription is near expiry
        document.addEventListener('DOMContentLoaded', function() {
            <?php if($showWarning): ?>
                setTimeout(() => {
                    Swal.fire({
                        title: 'Peringatan Masa Aktif!',
                        html: `
                        <div class="text-center">
                            <p class="text-lg mb-4">Akun Anda akan berakhir dalam</p>
                            <div class="bg-red-100 text-red-800 text-2xl font-bold py-3 px-6 rounded-lg mb-4">
                                <?php echo e($daysLeft); ?> hari
                            </div>
                            <p class="text-gray-600 mb-4">Segera lakukan pembayaran untuk perpanjangan akun Anda.</p>
                        </div>
                    `,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Perpanjang Sekarang',
                        cancelButtonText: 'Nanti',
                        confirmButtonColor: '#EF4444'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '<?php echo e(route('payment.index')); ?>';
                        }
                    });
                }, 2000);
            <?php endif; ?>
        });

        // Function to copy payment details
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                Swal.fire({
                    icon: 'success',
                    title: 'Tersalin!',
                    text: 'Informasi telah disalin ke clipboard',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/dashboard/index.blade.php ENDPATH**/ ?>