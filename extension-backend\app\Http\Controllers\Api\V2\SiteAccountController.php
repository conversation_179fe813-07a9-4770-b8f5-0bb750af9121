<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SiteAccountController extends Controller
{
    /**
     * Get account credentials for a specific site
     */
    public function getSiteAccounts($siteId)
    {
        try {
            $user = Auth::user();
            
            // Get site with role-based access control
            $query = Site::where('id', $siteId)->where('is_active', true);
            
            // Apply role-based filtering
            if ($user->role === 'elite') {
                $query->where('category', 'elite');
            } elseif ($user->role === 'premium') {
                $query->whereIn('category', ['elite', 'premium']);
            }
            
            $site = $query->first();
            
            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site not found or access denied'
                ], 404);
            }
            
            // Get alternative accounts from JSON field
            $alternativeAccounts = $site->alternative_accounts ?? [];
            
            // If no alternative accounts, return empty array
            if (empty($alternativeAccounts)) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'site_id' => $site->id,
                        'site_name' => $site->name,
                        'accounts' => []
                    ]
                ]);
            }
            
            // Format accounts data
            $accounts = [];
            foreach ($alternativeAccounts as $index => $account) {
                $accounts[] = [
                    'id' => $index + 1,
                    'email' => $account['email'] ?? '',
                    'password' => $account['password'] ?? '',
                    'name' => $account['name'] ?? "Account " . ($index + 1)
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => [
                    'site_id' => $site->id,
                    'site_name' => $site->name,
                    'accounts' => $accounts
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving site accounts: ' . $e->getMessage()
            ], 500);
        }
    }
}