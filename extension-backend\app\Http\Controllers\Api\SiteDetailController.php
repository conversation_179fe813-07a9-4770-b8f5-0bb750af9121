<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Models\SiteJsonFile;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SiteDetailController extends Controller
{
    /**
     * Get site detail with JSON files
     */
    public function getSiteDetail($siteId): JsonResponse
    {
        try {
            $site = Site::with(['activeJsonFiles'])
                ->where('id', $siteId)
                ->where('is_active', true)
                ->first();

            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site tidak ditemukan atau tidak aktif'
                ], 404);
            }

            $jsonFiles = $site->activeJsonFiles->map(function ($jsonFile) {
                return [
                    'id' => $jsonFile->id,
                    'name' => $jsonFile->name,
                    'description' => $jsonFile->description,
                    'file_path' => $jsonFile->file_path,
                    'cookies' => $jsonFile->getCookiesFromFile(),
                    'created_at' => $jsonFile->created_at->format('Y-m-d H:i:s')
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'site' => [
                        'id' => $site->id,
                        'name' => $site->name,
                        'url' => $site->url,
                        'logo_path' => $site->logo_path,
                        'description' => $site->description,
                        'domain' => $site->domain,
                        // Custom redirect fields
                        'enable_custom_redirect' => $site->enable_custom_redirect,
                        'redirect_url' => $site->redirect_url,
                        'redirect_title' => $site->redirect_title,
                        'redirect_content' => $site->redirect_content,
                        'redirect_delay' => $site->redirect_delay,
                        // Account injection fields
                        'login_url' => $site->login_url,
                        'email_selector' => $site->email_selector,
                        'password_selector' => $site->password_selector,
                        'submit_selector' => $site->submit_selector,
                        'additional_script' => $site->additional_script,
                        'js_after_submit' => $site->js_after_submit,
                    ],
                    'json_files' => $jsonFiles
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cookies from specific JSON file
     */
    public function getJsonFileCookies($jsonFileId): JsonResponse
    {
        try {
            $jsonFile = SiteJsonFile::where('id', $jsonFileId)
                ->where('is_active', true)
                ->with('site')
                ->first();

            if (!$jsonFile) {
                return response()->json([
                    'success' => false,
                    'message' => 'File JSON tidak ditemukan atau tidak aktif'
                ], 404);
            }

            $cookies = $jsonFile->getCookiesFromFile();

            return response()->json([
                'success' => true,
                'data' => [
                    'json_file' => [
                        'id' => $jsonFile->id,
                        'name' => $jsonFile->name,
                        'description' => $jsonFile->description,
                    ],
                    'site' => [
                        'id' => $jsonFile->site->id,
                        'name' => $jsonFile->site->name,
                        'url' => $jsonFile->site->url,
                    ],
                    'cookies' => $cookies
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all sites with their JSON files count
     */
    public function getAllSites(): JsonResponse
    {
        try {
            $sites = Site::where('is_active', true)
                ->withCount(['activeJsonFiles'])
                ->get()
                ->map(function ($site) {
                    return [
                        'id' => $site->id,
                        'name' => $site->name,
                        'url' => $site->url,
                        'logo_path' => $site->logo_path,
                        'description' => $site->description,
                        'json_files_count' => $site->active_json_files_count
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $sites
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
