@extends('layout.homeLayout')

@section('title', '<PERSON><PERSON>')

@push('styles')
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .plan-card {
            position: relative;
            overflow: hidden;
        }

        .plan-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .popular-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section -->
    <div class="gradient-bg relative overflow-hidden py-20">
        <div class="absolute inset-0">
            <div
                class="absolute top-0 left-0 w-72 h-72 bg-white opacity-10 rounded-full -translate-x-1/2 -translate-y-1/2 floating">
            </div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full translate-x-1/2 translate-y-1/2 floating"
                style="animation-delay: -1s;"></div>
        </div>
        <div class="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center mt-30">
            <div class="mb-8">
                <h1 class="text-5xl font-bold text-white mb-4">Paket Langganan</h1>
                <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">Pilih paket yang sesuai untuk perpanjangan akun
                    Anda dan nikmati akses unlimited ke aplikasi premium</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50 -mt-10">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">

            <!-- Warning Alert -->
            @if ($showWarning)
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 relative mb-10">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                <strong>Peringatan!</strong> Akun Anda akan berakhir dalam {{ $daysLeft }} hari. Silakan
                                lakukan pembayaran untuk perpanjangan.
                            </p>
                        </div>
                    </div>
                </div>
            @endif



            <!-- Subscription Plans -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-10 mb-12">
                @foreach ($plans as $index => $plan)
                    <div
                        class="plan-card bg-white rounded-3xl shadow-2xl overflow-hidden border-0 card-hover {{ $index == 1 ? 'ring-4 ring-purple-500 ring-opacity-50' : '' }}">
                        @if ($index == 1)
                            <div class="popular-badge text-white text-center py-2 px-4 text-sm font-bold">
                                <i class="fas fa-crown mr-2"></i>PALING POPULER
                            </div>
                        @endif
                        <div class="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 px-8 py-8">
                            <div class="text-center">
                                <h3 class="text-2xl font-bold text-white mb-2">{{ $plan->name }}</h3>
                                <p class="text-indigo-100 text-sm">{{ $plan->description }}</p>
                            </div>
                        </div>
                        <div class="p-8">
                            <div class="text-center mb-8">
                                <div class="text-5xl font-bold text-gray-900 mb-2">
                                    {{ $plan->formatted_price }}
                                </div>
                                <div class="text-gray-500 text-lg">/{{ $plan->duration_text }}</div>
                            </div>

                            @if ($plan->features && count($plan->features) > 0)
                                <div class="mb-8">
                                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Fitur yang Termasuk:</h4>
                                    <ul class="space-y-4">
                                        @foreach ($plan->features as $feature)
                                            <li class="flex items-center">
                                                <div class="py-1 px-2.5 bg-green-100 rounded-full mr-3 mt-1">
                                                    <i class="fas fa-check text-green-600 text-xs"></i>
                                                </div>
                                                <span
                                                    class="text-gray-700 text-sm leading-relaxed">{{ $feature }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <button
                                onclick="selectPlan({{ $plan->id }}, '{{ $plan->name }}', {{ $plan->price }}, '{{ $plan->duration_text }}', '{{ $plan->whatsapp_number ?? '*************' }}')"
                                class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-2xl transition duration-300 transform hover:scale-105 shadow-lg">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                Pilih {{ $plan->name }}
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Payment Instructions -->
            <div class="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-3xl p-8 border border-blue-100">
                <div class="text-center mb-8">
                    <div
                        class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-4">
                        <i class="fas fa-info-circle text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Cara Pembayaran</h3>
                    <p class="text-gray-600">Ikuti langkah-langkah mudah berikut untuk melakukan pembayaran</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-6 bg-white rounded-2xl shadow-sm">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-blue-600 font-bold text-lg">1</span>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Pilih Paket</h4>
                        <p class="text-sm text-gray-600">Pilih paket langganan yang sesuai dengan kebutuhan Anda</p>
                    </div>

                    <div class="text-center p-6 bg-white rounded-2xl shadow-sm">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-green-600 font-bold text-lg">2</span>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Hubungi Admin</h4>
                        <p class="text-sm text-gray-600">Klik tombol untuk mengarahkan ke WhatsApp admin</p>
                    </div>

                    <div class="text-center p-6 bg-white rounded-2xl shadow-sm">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-purple-600 font-bold text-lg">3</span>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Konfirmasi</h4>
                        <p class="text-sm text-gray-600">Kirim pesan otomatis dan tunggu verifikasi admin</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SweetAlert2 Script -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                confirmButtonText: 'OK'
            });
        @endif

        @if (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '{{ session('error') }}',
                confirmButtonText: 'OK'
            });
        @endif

        // WhatsApp functions
        function contactAdmin() {
            const message = `Halo admin, saya ingin konfirmasi pembayaran untuk akun {{ $user->email }}`;
            const whatsappUrl = `https://wa.me/*************?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function showPaymentGuide() {
            Swal.fire({
                title: 'Panduan Pembayaran',
                html: `
                        <div class="text-left space-y-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">📱 Transfer Bank</h4>
                                <p class="text-blue-700 text-sm">Transfer ke rekening admin sesuai dengan jumlah yang ditentukan. Simpan bukti transfer sebagai bukti pembayaran.</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">💵 Tunai</h4>
                                <p class="text-green-700 text-sm">Lakukan pembayaran langsung ke lokasi yang ditentukan oleh admin. Minta tanda terima sebagai bukti.</p>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">📲 E-Wallet</h4>
                                <p class="text-purple-700 text-sm">Transfer melalui aplikasi e-wallet favorit Anda. Screenshot bukti transfer sebagai bukti pembayaran.</p>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <p class="text-yellow-800 text-sm"><strong>Catatan:</strong> Setelah pembayaran dikirim, admin akan memverifikasi dalam waktu 1x24 jam.</p>
                            </div>
                        </div>
                    `,
                icon: 'info',
                confirmButtonText: 'Mengerti',
                confirmButtonColor: '#3B82F6',
                width: 600
            });
        }

        // Form submission handler with SweetAlert2 confirmation
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            const amount = document.getElementById('amount').value;
            const paymentMethod = document.getElementById('payment_method').value;
            const paymentProof = document.getElementById('payment_proof').files[0];

            if (!amount || !paymentMethod || !paymentProof) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Perhatian',
                    text: 'Harap lengkapi semua field yang diperlukan!',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Show confirmation dialog
            Swal.fire({
                title: 'Konfirmasi Pembayaran',
                html: `
                <div class="text-left">
                    <p><strong>Jumlah:</strong> Rp ${parseInt(amount).toLocaleString('id-ID')}</p>
                    <p><strong>Metode:</strong> ${paymentMethod.replace('_', ' ').toUpperCase()}</p>
                    <p class="text-sm text-gray-600 mt-2">Pastikan data yang Anda masukkan sudah benar.</p>
                </div>
            `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Ya, Kirim',
                cancelButtonText: 'Batal',
                confirmButtonColor: '#3B82F6',
                cancelButtonColor: '#EF4444'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Mengirim...',
                        text: 'Mohon tunggu, sedang memproses pembayaran Anda',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit form
                    this.submit();
                }
            });
        });

        // File upload preview
        document.getElementById('payment_proof').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 2 * 1024 * 1024) {
                    Swal.fire({
                        icon: 'error',
                        title: 'File Terlalu Besar',
                        text: 'Ukuran file maksimal 2MB',
                        confirmButtonText: 'OK'
                    });
                    this.value = '';
                    return;
                }

                if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Format Tidak Valid',
                        text: 'Hanya menerima file JPG, PNG, atau JPEG',
                        confirmButtonText: 'OK'
                    });
                    this.value = '';
                    return;
                }
            }
        });
    </script>

    <!-- WhatsApp Template Modal -->
    <div id="whatsappModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Konfirmasi Pembelian</h3>
                <p class="text-gray-600 mb-4">
                    Anda akan diarahkan ke WhatsApp admin untuk melakukan pembayaran paket <span id="selectedPlanName"
                        class="font-semibold"></span>.
                </p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeModal()"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        Batal
                    </button>
                    <button id="confirmWhatsApp" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        Lanjut ke WhatsApp
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectPlan(planId, planName, price, duration, whatsappNumber) {
            document.getElementById('selectedPlanName').textContent = planName;
            document.getElementById('whatsappModal').classList.remove('hidden');

            const message =
                `Halo Admin Satu Pintu,\n\nSaya ingin membeli paket ${planName} dengan harga Rp ${price.toLocaleString('id-ID')} untuk ${duration}.\n\nMohon informasi pembayarannya.\n\nTerima kasih.`;

            const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;

            document.getElementById('confirmWhatsApp').onclick = function() {
                window.open(whatsappUrl, '_blank');
                closeModal();
            };
        }

        function closeModal() {
            document.getElementById('whatsappModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('whatsappModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
@endsection
