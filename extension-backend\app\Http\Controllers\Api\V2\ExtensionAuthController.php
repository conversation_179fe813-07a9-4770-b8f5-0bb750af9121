<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Anyone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ExtensionAuthController extends Controller
{
    /**
     * Check if user is authenticated via website session
     * This endpoint is used by the browser extension to verify authentication
     */
    public function checkWebsiteSession(Request $request)
    {
        try {
            // First try to get session info from cache
            $sessionId = $request->session()->getId();
            $sessionData = \Illuminate\Support\Facades\Cache::get('extension_session_' . $sessionId);
            
            if (!$sessionData) {
                // Fallback to check if user is authenticated via website session
                if (!Auth::guard('anyone')->check()) {
                    return response()->json([
                        'success' => false,
                        'authenticated' => false,
                        'message' => 'User not authenticated via website session',
                        'redirect_url' => route('login')
                    ], 401);
                }
                
                $user = Auth::guard('anyone')->user();
            } else {
                // Get user from cache data
                $user = Anyone::find($sessionData['user_id']);
                if (!$user) {
                    return response()->json([
                        'success' => false,
                        'authenticated' => false,
                        'message' => 'User not found',
                        'redirect_url' => route('login')
                    ], 401);
                }
            }
            
            // Check if user has active subscription using model methods
            $isSubscriptionActive = $user->isSubscriptionActive();
            
            if (!$isSubscriptionActive) {
                return response()->json([
                    'success' => false,
                    'authenticated' => true,
                    'subscription_active' => false,
                    'message' => 'User does not have an active subscription',
                    'redirect_url' => route('user.dashboard'),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                    ]
                ], 403);
            }

            // User is authenticated and has active subscription
            return response()->json([
                'success' => true,
                'authenticated' => true,
                'subscription_active' => true,
                'message' => 'User authenticated with active subscription',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'subscription' => [
                        'id' => $user->id, // Use user id as subscription id
                        'name' => $user->subscription_plan,
                        'status' => $user->subscription_status,
                        'expires_at' => $user->subscription_expires_at,
                        'is_expired' => $user->isSubscriptionExpired(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Extension auth check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'authenticated' => false,
                'message' => 'Internal server error',
                'redirect_url' => route('login')
            ], 500);
        }
    }

    /**
     * Get user session info for extension
     */
    public function getSessionInfo(Request $request)
    {
        try {
            // First try to get session info from cache
            $sessionId = $request->session()->getId();
            $sessionData = \Illuminate\Support\Facades\Cache::get('extension_session_' . $sessionId);
            
            if (!$sessionData) {
                // Fallback to check if user is authenticated via website session
                if (!Auth::guard('anyone')->check()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Not authenticated'
                    ], 401);
                }
                
                $user = Auth::guard('anyone')->user();
            } else {
                // Get user from cache data
                $user = Anyone::find($sessionData['user_id']);
                if (!$user) {
                    return response()->json([
                        'success' => false,
                        'message' => 'User not found'
                    ], 401);
                }
            }

            return response()->json([
                'success' => true,
                'session_id' => session()->getId(),
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'subscription' => [
                        'id' => $user->id, // Use user id as subscription id
                        'name' => $user->subscription_plan,
                        'status' => $user->subscription_status,
                        'expires_at' => $user->subscription_expires_at,
                        'is_expired' => $user->isSubscriptionExpired(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Get session info failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Check authentication using localStorage data from user dashboard
     * This is an alternative method when session cookies are not accessible
     */
    public function checkLocalStorageAuth(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|integer',
                'email' => 'required|email',
                'session_key' => 'required|string'
            ]);

            // Find user by ID and email
            $user = Anyone::where('id', $request->user_id)
                         ->where('email', $request->email)
                         ->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'authenticated' => false,
                    'message' => 'User not found or credentials invalid',
                    'redirect_url' => route('login')
                ], 401);
            }

            // Check if user has active subscription using model methods
            $isSubscriptionActive = $user->isSubscriptionActive();

            if (!$isSubscriptionActive) {
                return response()->json([
                    'success' => false,
                    'authenticated' => true,
                    'subscription_active' => false,
                    'message' => 'User does not have an active subscription',
                    'redirect_url' => route('user.dashboard'),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                    ]
                ], 403);
            }

            // User is authenticated and has active subscription
            return response()->json([
                'success' => true,
                'authenticated' => true,
                'subscription_active' => true,
                'message' => 'User authenticated with active subscription via localStorage',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'subscription' => [
                        'id' => $user->id, // Use user id as subscription id
                        'name' => $user->subscription_plan,
                        'status' => $user->subscription_status,
                        'expires_at' => $user->subscription_expires_at,
                        'is_expired' => $user->isSubscriptionExpired(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Extension localStorage auth check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'authenticated' => false,
                'message' => 'Internal server error',
                'redirect_url' => route('login')
            ], 500);
        }
    }

    /**
     * Get sites based on user subscription for extension
     */
    public function getSites(Request $request)
    {
        try {
            // First try to get session info from cache
            $sessionId = $request->session()->getId();
            $sessionData = \Illuminate\Support\Facades\Cache::get('extension_session_' . $sessionId);
            
            if (!$sessionData) {
                // Fallback to check if user is authenticated via website session
                if (!Auth::guard('anyone')->check()) {
                    return response()->json([
                        'success' => false,
                        'authenticated' => false,
                        'message' => 'User not authenticated',
                        'redirect_url' => route('login')
                    ], 401);
                }
                
                $user = Auth::guard('anyone')->user();
            } else {
                // Get user from cache data
                $user = Anyone::find($sessionData['user_id']);
                if (!$user) {
                    return response()->json([
                        'success' => false,
                        'authenticated' => false,
                        'message' => 'User not found',
                        'redirect_url' => route('login')
                    ], 401);
                }
            }
            
            // Check if user has active subscription using model methods
            $isSubscriptionActive = $user->isSubscriptionActive();
            
            if (!$isSubscriptionActive) {
                return response()->json([
                    'success' => false,
                    'authenticated' => true,
                    'subscription_active' => false,
                    'message' => 'User does not have an active subscription',
                    'redirect_url' => route('user.dashboard')
                ], 403);
            }

            // Get sites based on subscription
            // You may need to adjust this query based on your site model and relationships
            $sites = \App\Models\Site::where('is_active', true)
                ->with(['category', 'jsonFiles'])
                ->get()
                ->map(function ($site) {
                    return [
                        'id' => $site->id,
                        'name' => $site->name,
                        'url' => $site->url,
                        'description' => $site->description,
                        'logo_path' => $site->logo_path,
                        'category' => $site->category ? $site->category->slug : null,
                        'category_model' => $site->category ? [
                            'id' => $site->category->id,
                            'name' => $site->category->name,
                            'slug' => $site->category->slug,
                            'color' => $site->category->color ?? null,
                        ] : null,
                        'enable_custom_redirect' => $site->enable_custom_redirect ?? false,
                        'redirect_url' => $site->redirect_url,
                        'redirect_delay' => $site->redirect_delay ?? 5,
                        'redirect_title' => $site->redirect_title,
                        'redirect_content' => $site->redirect_content,
                        'json_files_count' => $site->jsonFiles ? $site->jsonFiles->count() : 0,
                    ];
                });

            return response()->json([
                'success' => true,
                'sites' => $sites,
                'important_info' => null // You can add important info logic here if needed
            ]);

        } catch (\Exception $e) {
            Log::error('Get sites for extension failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }
}