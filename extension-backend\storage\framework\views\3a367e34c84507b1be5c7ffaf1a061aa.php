<?php $__env->startSection('title', 'Detail Situs - ' . $site->name); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <img src="<?php echo e($site->logo_path ? Storage::url($site->logo_path) : '/images/default-site-logo.png'); ?>"
                         alt="<?php echo e($site->name); ?>"
                         class="w-16 h-16 rounded-lg object-cover">
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-gray-900"><?php echo e($site->name); ?></h1>
                        <p class="text-sm text-gray-600"><?php echo e($site->url); ?></p>
                        <p class="text-sm text-gray-500"><?php echo e($site->domain ?? 'Tidak ada domain'); ?></p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="flex items-center space-x-2">
                        <?php switch($site->visibility):
                            case ('elite'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    Elite Only
                                </span>
                                <?php break; ?>
                            <?php case ('premium'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Premium Only
                                </span>
                                <?php break; ?>
                            <?php case ('both'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Semua Role
                                </span>
                                <?php break; ?>
                        <?php endswitch; ?>
                        <a href="<?php echo e($site->url); ?>"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium">
                            Kunjungi Situs
                        </a>
                    </div>
                </div>
            </div>

            <?php if($site->description): ?>
                <div class="mt-4">
                    <p class="text-gray-700"><?php echo e($site->description); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Alternative Accounts Section -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">Akun Alternatif</h2>
                        <span class="text-sm text-gray-500"><?php echo e(count($site->alternative_accounts ?? [])); ?> akun tersedia</span>
                    </div>

                    <?php if(!empty($site->alternative_accounts)): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $site->alternative_accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-medium text-gray-900"><?php echo e($account['account_name'] ?? 'Akun ' . ($index + 1)); ?></h3>
                                            <p class="text-sm text-gray-600"><?php echo e($account['username'] ?? 'Tidak ada username'); ?></p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <?php if(!empty($account['is_primary'])): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Utama
                                                </span>
                                            <?php endif; ?>
                                            <?php if(!empty($account['is_active'])): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    Aktif
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    Nonaktif
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if(!empty($account['custom_fields'])): ?>
                                        <div class="mt-3">
                                            <h4 class="text-sm font-medium text-gray-700 mb-2">Field Kustom:</h4>
                                            <div class="bg-gray-50 rounded-md p-3">
                                                <pre class="text-xs text-gray-600 whitespace-pre-wrap"><?php echo e(json_encode($account['custom_fields'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="mt-4 flex space-x-2">
                                        <button onclick="copyToClipboard('<?php echo e($account['username'] ?? ''); ?>')"
                                                class="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors">
                                            Salin Username
                                        </button>
                                        <button onclick="copyToClipboard('<?php echo e($account['password'] ?? ''); ?>')"
                                                class="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors">
                                            Salin Password
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada akun alternatif</h3>
                            <p class="mt-1 text-sm text-gray-500">Situs ini belum memiliki akun alternatif yang tersedia.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- JSON Files Section -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">File JSON Cookies</h2>
                        <span class="text-sm text-gray-500"><?php echo e($site->jsonFiles()->count()); ?> file tersedia</span>
                    </div>

                    <?php if($site->jsonFiles->isNotEmpty()): ?>
                        <div class="space-y-3">
                            <?php $__currentLoopData = $site->jsonFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jsonFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900"><?php echo e($jsonFile->filename); ?></span>
                                    </div>
                                    <a href="<?php echo e(Storage::url($jsonFile->file_path)); ?>"
                                       download
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Download
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-sm text-gray-500">Tidak ada file JSON cookies yang tersedia.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- User Info -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Info Langganan</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Paket Langganan</span>
                            <p class="text-lg font-semibold text-gray-900"><?php echo e(ucfirst($user->role)); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Status</span>
                            <p class="text-sm font-medium text-green-600"><?php echo e(ucfirst($user->subscription_status)); ?></p>
                        </div>
                        <?php if($user->subscription_expires_at): ?>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Masa Berakhir</span>
                                <p class="text-sm font-medium text-gray-900"><?php echo e($user->subscription_expires_at->format('d M Y')); ?></p>
                            </div>
                        <?php endif; ?>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Sisa Hari</span>
                            <p class="text-lg font-bold text-green-600"><?php echo e($user->getDaysUntilExpiry() ?? 'Unlimited'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
                    <div class="space-y-2">
                        <a href="<?php echo e(route('sites.index')); ?>"
                           class="w-full bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-sm font-medium block">
                            Kembali ke Daftar
                        </a>
                        <a href="<?php echo e(route('user.dashboard')); ?>"
                           class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium block">
                            Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Teks berhasil disalin ke clipboard!');
    }, function(err) {
        alert('Gagal menyalin teks: ' + err);
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/sites/show.blade.php ENDPATH**/ ?>