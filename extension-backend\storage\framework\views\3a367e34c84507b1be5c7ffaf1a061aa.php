<?php $__env->startSection('title', 'Detail Situs - ' . $site->name); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <div class="gradient-bg relative overflow-hidden py-16">
        <div class="absolute inset-0">
            <div
                class="absolute top-0 left-0 w-72 h-72 bg-white opacity-10 rounded-full -translate-x-1/2 -translate-y-1/2 floating">
            </div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full translate-x-1/2 translate-y-1/2 floating"
                style="animation-delay: -1s;"></div>
        </div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row items-center justify-between">
                <div class="flex items-center text-white mb-8 lg:mb-0">
                    <div class="relative">
                        <img src="<?php echo e($site->logo_path ? Storage::url($site->logo_path) : '/images/default-site-logo.png'); ?>"
                            alt="<?php echo e($site->name); ?>"
                            class="w-24 h-24 rounded-3xl object-cover shadow-2xl border-4 border-white border-opacity-20">
                        <div
                            class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-6">
                        <h1 class="text-4xl lg:text-5xl font-bold mb-2"><?php echo e($site->name); ?></h1>
                        <p class="text-xl opacity-90 mb-1"><?php echo e($site->url); ?></p>
                        <p class="text-sm opacity-75"><?php echo e($site->domain ?? 'Domain tidak tersedia'); ?></p>
                    </div>
                </div>
                <div class="glass-effect rounded-2xl p-6 border border-white border-opacity-20">
                    <div class="flex flex-col items-center space-y-4">
                        <?php switch($site->visibility):
                            case ('elite'): ?>
                                <span
                                    class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                                    <i class="fas fa-crown mr-2"></i>
                                    Elite Only
                                </span>
                            <?php break; ?>

                            <?php case ('premium'): ?>
                                <span
                                    class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                                    <i class="fas fa-star mr-2"></i>
                                    Premium Only
                                </span>
                            <?php break; ?>

                            <?php case ('both'): ?>
                                <span
                                    class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 text-white">
                                    <i class="fas fa-users mr-2"></i>
                                    All Access
                                </span>
                            <?php break; ?>
                        <?php endswitch; ?>
                        <a href="<?php echo e($site->url); ?>" target="_blank" rel="noopener noreferrer"
                            class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl transition-all duration-300 text-sm font-semibold border border-white border-opacity-20">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            Kunjungi Situs
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50 -mt-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            <?php if($site->description): ?>
                <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 mt-8">
                    <div class="text-center">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Tentang Aplikasi</h2>
                        <p class="text-gray-600 leading-relaxed max-w-3xl mx-auto"><?php echo e($site->description); ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Alternative Accounts Section -->
                    <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 card-hover">
                        <div class="flex items-center justify-between mb-8">
                            <div class="flex items-center">
                                <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl mr-4">
                                    <i class="fas fa-users text-white text-xl"></i>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900">Akun Alternatif</h2>
                                    <p class="text-gray-500">Pilih akun yang tersedia untuk digunakan</p>
                                </div>
                            </div>
                            <div class="bg-blue-50 px-4 py-2 rounded-xl">
                                <span class="text-blue-600 font-semibold"><?php echo e(count($site->alternative_accounts ?? [])); ?>

                                    akun tersedia</span>
                            </div>
                        </div>

                        <?php if(!empty($site->alternative_accounts)): ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <?php $__currentLoopData = $site->alternative_accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div
                                        class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 hover:border-blue-300 transition-all duration-300 card-hover">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="flex items-center">
                                                <div
                                                    class="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl mr-4">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                                <div>
                                                    <h3 class="font-bold text-gray-900 text-lg">
                                                        <?php echo e($account['account_name'] ?? 'Akun ' . ($index + 1)); ?></h3>
                                                    <p class="text-sm text-gray-600">
                                                        <?php echo e($account['username'] ?? 'Username tidak tersedia'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-wrap gap-2">
                                            <?php if(!empty($account['is_primary'])): ?>
                                                <span
                                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-green-500 to-green-600 text-white">
                                                    <i class="fas fa-crown mr-1"></i>
                                                    Utama
                                                </span>
                                            <?php endif; ?>
                                            <?php if(!empty($account['is_active'])): ?>
                                                <span
                                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                                                    <i class="fas fa-check-circle mr-1"></i>
                                                    Aktif
                                                </span>
                                            <?php else: ?>
                                                <span
                                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-gray-400 to-gray-500 text-white">
                                                    <i class="fas fa-times-circle mr-1"></i>
                                                    Nonaktif
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if(!empty($account['custom_fields'])): ?>
                                        <div class="mt-3">
                                            <h4 class="text-sm font-medium text-gray-700 mb-2">Field Kustom:</h4>
                                            <div class="bg-gray-50 rounded-md p-3">
                                                <pre class="text-xs text-gray-600 whitespace-pre-wrap"><?php echo e(json_encode($account['custom_fields'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="mt-4 flex space-x-2">
                                        <button onclick="copyToClipboard('<?php echo e($account['username'] ?? ''); ?>')"
                                            class="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors">
                                            Salin Username
                                        </button>
                                        <button onclick="copyToClipboard('<?php echo e($account['password'] ?? ''); ?>')"
                                            class="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors">
                                            Salin Password
                                        </button>
                                    </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada akun alternatif</h3>
                        <p class="mt-1 text-sm text-gray-500">Situs ini belum memiliki akun alternatif yang
                            tersedia.</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- JSON Files Section -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">File JSON Cookies</h2>
                        <span class="text-sm text-gray-500"><?php echo e($site->jsonFiles()->count()); ?> file tersedia</span>
                    </div>

                    <?php if($site->jsonFiles->isNotEmpty()): ?>
                        <div class="space-y-3">
                            <?php $__currentLoopData = $site->jsonFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jsonFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                            </path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900"><?php echo e($jsonFile->filename); ?></span>
                                    </div>
                                    <a href="<?php echo e(Storage::url($jsonFile->file_path)); ?>" download
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Download
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-sm text-gray-500">Tidak ada file JSON cookies yang tersedia.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- User Info -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Info Langganan</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Paket Langganan</span>
                            <p class="text-lg font-semibold text-gray-900"><?php echo e(ucfirst($user->role)); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Status</span>
                            <p class="text-sm font-medium text-green-600"><?php echo e(ucfirst($user->subscription_status)); ?>

                            </p>
                        </div>
                        <?php if($user->subscription_expires_at): ?>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Masa Berakhir</span>
                                <p class="text-sm font-medium text-gray-900">
                                    <?php echo e($user->subscription_expires_at->format('d M Y')); ?></p>
                            </div>
                        <?php endif; ?>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Sisa Hari</span>
                            <p class="text-lg font-bold text-green-600">
                                <?php echo e($user->getDaysUntilExpiry() ?? 'Unlimited'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
                    <div class="space-y-2">
                        <a href="<?php echo e(route('sites.index')); ?>"
                            class="w-full bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-sm font-medium block">
                            Kembali ke Daftar
                        </a>
                        <a href="<?php echo e(route('user.dashboard')); ?>"
                            class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium block">
                            Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('Teks berhasil disalin ke clipboard!');
                }, function(err) {
                    alert('Gagal menyalin teks: ' + err);
                });
            }
        </script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/sites/show.blade.php ENDPATH**/ ?>