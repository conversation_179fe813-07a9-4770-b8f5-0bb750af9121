<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Anyone extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\AnyoneFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'role',
        'is_active',
        'subscription_plan',
        'subscription_status',
        'subscription_expires_at',
        'subscription_activated_at',
        'pending_order_id',
        'pending_payment_data',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'subscription_expires_at' => 'datetime',
            'subscription_activated_at' => 'datetime',
            'pending_payment_data' => 'array',
            'payment_date' => 'datetime',
            'payment_amount' => 'decimal:2',
            'midtrans_response' => 'array',
        ];
    }

    /**
     * Check if user is elite
     */
    public function isElite(): bool
    {
        return $this->role === 'elite';
    }

    /**
     * Check if user is premium
     */
    public function isPremium(): bool
    {
        return $this->role === 'premium';
    }

    /**
     * Check if subscription is active (not expired)
     */
    public function isSubscriptionActive(): bool
    {
        // If subscription status is expired, cancelled, or suspended, not active
        if (in_array($this->subscription_status, ['expired', 'cancelled', 'suspended'])) {
            return false;
        }

        // Check if subscription status is active or pending
        if (in_array($this->subscription_status, ['active', 'pending'])) {
            // If no expiry date, consider it unlimited access
            if (!$this->subscription_expires_at) {
                return true;
            }
            // Check if not expired (still in future or today)
            return $this->subscription_expires_at->isFuture() || $this->subscription_expires_at->isToday();
        }

        return false;
    }

    /**
     * Check if subscription is expired
     */
    public function isSubscriptionExpired(): bool
    {
        // If subscription status is already expired, return true
        if ($this->subscription_status === 'expired') {
            return true;
        }

        // If no expiry date and status is active/pending, never expired
        if (!$this->subscription_expires_at && in_array($this->subscription_status, ['active', 'pending'])) {
            return false;
        }

        // If no expiry date but status is not active/pending, consider expired
        if (!$this->subscription_expires_at) {
            return !in_array($this->subscription_status, ['active', 'pending']);
        }

        // Check if expiry date has passed
        return $this->subscription_expires_at->isPast();
    }

    /**
     * Get days remaining until subscription expires
     */
    public function getDaysUntilExpiry(): ?int
    {
        // If subscription is already expired, return 0
        if ($this->subscription_status === 'expired') {
            return 0;
        }

        if (!$this->subscription_expires_at) {
            return null; // Unlimited
        }

        $daysLeft = now()->diffInDays($this->subscription_expires_at, false);

        // If negative (expired), return 0
        return max(0, $daysLeft);
    }
}
