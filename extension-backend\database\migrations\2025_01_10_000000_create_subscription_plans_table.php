<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->decimal('price', 10, 0);
            $table->integer('duration_days');
            $table->text('description')->nullable();
            $table->json('features')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->text('whatsapp_message_template')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Insert default plans
        \App\Models\SubscriptionPlan::create([
            'name' => 'Basic Plan',
            'price' => 30000,
            'duration_days' => 14,
            'description' => 'Paket dasar untuk 2 pekan akses penuh',
            'features' => [
                'Akses semua fitur dasar',
                'Support via WhatsApp',
                'Update otomatis',
                'Akses 2 pekan penuh'
            ]
        ]);

        \App\Models\SubscriptionPlan::create([
            'name' => 'Premium Plan',
            'price' => 50000,
            'duration_days' => 30,
            'description' => 'Paket premium untuk 1 bulan akses penuh',
            'features' => [
                'Akses semua fitur premium',
                'Support prioritas via WhatsApp',
                'Update otomatis',
                'Akses 1 bulan penuh',
                'Fitur tambahan eksklusif'
            ]
        ]);
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};