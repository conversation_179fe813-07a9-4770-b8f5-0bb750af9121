<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sites', function (Blueprint $table) {
            // Cookies JSON fields
            $table->json('cookies')->nullable()->after('cookie_file_uploaded_at');
            $table->json('cookies_json')->nullable()->after('cookies');

            // Alternative accounts
            $table->json('alternative_accounts')->nullable()->after('cookies_json');

            // Custom redirect fields
            $table->boolean('enable_custom_redirect')->default(false)->after('additional_script');
            $table->string('redirect_title')->nullable()->after('enable_custom_redirect');
            $table->text('redirect_content')->nullable()->after('redirect_title');
            $table->string('redirect_url')->nullable()->after('redirect_content');
            $table->integer('redirect_delay')->default(5)->after('redirect_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sites', function (Blueprint $table) {
            $table->dropColumn([
                'cookies',
                'cookies_json',
                'alternative_accounts',
                'login_url',
                'email_selector',
                'password_selector',
                'submit_selector',
                'additional_script',
                'js_after_submit',
                'enable_custom_redirect',
                'redirect_title',
                'redirect_content',
                'redirect_url',
                'redirect_delay'
            ]);
        });
    }
};
