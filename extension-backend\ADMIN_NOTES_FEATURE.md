# Fitur Catatan Admin untuk Laporan Sites

## Overview

Fitur ini memungkinkan admin untuk memberikan respon dan catatan terkait laporan masalah sites yang dilaporkan oleh pengguna. Sistem ini terintegrasi dengan Filament admin panel dan API v2.

## Database Structure

### Tabel `reports`
Field yang terkait dengan catatan admin:
- `admin_notes` (TEXT) - Catatan dari admin terkait laporan
- `resolved_by` (BIGINT) - ID admin yang menyelesaikan laporan
- `resolved_at` (TIMESTAMP) - Waktu penyelesaian laporan
- `status` (ENUM) - Status laporan: pending, investigating, resolved, dismissed

## Filament Admin Interface

### 1. **ViewReport Page** (`ViewReport.php`)

#### **Header Actions:**
- **Selidiki** - Mengubah status menjadi "investigating"
- **Selesaikan** - Menandai laporan sebagai resolved dengan catatan
- **Tolak** - Menandai laporan sebagai dismissed dengan alasan
- **Tambah Catatan** - Menambahkan catatan tambahan ke laporan

#### **Infolist Sections:**
1. **Informasi Laporan**
   - <PERSON>, <PERSON><PERSON>us, ID Situs
   - Jenis Masalah dengan color coding
   - Status dengan badge
   - Pesan kustom dari pengguna

2. **Detail Teknis**
   - Waktu laporan dan pembuatan
   - User Agent browser

3. **Catatan Admin & Penyelesaian**
   - Catatan admin dengan formatting
   - Info admin yang menyelesaikan
   - Waktu penyelesaian

4. **Statistik Terkait**
   - Jumlah laporan serupa untuk site yang sama
   - Jumlah laporan dengan jenis yang sama
   - Laporan terbaru untuk site yang sama (7 hari)

### 2. **ReportResource** (`ReportResource.php`)

#### **Table Actions:**
- **Selidiki** - Quick action untuk mengubah status
- **Selesaikan** - Form modal untuk menyelesaikan dengan catatan
- **Tolak** - Form modal untuk menolak dengan alasan

#### **Bulk Actions:**
- **Selesaikan Massal** - Menyelesaikan beberapa laporan sekaligus
- **Tolak Massal** - Menolak beberapa laporan sekaligus

## API Endpoints

### 1. **GET /api/v2/reports/{id}**
Mendapatkan detail laporan dengan catatan admin

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "site_id": 123,
    "site_name": "Example Site",
    "report_type": "login_issue",
    "report_type_name": "Masalah Login",
    "custom_message": "Tidak bisa login dengan akun yang benar",
    "user_agent": "Mozilla/5.0...",
    "reported_at": "2024-01-15T10:30:00Z",
    "status": "resolved",
    "status_name": "Diselesaikan",
    "admin_notes": "Masalah telah diperbaiki dengan update server",
    "resolved_by": 1,
    "resolved_at": "2024-01-15T14:30:00Z",
    "statistics": {
      "similar_reports_count": 3,
      "same_type_reports_count": 15,
      "recent_reports_same_site": 1
    },
    "admin_response": {
      "has_response": true,
      "response_text": "Masalah telah diperbaiki dengan update server",
      "response_date": "2024-01-15T14:30:00Z",
      "is_resolved": true,
      "resolution_type": "resolved"
    }
  }
}
```

### 2. **GET /api/v2/reports/site/{siteId}**
Mendapatkan laporan untuk site tertentu dengan opsi catatan admin

**Parameters:**
- `status` (optional) - Filter berdasarkan status
- `limit` (optional) - Limit hasil (default: 10, max: 50)
- `include_admin_notes` (optional) - Include catatan admin (default: false)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "report_type": "login_issue",
      "report_type_name": "Masalah Login",
      "custom_message": "Tidak bisa login",
      "reported_at": "2024-01-15T10:30:00Z",
      "status": "resolved",
      "status_name": "Diselesaikan",
      "has_admin_response": true,
      "admin_notes": "Masalah telah diperbaiki",
      "resolved_by": 1,
      "resolved_at": "2024-01-15T14:30:00Z"
    }
  ],
  "meta": {
    "site_id": 123,
    "total_count": 1,
    "has_admin_responses": 1
  }
}
```

## Model Methods

### Report Model

#### **markAsResolved($adminId, $notes)**
```php
public function markAsResolved($adminId, $notes)
{
    $this->update([
        'status' => self::STATUS_RESOLVED,
        'admin_notes' => $notes,
        'resolved_by' => $adminId,
        'resolved_at' => now(),
    ]);
}
```

#### **markAsDismissed($adminId, $notes)**
```php
public function markAsDismissed($adminId, $notes)
{
    $this->update([
        'status' => self::STATUS_DISMISSED,
        'admin_notes' => $notes,
        'resolved_by' => $adminId,
        'resolved_at' => now(),
    ]);
}
```

## Status Flow

```
pending → investigating → resolved/dismissed
```

### Status Descriptions:
- **pending** - Laporan baru yang belum ditangani
- **investigating** - Laporan sedang diselidiki oleh admin
- **resolved** - Laporan telah diselesaikan dengan solusi
- **dismissed** - Laporan ditolak/tidak valid

## Color Coding

### Report Types:
- **Danger (Red):** login_issue, site_down, security_issue
- **Warning (Yellow):** subscription_expired, slow_loading, broken_features
- **Secondary (Gray):** content_issue, other

### Status:
- **Warning (Yellow):** pending
- **Info (Blue):** investigating
- **Success (Green):** resolved
- **Danger (Red):** dismissed

## Notifications

### Admin Actions:
- **Investigate:** "Status diperbarui - Laporan sedang diselidiki"
- **Resolve:** "Laporan diselesaikan - Laporan telah ditandai sebagai selesai"
- **Dismiss:** "Laporan ditolak - Laporan telah ditolak"
- **Add Note:** "Catatan ditambahkan - Catatan admin berhasil ditambahkan"

## Security & Permissions

### Admin Requirements:
- Hanya admin yang dapat mengakses ViewReport page
- Hanya admin yang dapat menambahkan catatan
- Hanya admin yang dapat mengubah status laporan

### API Security:
- Endpoint detail laporan memerlukan autentikasi admin
- Catatan admin hanya ditampilkan untuk admin yang terautentikasi

## Usage Examples

### 1. **Menyelesaikan Laporan via Filament:**
1. Buka laporan di admin panel
2. Klik "Selesaikan"
3. Isi catatan penyelesaian
4. Submit form
5. Status berubah menjadi "resolved"

### 2. **Menambah Catatan Tambahan:**
1. Buka laporan yang sudah ada
2. Klik "Tambah Catatan"
3. Isi catatan tambahan
4. Catatan ditambahkan dengan timestamp

### 3. **Mengakses Detail via API:**
```bash
curl -X GET "http://localhost:8000/api/v2/reports/1" \
  -H "Authorization: Bearer {token}" \
  -H "Accept: application/json"
```

## Best Practices

### 1. **Catatan Admin:**
- Berikan penjelasan yang jelas dan detail
- Sertakan langkah-langkah yang telah diambil
- Gunakan bahasa yang profesional dan mudah dipahami

### 2. **Status Management:**
- Ubah status menjadi "investigating" saat mulai menangani
- Selalu berikan catatan saat menyelesaikan atau menolak
- Gunakan "dismiss" hanya untuk laporan yang tidak valid

### 3. **Follow-up:**
- Monitor laporan serupa untuk pola masalah
- Gunakan statistik untuk identifikasi masalah berulang
- Update catatan jika ada perkembangan baru

## Troubleshooting

### Common Issues:
1. **Catatan tidak tersimpan:** Pastikan admin memiliki permission yang tepat
2. **Status tidak berubah:** Check validasi form dan database constraints
3. **API tidak mengembalikan catatan:** Pastikan parameter `include_admin_notes=true`

### Debugging:
- Check log Laravel untuk error details
- Verify database connection dan migration
- Ensure proper authentication untuk API endpoints

---

**Note:** Fitur ini dirancang untuk meningkatkan komunikasi antara admin dan pengguna terkait masalah sites, serta memberikan tracking yang lebih baik untuk resolusi masalah.
