<?php $__env->startSection('title', 'Status Laporan Sites'); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        /* Reset dan Base Styles */
        * {
            box-sizing: border-box;
        }

        .text-red-600 {
            color: #DC2626;
        }

        .text-yellow-600 {
            color: #F59E0B;
        }

        .text-green-600 {
            color: #22C55E;
        }

        /* Container Utama */
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Header Section */
        .dashboard-header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            text-align: center;
        }

        .header-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .logout-btn {
            background: #DC2626;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .logout-btn:hover {
            background: #B91C1C;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        /* Grid Layout untuk Desktop */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        /* Extension Cards */
        .extension-card {
            background: linear-gradient(135deg, #462c2c, #705656);
            color: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .extension-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
        }

        .extension-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .extension-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .extension-card p {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .download-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            flex: 1;
            min-width: 150px;
            justify-content: center;
        }

        .download-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .extension-version {
            font-size: 0.875rem;
            opacity: 0.8;
            display: block;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            border-left: 3px solid rgba(255, 255, 255, 0.3);
        }

        .no-extension-message {
            font-style: italic;
            opacity: 0.8;
            text-align: center;
            padding: 2rem;
        }

        /* Status Cards Grid */
        .status-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), transparent);
        }

        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .status-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
            position: relative;
        }

        .status-icon svg {
            width: 1.5rem;
            height: 1.5rem;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }

        .status-icon .text-green-400 {
            color: #4ade80;
        }

        .status-icon .text-red-400 {
            color: #f87171;
        }

        .status-icon .text-blue-400 {
            color: #60a5fa;
        }

        .status-icon .text-orange-400 {
            color: #fb923c;
        }

        .status-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0.75rem;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .status-card:hover .status-icon::before {
            opacity: 1;
        }

        .status-info h4 {
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin: 0 0 0.5rem 0;
            opacity: 0.8;
        }

        .status-info .status-value {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        /* Actions Section */
        .actions-section {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .actions-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            color: white;
            padding: 1.25rem 1.5rem;
            border-radius: 1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.25);
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .action-btn svg {
            width: 1.25rem;
            height: 1.25rem;
            flex-shrink: 0;
        }

        /* Warning Alert */
        .warning-alert {
            background: linear-gradient(135deg, #ff7e5f, #feb47b);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 25px rgba(255, 126, 95, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .warning-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .warning-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        .warning-text {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        .warning-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.5rem;
        }

        /* Reports Grid Styling */
        .reports-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .report-card {
            background: rgba(31, 41, 55, 0.6);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 0.75rem;
            padding: 1.25rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .report-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.5), rgba(16, 185, 129, 0.5));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .report-card:hover {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(107, 114, 128, 0.7);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .report-card:hover::before {
            opacity: 1;
        }

        .report-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-text {
            font-size: 0.75rem;
            font-weight: 500;
            color: #d1d5db;
        }

        .report-time {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        .report-content {
            margin-bottom: 0.75rem;
        }

        .report-title {
            font-weight: 500;
            color: white;
            font-size: 0.875rem;
            margin: 0 0 0.25rem 0;
            line-height: 1.25;
        }

        .report-description {
            color: #9ca3af;
            font-size: 0.75rem;
            line-height: 1.4;
            margin: 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .report-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .report-id {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .detail-btn {
            color: #60a5fa;
            font-size: 0.75rem;
            font-weight: 500;
            background: none;
            border: none;
            cursor: pointer;
            transition: color 0.2s ease;
            padding: 0;
        }

        .detail-btn:hover {
            color: #93c5fd;
        }

        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: inline-block;
        text-align: center;
        }

        .warning-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Filter Section Styles */
        .filter-section {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            opacity: 0.8;
        }

        .filter-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            color: white;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
        }

        .filter-select option {
            background: #2d1b1b;
            color: white;
        }

        /* Stats Grid Styles */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Table Section Styles */
        .table-section {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table-title {
            color: white;
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .sites-table {
            width: 100%;
            border-collapse: collapse;
            color: white;
        }

        .sites-table th,
        .sites-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sites-table th {
            background: rgba(255, 255, 255, 0.05);
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
            opacity: 0.8;
        }

        .sites-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-safe {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-danger {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-dot {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            background: currentColor;
        }

        .report-count {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        .action-button {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        /* Reports Section */
        .reports-section {
            margin-top: 2rem;
        }

        .report-card {
            background: rgba(31, 41, 55, 0.5);
            border: 1px solid rgb(55, 65, 81);
            border-radius: 0.5rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .report-card:hover {
            background: rgba(31, 41, 55, 0.7);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Responsive Design */
        @media (min-width: 640px) {
            .dashboard-container {
                padding: 0 2rem;
            }

            .header-content {
                flex-direction: row;
                justify-content: space-between;
                text-align: left;
            }

            .header-title {
                font-size: 2rem;
            }

            .download-buttons {
                flex-wrap: nowrap;
            }

            .download-btn {
                flex: none;
                min-width: auto;
            }
        }

        @media (min-width: 768px) {
            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .actions-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .reports-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .filter-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .reports-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* Mobile App Card Specific */
        .mobile-app-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 1rem 0;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dashboard-container>* {
            animation: fadeInUp 0.6s ease-out;
        }

        .dashboard-container>*:nth-child(2) {
            animation-delay: 0.1s;
        }

        .dashboard-container>*:nth-child(3) {
            animation-delay: 0.2s;
        }

        .dashboard-container>*:nth-child(4) {
            animation-delay: 0.3s;
        }
    </style>
<?php $__env->stopPush(); ?>


<?php $__env->startSection('content'); ?>
    <section class="bg-darkPink-900 relative py-28 md:py-28" style="min-height: 100vh;">
        <div class="absolute top-0 left-0 w-full h-full"><img class="w-full h-full object-cover"
                src="<?php echo e(asset('assets/img/lines-dashboard.svg')); ?>" alt="" /></div>
        <div class="dashboard-container">
            <div class="mx-auto relative z-10">
                <div class="dashboard-header">
                    <div class="header-content">
                        <h1 class="header-title">
                            Status Laporan Sites
                        </h1>
                        <form action="<?php echo e(route('user.dashboard')); ?>" method="GET" class="m-0">
                            <button type="submit" class="logout-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                    <polyline points="16 17 21 12 16 7"></polyline>
                                    <line x1="21" y1="12" x2="9" y2="12"></line>
                                </svg>
                                Kembali
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="filter-section">
                    <form method="GET" action="<?php echo e(route('user.site-reports')); ?>" class="filter-grid">
                        <div class="filter-group">
                            <label class="filter-label">Status Website</label>
                            <select class="filter-select" name="status" id="statusFilter">
                                <option value="all" <?php echo e($statusFilter == 'all' ? 'selected' : ''); ?>>Semua Status</option>
                                <option value="normal" <?php echo e($statusFilter == 'normal' ? 'selected' : ''); ?>>Normal</option>
                                <option value="warning" <?php echo e($statusFilter == 'warning' ? 'selected' : ''); ?>>Peringatan
                                </option>
                                <option value="reported" <?php echo e($statusFilter == 'reported' ? 'selected' : ''); ?>>Dilaporkan
                                </option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Status Laporan</label>
                            <select class="filter-select" name="report_status" id="reportStatusFilter">
                                <option value="all" <?php echo e($reportStatusFilter == 'all' ? 'selected' : ''); ?>>Semua Laporan
                                </option>
                                <option value="pending" <?php echo e($reportStatusFilter == 'pending' ? 'selected' : ''); ?>>Pending
                                </option>
                                <option value="investigating"
                                    <?php echo e($reportStatusFilter == 'investigating' ? 'selected' : ''); ?>>Sedang Diselidiki
                                </option>
                                <option value="resolved" <?php echo e($reportStatusFilter == 'resolved' ? 'selected' : ''); ?>>
                                    Terselesaikan</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Pencarian</label>
                            <input type="text" class="filter-select" name="search" placeholder="Cari nama website..."
                                value="<?php echo e($search); ?>" id="searchInput">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">&nbsp;</label>
                            <button type="submit"
                                class="filter-select bg-blue-600 hover:bg-blue-700 text-white font-medium">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number text-green-600"><?php echo e($statistics['safe'] ?? 0); ?></div>
                        <div class="stat-label">Website Aktif</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-yellow-600"><?php echo e($statistics['warning'] ?? 0); ?></div>
                        <div class="stat-label">Pemeliharaan</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-red-600"><?php echo e($statistics['danger'] ?? 0); ?></div>
                        <div class="stat-label">Bermasalah</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-blue-600"><?php echo e($statistics['total_reports'] ?? 0); ?></div>
                        <div class="stat-label">Total Laporan</div>
                    </div>
                </div>

                <!-- Website Management Table -->
                <div class="table-section">
                    <h2 class="table-title">Daftar Website dan Status Laporan</h2>
                    <div class="table-responsive">
                        <table class="sites-table" id="websitesTable">
                            <thead>
                                <tr>
                                    <th>Website</th>
                                    <th>Kategori</th>
                                    <th>Status Sesi</th>
                                    <th>Status Laporan</th>
                                    <th>Laporan</th>
                                    <th>Terakhir Diakses</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <?php
                                        $hasActiveReports =
                                            $site->pending_reports_count > 0 || $site->investigating_reports_count > 0;
                                        $statusClass = 'status-safe';
                                        $statusText = 'Normal';
                                        $iconClass = 'text-green-600';
                                        $iconBg = 'bg-green-100';

                                        if ($site->enable_custom_redirect) {
                                            $statusClass = 'status-warning';
                                            $statusText = 'Redirect Aktif';
                                            $iconClass = 'text-yellow-600';
                                            $iconBg = 'bg-yellow-100';
                                        } elseif ($hasActiveReports) {
                                            $statusClass = 'status-danger';
                                            $statusText = 'Dilaporkan';
                                            $iconClass = 'text-red-600';
                                            $iconBg = 'bg-red-100';
                                        }
                                    ?>
                                    <tr>
                                        <td>
                                            <div class="flex items-center gap-3">
                                                <div
                                                    class="w-6 h-6 <?php echo e($iconBg); ?> rounded-lg flex items-center justify-center">
                                                    <svg class="w-3 h-3 <?php echo e($iconClass); ?>" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <?php if($hasActiveReports): ?>
                                                            <path fill-rule="evenodd"
                                                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                                clip-rule="evenodd" />
                                                        <?php elseif($site->enable_custom_redirect): ?>
                                                            <path fill-rule="evenodd"
                                                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                                                clip-rule="evenodd" />
                                                        <?php else: ?>
                                                            <path fill-rule="evenodd"
                                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                clip-rule="evenodd" />
                                                        <?php endif; ?>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div class="font-semibold text-white">
                                                        <?php echo e($site->name ?? $site->domain); ?></div>
                                                    <div class="text-sm text-gray-400"><?php echo e($site->url); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span
                                                class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                                <?php echo e($site->category ?? 'Umum'); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge <?php echo e($statusClass); ?>">
                                                <span class="status-dot"></span>
                                                <?php echo e($statusText); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                                $reportStatus = 'Normal';
                                                $reportStatusClass = 'bg-green-100 text-green-800';

                                                if ($site->investigating_reports_count > 0) {
                                                    $reportStatus = 'Diselidiki';
                                                    $reportStatusClass = 'bg-orange-100 text-orange-800';
                                                } elseif ($site->pending_reports_count > 0) {
                                                    $reportStatus = 'Dilaporkan';
                                                    $reportStatusClass = 'bg-red-100 text-red-800';
                                                } elseif ($site->resolved_reports_count > 0) {
                                                    $reportStatus = 'Diselesaikan';
                                                    $reportStatusClass = 'bg-blue-100 text-blue-800';
                                                }
                                            ?>
                                            <span
                                                class="px-2 py-1 <?php echo e($reportStatusClass); ?> rounded-full text-xs font-medium">
                                                <?php echo e($reportStatus); ?>

                                            </span>
                                            <?php if($site->investigating_reports_count > 0): ?>
                                                <div class="text-xs text-orange-400 mt-1">
                                                    <?php echo e($site->investigating_reports_count); ?> sedang diselidiki</div>
                                            <?php endif; ?>
                                            <?php if($site->pending_reports_count > 0): ?>
                                                <div class="text-xs text-red-400 mt-1"><?php echo e($site->pending_reports_count); ?>

                                                    menunggu</div>
                                            <?php endif; ?>
                                            <?php if($site->resolved_reports_count > 0): ?>
                                                <div class="text-xs text-blue-400 mt-1">
                                                    <?php echo e($site->resolved_reports_count); ?> diselesaikan</div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="report-count"><?php echo e($site->reports_count); ?> Total Laporan</span>
                                        </td>
                                        <td class="text-gray-300">
                                            <?php echo e($site->updated_at ? $site->updated_at->diffForHumans() : 'Tidak diketahui'); ?>

                                        </td>
                                        <td>
                                            <?php if($site->reports_count > 0): ?>
                                                <button
                                                    onclick="showReports(<?php echo e($site->id); ?>, '<?php echo e($site->name ?? $site->domain); ?>')"
                                                    class="action-button">
                                                    Lihat Laporan
                                                </button>
                                            <?php else: ?>
                                                <button disabled class="action-button opacity-50 cursor-not-allowed"
                                                    title="Tidak ada laporan">
                                                    Lihat Laporan
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-gray-400 py-8">
                                            <div class="flex flex-col items-center gap-2">
                                                <svg class="w-12 h-12 text-gray-500" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                <p class="text-lg font-medium">Tidak ada website ditemukan</p>
                                                <p class="text-sm">Coba ubah filter atau kata kunci pencarian</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Recent Reports Section -->
                    <?php if(isset($reports) && $reports->count() > 0): ?>
                        <div class="reports-section mt-8">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-white mb-0">Laporan Terbaru</h3>
                            </div>

                            <div class="reports-grid">
                                <?php $__currentLoopData = $reports->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $report): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="report-card">
                                        <div class="report-header">
                                            <div class="status-badge">
                                                <?php
                                                    $statusColor = match ($report->status) {
                                                        'pending' => '#f59e0b',
                                                        'investigating' => '#3b82f6',
                                                        'resolved' => '#10b981',
                                                        'rejected' => '#ef4444',
                                                        default => '#6b7280',
                                                    };
                                                    $statusText = match ($report->status) {
                                                        'pending' => 'Pending',
                                                        'investigating' => 'Diselidiki',
                                                        'resolved' => 'Selesai',
                                                        'rejected' => 'Ditolak',
                                                        default => 'Unknown',
                                                    };
                                                ?>
                                                <span class="status-dot"
                                                    style="background-color: <?php echo e($statusColor); ?>;"></span>
                                                <span class="status-text"><?php echo e($statusText); ?></span>
                                            </div>
                                            <span class="report-time">
                                                <?php echo e($report->created_at->diffForHumans()); ?>

                                            </span>
                                        </div>

                                        <div class="report-content">
                                            <h4 class="report-title">
                                                <?php echo e($report->site->name ?? ($report->site->domain ?? 'Website Tidak Diketahui')); ?>

                                            </h4>
                                            <p class="report-description">
                                                <?php echo e(Str::limit($report->description, 80)); ?>

                                            </p>
                                        </div>

                                        <div class="report-footer">
                                            <span class="report-id">
                                                ID: #<?php echo e(str_pad($report->id, 3, '0', STR_PAD_LEFT)); ?>

                                            </span>
                                            <button onclick="showReportDetail(<?php echo e($report->id); ?>)" class="detail-btn">
                                                Detail →
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Pagination -->
                    <div class="pagination-wrapper" style="margin-top: 20px !important;">
                        <div
                            class="flex items-center justify-between bg-gray-800/50 border border-gray-700 rounded-lg p-4">
                            <div class="flex items-center gap-2 text-sm text-gray-400">
                                <span>Menampilkan</span>
                                <span id="currentStart" class="font-medium text-white">1</span>
                                <span>-</span>
                                <span id="currentEnd" class="font-medium text-white">10</span>
                                <span>dari</span>
                                <span id="totalItems" class="font-medium text-white"><?php echo e($sites->total()); ?></span>
                                <span>website</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <button id="prevPage"
                                    class="pagination-btn px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled>
                                    ← Sebelumnya
                                </button>

                                <div id="pageNumbers" class="flex items-center gap-1">
                                    <!-- Page numbers will be generated by JavaScript -->
                                </div>

                                <button id="nextPage"
                                    class="pagination-btn px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                    Selanjutnya →
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const categoryFilter = document.getElementById('categoryFilter');
            const sessionFilter = document.getElementById('sessionFilter');
            const reportFilter = document.getElementById('reportFilter');
            const searchInput = document.getElementById('searchInput');
            const tableRows = document.querySelectorAll('#websitesTable tbody tr');

            function filterTable() {
                const categoryValue = categoryFilter.value.toLowerCase();
                const sessionValue = sessionFilter.value.toLowerCase();
                const reportValue = reportFilter.value.toLowerCase();
                const searchValue = searchInput.value.toLowerCase();

                tableRows.forEach(row => {
                    const category = row.getAttribute('data-category');
                    const session = row.getAttribute('data-session');
                    const reports = row.getAttribute('data-reports');
                    const websiteName = row.querySelector('.font-semibold').textContent.toLowerCase();

                    const categoryMatch = !categoryValue || category === categoryValue;
                    const sessionMatch = !sessionValue || session === sessionValue;
                    const reportMatch = !reportValue || reports === reportValue;
                    const searchMatch = !searchValue || websiteName.includes(searchValue);

                    if (categoryMatch && sessionMatch && reportMatch && searchMatch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            categoryFilter.addEventListener('change', filterTable);
            sessionFilter.addEventListener('change', filterTable);
            reportFilter.addEventListener('change', filterTable);
            searchInput.addEventListener('input', filterTable);
        });



        function showReports(siteId, siteName) {
            // Menampilkan loading dialog
            Swal.fire({
                title: `Laporan untuk ${siteName}`,
                html: `
                <div class="text-left">
                    <p class="mb-4">Memuat data laporan...</p>
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                </div>
            `,
                showConfirmButton: false,
                allowOutsideClick: false,
                width: '800px',
                padding: '2rem'
            });

            // Fetch data laporan dari API
            fetch(`/dashboard/sites/${siteId}/reports`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.reports.length > 0) {
                        let reportsHtml = '<div class="text-left space-y-4">';

                        data.reports.forEach(report => {
                            const statusColors = {
                                'pending': {
                                    bg: 'bg-yellow-50',
                                    border: 'border-yellow-200',
                                    dot: 'bg-yellow-500',
                                    text: 'text-yellow-800',
                                    desc: 'text-yellow-700'
                                },
                                'investigating': {
                                    bg: 'bg-blue-50',
                                    border: 'border-blue-200',
                                    dot: 'bg-blue-500',
                                    text: 'text-blue-800',
                                    desc: 'text-blue-700'
                                },
                                'resolved': {
                                    bg: 'bg-green-50',
                                    border: 'border-green-200',
                                    dot: 'bg-green-500',
                                    text: 'text-green-800',
                                    desc: 'text-green-700'
                                },
                                'rejected': {
                                    bg: 'bg-red-50',
                                    border: 'border-red-200',
                                    dot: 'bg-red-500',
                                    text: 'text-red-800',
                                    desc: 'text-red-700'
                                }
                            };

                            const colors = statusColors[report.status] || statusColors['pending'];
                            const statusText = {
                                'pending': 'Pending',
                                'investigating': 'Diselidiki',
                                'resolved': 'Selesai',
                                'rejected': 'Ditolak'
                            } [report.status] || 'Unknown';

                            reportsHtml += `
                            <div class="${colors.bg} border ${colors.border} rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="w-3 h-3 ${colors.dot} rounded-full mr-2"></span>
                                    <strong class="${colors.text}">Laporan ${statusText} #${String(report.id).padStart(3, '0')}</strong>
                                </div>
                                <p class="${colors.desc} text-sm">${report.description}</p>
                                <p class="text-gray-600 text-xs mt-2">Dilaporkan: ${report.created_at_human}</p>
                                ${report.updated_at_human ? `<p class="text-gray-600 text-xs">Diperbarui: ${report.updated_at_human}</p>` : ''}
                            </div>
                        `;
                        });

                        reportsHtml += '</div>';

                        Swal.update({
                            title: `Laporan untuk ${siteName}`,
                            html: reportsHtml,
                            showConfirmButton: true,
                            confirmButtonText: 'Tutup',
                            confirmButtonColor: '#6B7280'
                        });
                    } else {
                        Swal.update({
                            title: `Laporan untuk ${siteName}`,
                            html: `
                            <div class="text-center">
                                <div class="mb-4">
                                    <svg class="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <p class="text-gray-600">Tidak ada laporan untuk website ini</p>
                            </div>
                        `,
                            showConfirmButton: true,
                            confirmButtonText: 'Tutup',
                            confirmButtonColor: '#6B7280'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.update({
                        title: 'Error',
                        html: '<p class="text-red-600">Terjadi kesalahan saat memuat data laporan</p>',
                        showConfirmButton: true,
                        confirmButtonText: 'Tutup',
                        confirmButtonColor: '#EF4444'
                    });
                });
        }

        // Fungsi untuk menampilkan detail laporan
        function showReportDetail(reportId) {
            fetch(`/dashboard/reports/${reportId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const report = data.report;
                        const statusColors = {
                            'pending': 'text-yellow-600',
                            'investigating': 'text-blue-600',
                            'resolved': 'text-green-600',
                            'rejected': 'text-red-600'
                        };

                        Swal.fire({
                            title: `Detail Laporan #${String(report.id).padStart(3, '0')}`,
                            html: `
                            <div class="text-left space-y-4">
                                <div class="border-b pb-4">
                                    <h4 class="font-semibold text-gray-800 mb-2">Website</h4>
                                    <p class="text-gray-600">${report.site_name || report.site_domain}</p>
                                </div>

                                <div class="border-b pb-4">
                                    <h4 class="font-semibold text-gray-800 mb-2">Status</h4>
                                    <span class="px-2 py-1 rounded-full text-xs font-medium ${statusColors[report.status] || 'text-gray-600'}">
                                        ${report.status_text}
                                    </span>
                                </div>

                                <div class="border-b pb-4">
                                    <h4 class="font-semibold text-gray-800 mb-2">Deskripsi Masalah</h4>
                                    <p class="text-gray-600">${report.description}</p>
                                </div>

                                ${report.admin_notes ? `
                                        <div class="border-b pb-4">
                                            <h4 class="font-semibold text-gray-800 mb-2 flex items-center">
                                                <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                                                </svg>
                                                Respon Admin
                                            </h4>
                                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                                <p class="text-blue-800 text-sm whitespace-pre-wrap">${report.admin_notes}</p>
                                                ${report.resolved_at ? `
                                        <div class="mt-2 pt-2 border-t border-blue-200">
                                            <p class="text-blue-600 text-xs">
                                                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                Direspon: ${new Date(report.resolved_at).toLocaleDateString('id-ID', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                })}
                                            </p>
                                        </div>
                                        ` : ''}
                                            </div>
                                        </div>
                                        ` : ''}

                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <h5 class="font-medium text-gray-700">Dilaporkan</h5>
                                        <p class="text-gray-600">${report.created_at_human}</p>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-700">Terakhir Diperbarui</h5>
                                        <p class="text-gray-600">${report.updated_at_human}</p>
                                    </div>
                                </div>

                                ${report.status === 'resolved' && report.admin_notes ? `
                                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-green-800 font-medium text-sm">Masalah telah diselesaikan</span>
                                            </div>
                                        </div>
                                        ` : ''}

                                ${report.status === 'dismissed' && report.admin_notes ? `
                                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-red-800 font-medium text-sm">Laporan ditolak</span>
                                            </div>
                                        </div>
                                        ` : ''}

                                ${report.status === 'investigating' ? `
                                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-orange-800 font-medium text-sm">Laporan sedang diselidiki oleh admin</span>
                                            </div>
                                        </div>
                                        ` : ''}

                                ${report.status === 'pending' ? `
                                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-yellow-800 font-medium text-sm">Menunggu tinjauan admin</span>
                                            </div>
                                        </div>
                                        ` : ''}
                            </div>
                        `,
                            width: '700px',
                            confirmButtonText: 'Tutup',
                            confirmButtonColor: '#6B7280'
                        });
                    } else {
                        Swal.fire('Error', 'Gagal memuat detail laporan', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire('Error', 'Terjadi kesalahan saat memuat detail laporan', 'error');
                });
        }

        // Fungsi untuk update status website di tabel
        function updateWebsiteStatus(websiteName, newStatus) {
            const rows = document.querySelectorAll('#websitesTable tbody tr');
            rows.forEach(row => {
                const nameElement = row.querySelector('.font-semibold');
                if (nameElement && nameElement.textContent === websiteName) {
                    const statusBadge = row.querySelector('.status-badge');
                    const actionButton = row.querySelector('.view-reports-btn');

                    if (newStatus === 'redirect_active') {
                        statusBadge.innerHTML = '<span class="status-dot"></span>Redirect Aktif';
                        statusBadge.className = 'status-badge status-info';
                        actionButton.textContent = 'Kelola';
                        actionButton.className = 'view-reports-btn';
                        actionButton.setAttribute('onclick', `manageWebsite('${websiteName}', 'redirect_active')`);
                    }
                }
            });
        }

        // Fungsi untuk menampilkan notifikasi
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-600' :
            type === 'error' ? 'bg-red-600' :
            type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
        }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Pagination variables
        let currentPage = 1;
        let totalPages = <?php echo e($sites->lastPage()); ?>;
        let totalItems = <?php echo e($sites->total()); ?>;
        let itemsPerPage = <?php echo e($sites->perPage()); ?>;
        let currentFilter = '<?php echo e(request('filter', 'all')); ?>';
        let currentSearch = '<?php echo e(request('search', '')); ?>';

        // Initialize pagination
        updatePaginationInfo();
        generatePageNumbers();

        // Pagination event listeners
        document.getElementById('prevPage').addEventListener('click', () => {
            if (currentPage > 1) {
                loadPage(currentPage - 1);
            }
        });

        document.getElementById('nextPage').addEventListener('click', () => {
            if (currentPage < totalPages) {
                loadPage(currentPage + 1);
            }
        });

        // Function to load page data
        function loadPage(page) {
            const url = new URL(window.location.href);
            url.searchParams.set('page', page);
            if (currentFilter !== 'all') {
                url.searchParams.set('filter', currentFilter);
            }
            if (currentSearch) {
                url.searchParams.set('search', currentSearch);
            }

            fetch(url.toString(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');

                    // Update table content
                    const newTableBody = doc.querySelector('#websitesTable tbody');
                    if (newTableBody) {
                        document.querySelector('#websitesTable tbody').innerHTML = newTableBody.innerHTML;
                    }

                    // Update recent reports
                    const newRecentReports = doc.querySelector(
                        '.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-6');
                    if (newRecentReports) {
                        document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-6').innerHTML =
                            newRecentReports.innerHTML;
                    }

                    currentPage = page;
                    updatePaginationInfo();
                    generatePageNumbers();

                    // Update URL without reload
                    window.history.pushState({}, '', url.toString());
                })
                .catch(error => {
                    console.error('Error loading page:', error);
                    showNotification('Gagal memuat halaman', 'error');
                });
        }

        // Function to update pagination info
        function updatePaginationInfo() {
            const start = ((currentPage - 1) * itemsPerPage) + 1;
            const end = Math.min(currentPage * itemsPerPage, totalItems);

            document.getElementById('currentStart').textContent = start;
            document.getElementById('currentEnd').textContent = end;
            document.getElementById('totalItems').textContent = totalItems;

            // Update button states
            document.getElementById('prevPage').disabled = currentPage <= 1;
            document.getElementById('nextPage').disabled = currentPage >= totalPages;
        }

        // Function to generate page numbers
        function generatePageNumbers() {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            pageNumbersContainer.innerHTML = '';

            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                i === currentPage
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600 text-white'
            }`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => loadPage(i));
                pageNumbersContainer.appendChild(pageBtn);
            }
        }

        // Update filter and search to work with pagination
        document.getElementById('filterSelect').addEventListener('change', function() {
            currentFilter = this.value;
            currentPage = 1;
            loadPage(1);
        });

        document.getElementById('searchInput').addEventListener('input', function() {
            currentSearch = this.value;
            currentPage = 1;
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                loadPage(1);
            }, 500);
        });

        // Auto-refresh data setiap 30 detik
        setInterval(() => {
            // Refresh statistics dan data tabel
            fetch('/api/website-stats')
                .then(response => response.json())
                .then(data => {
                    // Update statistics cards
                    document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = data.safe || 0;
                    document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = data.warning ||
                        0;
                    document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = data.danger ||
                        0;
                    document.querySelector('.stat-card:nth-child(4) .stat-number').textContent = data
                        .total_reports || 0;
                })
                .catch(error => console.error('Error refreshing stats:', error));
        }, 30000);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.appLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/dashboard/site-reports.blade.php ENDPATH**/ ?>