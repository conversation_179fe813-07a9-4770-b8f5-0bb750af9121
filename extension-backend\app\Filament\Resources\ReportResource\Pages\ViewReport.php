<?php

namespace App\Filament\Resources\ReportResource\Pages;

use App\Filament\Resources\ReportResource;
use App\Models\Report;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ViewReport extends ViewRecord
{
    protected static string $resource = ReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('investigate')
                ->label('Selidiki')
                ->icon('heroicon-o-magnifying-glass')
                ->color('primary')
                ->visible(fn(): bool => $this->record->status === Report::STATUS_PENDING)
                ->action(function (): void {
                    $this->record->update(['status' => Report::STATUS_INVESTIGATING]);
                    Notification::make()
                        ->title('Status diperbarui')
                        ->body('Laporan sedang diselidiki')
                        ->success()
                        ->send();
                }),

            Actions\Action::make('resolve')
                ->label('Selesaikan')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn(): bool => in_array($this->record->status, [Report::STATUS_PENDING, Report::STATUS_INVESTIGATING]))
                ->form([
                    Textarea::make('admin_notes')
                        ->label('Catatan Penyelesaian')
                        ->required()
                        ->rows(4)
                        ->placeholder('Jelaskan bagaimana masalah ini diselesaikan...')
                        ->helperText('Catatan ini akan membantu untuk referensi masalah serupa di masa depan.'),
                ])
                ->action(function (array $data): void {
                    $this->record->markAsResolved(Auth::id(), $data['admin_notes']);
                    Notification::make()
                        ->title('Laporan diselesaikan')
                        ->body('Laporan telah ditandai sebagai selesai')
                        ->success()
                        ->send();
                }),

            Actions\Action::make('dismiss')
                ->label('Tolak')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn(): bool => in_array($this->record->status, [Report::STATUS_PENDING, Report::STATUS_INVESTIGATING]))
                ->form([
                    Textarea::make('admin_notes')
                        ->label('Alasan Penolakan')
                        ->required()
                        ->rows(4)
                        ->placeholder('Jelaskan mengapa laporan ini ditolak...')
                        ->helperText('Berikan alasan yang jelas untuk penolakan laporan ini.'),
                ])
                ->action(function (array $data): void {
                    $this->record->markAsDismissed(Auth::id(), $data['admin_notes']);
                    Notification::make()
                        ->title('Laporan ditolak')
                        ->body('Laporan telah ditolak')
                        ->warning()
                        ->send();
                }),

            Actions\Action::make('add_note')
                ->label('Tambah Catatan')
                ->icon('heroicon-o-pencil-square')
                ->color('gray')
                ->form([
                    Textarea::make('additional_notes')
                        ->label('Catatan Tambahan')
                        ->required()
                        ->rows(3)
                        ->placeholder('Tambahkan catatan atau update terkait laporan ini...')
                        ->helperText('Catatan akan ditambahkan ke catatan admin yang sudah ada.'),
                ])
                ->action(function (array $data): void {
                    $currentNotes = $this->record->admin_notes ?? '';
                    $timestamp = now()->format('d/m/Y H:i');
                    $adminName = Auth::user()?->name ?? 'Admin';

                    $newNote = "\n\n[{$timestamp}] {$adminName}:\n{$data['additional_notes']}";

                    $this->record->update([
                        'admin_notes' => $currentNotes . $newNote
                    ]);

                    Notification::make()
                        ->title('Catatan ditambahkan')
                        ->body('Catatan admin berhasil ditambahkan')
                        ->success()
                        ->send();
                }),

            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Laporan')
                    ->icon('heroicon-o-document-text')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('id')
                                    ->label('ID Laporan')
                                    ->badge()
                                    ->color('gray'),

                                TextEntry::make('site_name')
                                    ->label('Nama Situs')
                                    ->icon('heroicon-o-globe-alt')
                                    ->weight(FontWeight::Bold),

                                TextEntry::make('site_id')
                                    ->label('ID Situs')
                                    ->badge()
                                    ->color('primary'),
                            ]),

                        Grid::make(2)
                            ->schema([
                                TextEntry::make('report_type')
                                    ->label('Jenis Masalah')
                                    ->formatStateUsing(fn(string $state): string => Report::REPORT_TYPES[$state] ?? $state)
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'login_issue', 'site_down', 'security_issue' => 'danger',
                                        'subscription_expired', 'slow_loading', 'broken_features' => 'warning',
                                        'content_issue', 'other' => 'secondary',
                                        default => 'gray',
                                    }),

                                TextEntry::make('status')
                                    ->label('Status')
                                    ->formatStateUsing(fn(string $state): string => Report::STATUSES[$state] ?? $state)
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        Report::STATUS_PENDING => 'warning',
                                        Report::STATUS_INVESTIGATING => 'info',
                                        Report::STATUS_RESOLVED => 'success',
                                        Report::STATUS_DISMISSED => 'danger',
                                        default => 'gray',
                                    }),
                            ]),

                        TextEntry::make('custom_message')
                            ->label('Pesan Kustom')
                            ->columnSpanFull()
                            ->visible(fn($record): bool => !empty($record->custom_message))
                            ->formatStateUsing(fn(?string $state): HtmlString => new HtmlString(
                                '<div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border-l-4 border-blue-500">' .
                                    '<p class="text-sm text-gray-700 dark:text-gray-300 italic">' . e($state) . '</p>' .
                                    '</div>'
                            )),
                    ]),

                Section::make('Detail Teknis')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('reported_at')
                                    ->label('Waktu Laporan')
                                    ->dateTime('d F Y, H:i')
                                    ->icon('heroicon-o-clock'),

                                TextEntry::make('created_at')
                                    ->label('Dibuat di Sistem')
                                    ->dateTime('d F Y, H:i')
                                    ->icon('heroicon-o-calendar'),
                            ]),

                        TextEntry::make('user_agent')
                            ->label('User Agent')
                            ->columnSpanFull()
                            ->visible(fn($record): bool => !empty($record->user_agent))
                            ->formatStateUsing(fn(?string $state): HtmlString => new HtmlString(
                                '<div class="bg-gray-100 dark:bg-gray-700 p-2 rounded font-mono text-xs text-gray-600 dark:text-gray-400 break-all">' .
                                    e($state) .
                                    '</div>'
                            )),
                    ])
                    ->collapsible(),

                Section::make('Catatan Admin & Penyelesaian')
                    ->icon('heroicon-o-chat-bubble-left-ellipsis')
                    ->schema([
                        TextEntry::make('admin_notes')
                            ->label('Catatan Admin')
                            ->columnSpanFull()
                            ->visible(fn($record): bool => !empty($record->admin_notes))
                            ->formatStateUsing(fn(?string $state): HtmlString => new HtmlString(
                                '<div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">' .
                                    '<div class="prose prose-sm max-w-none dark:prose-invert">' .
                                    '<pre class="whitespace-pre-wrap text-sm text-blue-900 dark:text-blue-100 font-normal">' . e($state) . '</pre>' .
                                    '</div>' .
                                    '</div>'
                            )),

                        Grid::make(2)
                            ->schema([
                                TextEntry::make('resolved_by')
                                    ->label('Diselesaikan Oleh')
                                    ->visible(fn($record): bool => !empty($record->resolved_by))
                                    ->formatStateUsing(
                                        fn(?int $state): string =>
                                        $state ? "Admin ID: {$state}" : 'Tidak diketahui'
                                    )
                                    ->badge()
                                    ->color('success'),

                                TextEntry::make('resolved_at')
                                    ->label('Waktu Penyelesaian')
                                    ->visible(fn($record): bool => !empty($record->resolved_at))
                                    ->dateTime('d F Y, H:i')
                                    ->icon('heroicon-o-check-circle'),
                            ])
                            ->visible(fn($record): bool => in_array($record->status, [Report::STATUS_RESOLVED, Report::STATUS_DISMISSED])),
                    ])
                    ->visible(fn($record): bool => !empty($record->admin_notes) || in_array($record->status, [Report::STATUS_RESOLVED, Report::STATUS_DISMISSED]))
                    ->collapsible(),

                Section::make('Statistik Terkait')
                    ->icon('heroicon-o-chart-bar')
                    ->schema([
                        TextEntry::make('similar_reports_count')
                            ->label('Laporan Serupa (Site yang Sama)')
                            ->getStateUsing(
                                fn($record): int =>
                                Report::where('site_id', $record->site_id)
                                    ->where('id', '!=', $record->id)
                                    ->count()
                            )
                            ->badge()
                            ->color(fn(int $state): string => $state > 5 ? 'danger' : ($state > 2 ? 'warning' : 'success')),

                        TextEntry::make('same_type_reports_count')
                            ->label('Laporan Jenis yang Sama')
                            ->getStateUsing(
                                fn($record): int =>
                                Report::where('report_type', $record->report_type)
                                    ->where('id', '!=', $record->id)
                                    ->count()
                            )
                            ->badge()
                            ->color('info'),

                        TextEntry::make('recent_reports_same_site')
                            ->label('Laporan Terbaru (Site yang Sama)')
                            ->getStateUsing(
                                fn($record): int =>
                                Report::where('site_id', $record->site_id)
                                    ->where('id', '!=', $record->id)
                                    ->where('created_at', '>=', now()->subDays(7))
                                    ->count()
                            )
                            ->badge()
                            ->color(fn(int $state): string => $state > 3 ? 'danger' : ($state > 1 ? 'warning' : 'success'))
                            ->helperText('Dalam 7 hari terakhir'),
                    ])
                    ->collapsible(),
            ]);
    }

    public function getTitle(): string
    {
        return "Laporan #{$this->record->id} - {$this->record->site_name}";
    }

    public function getSubheading(): ?string
    {
        $reportType = Report::REPORT_TYPES[$this->record->report_type] ?? $this->record->report_type;
        $status = Report::STATUSES[$this->record->status] ?? $this->record->status;

        return "{$reportType} • Status: {$status} • Dilaporkan " . $this->record->reported_at->diffForHumans();
    }
}
