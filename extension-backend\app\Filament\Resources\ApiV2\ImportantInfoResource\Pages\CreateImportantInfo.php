<?php

namespace App\Filament\Resources\ApiV2\ImportantInfoResource\Pages;

use App\Filament\Resources\ApiV2\ImportantInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateImportantInfo extends CreateRecord
{
    protected static string $resource = ImportantInfoResource::class;
    
    public function getTitle(): string
    {
        return 'Tambah Informasi Penting';
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Informasi berhasil ditambahkan')
            ->body('Informasi penting baru telah berhasil disimpan.');
    }
}