<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ChatGPT Cookies API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 Test ChatGPT Cookies API</h1>
    
    <div>
        <h3>1. Login Test</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="password">
        <button onclick="testLogin()">Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div>
        <h3>2. Get ChatGPT Cookies Test</h3>
        <button onclick="testGetChatGPTCookies()">Get ChatGPT Cookies</button>
        <div id="cookiesResult" class="result"></div>
    </div>

    <div>
        <h3>3. Test Cookie Injection</h3>
        <button onclick="testCookieInjection()">Test Cookie Injection</button>
        <div id="injectionResult" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000/api';
        let authToken = null;

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.token;
                    showResult('loginResult', `✅ Login berhasil! Token: ${authToken.substring(0, 20)}...`, 'success');
                } else {
                    showResult('loginResult', `❌ Login gagal: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testGetChatGPTCookies() {
            if (!authToken) {
                showResult('cookiesResult', '❌ Silakan login terlebih dahulu', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/sites/cookies`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ domain: 'chatgpt.com' })
                });

                const data = await response.json();
                
                if (data.success) {
                    const cookiesCount = data.data.cookies ? data.data.cookies.length : 0;
                    showResult('cookiesResult', 
                        `✅ Cookies ditemukan!<br>
                        📊 Jumlah cookies: ${cookiesCount}<br>
                        📁 Cookie file: ${data.data.has_cookie_file ? 'Ada' : 'Tidak ada'}<br>
                        📅 Upload time: ${data.data.cookie_file_uploaded_at || 'N/A'}<br>
                        <details>
                            <summary>Detail Cookies (klik untuk expand)</summary>
                            <pre>${JSON.stringify(data.data.cookies, null, 2)}</pre>
                        </details>`, 
                        'success'
                    );
                } else {
                    showResult('cookiesResult', `❌ Gagal mengambil cookies: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('cookiesResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testCookieInjection() {
            if (!authToken) {
                showResult('injectionResult', '❌ Silakan login terlebih dahulu', 'error');
                return;
            }

            try {
                // Simulasi injeksi cookie menggunakan Chrome Extension API
                // Karena ini adalah test di browser biasa, kita hanya akan menampilkan informasi
                showResult('injectionResult', 
                    `ℹ️ Test injeksi cookie:<br>
                    🎯 Target domain: chatgpt.com<br>
                    🔧 Method: Chrome Extension API<br>
                    📝 Status: Simulasi (perlu ekstensi untuk injeksi sebenarnya)<br>
                    <br>
                    Untuk test sebenarnya:<br>
                    1. Buka ekstensi Cookie Manager<br>
                    2. Login dengan akun yang sama<br>
                    3. Klik tombol "🚀 Buka chatgpt.com"<br>
                    4. Periksa console untuk log injeksi`, 
                    'info'
                );
            } catch (error) {
                showResult('injectionResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-test saat halaman dimuat
        window.addEventListener('load', () => {
            showResult('loginResult', 'ℹ️ Klik "Login" untuk memulai test', 'info');
            showResult('cookiesResult', 'ℹ️ Login terlebih dahulu sebelum test cookies', 'info');
            showResult('injectionResult', 'ℹ️ Login terlebih dahulu sebelum test injeksi', 'info');
        });
    </script>
</body>
</html>