@extends('layout.authLayout')

@section('title', 'Verifikasi Kode OTP')

@section('content')
    <div class="min-h-screen bg-gray-100 text-gray-900 flex justify-center">
        <div class="max-w-screen-xl m-0 sm:m-10 bg-white shadow sm:rounded-lg flex justify-center flex-1">
            <div class="lg:w-1/2 xl:w-5/12 p-6 sm:p-12">
                <a href="{{ route('home') }}" class="flex flex-col items-center">
                    <img src="{{ asset('assets/img/logo_satu_pintu.png') }}" class="w-20 h-auto" />
                    <h1 class="text-2xl font-bold text-gray-800 mt-2">Satu Pintu</h1>
                </a>
                <div class="mt-12 flex flex-col items-center">
                    <div class="w-full flex-1 mt-8">
                        <div class="text-center mb-8">
                            <h2 class="text-xl font-semibold text-gray-800 mb-2"><PERSON><PERSON>fikasi Kode OTP</h2>
                            <p class="text-sm text-gray-600">
                                <PERSON><PERSON> telah mengirimkan kode OTP ke email Anda. Masukkan kode tersebut di bawah ini.
                            </p>
                        </div>

                        <div class="mx-auto max-w-xs">
                            @if (session('error'))
                                <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                                    {{ session('error') }}
                                </div>
                            @endif

                            <form method="POST" action="{{ route('password.verify-otp') }}">
                                @csrf
                                <input type="hidden" name="email" value="{{ $email ?? old('email') }}">
                                
                                <div class="flex justify-center space-x-2 mb-6">
                                    @for ($i = 1; $i <= 6; $i++)
                                        <input
                                            class="w-12 h-12 text-center text-lg font-semibold bg-gray-100 border border-gray-200 rounded-lg focus:outline-none focus:border-gray-400 focus:bg-white"
                                            type="text" name="otp_{{ $i }}" maxlength="1" required 
                                            oninput="moveToNext(this, {{ $i }})" 
                                            onkeydown="moveToPrev(this, event, {{ $i }})" />
                                    @endfor
                                </div>
                                
                                @error('otp')
                                    <p class="text-red-500 text-xs text-center mb-4">{{ $message }}</p>
                                @enderror
                                
                                <button type="submit"
                                    class="mt-5 tracking-wide font-semibold bg-green-400 text-white w-full py-4 rounded-lg hover:bg-green-700 transition-all duration-300 ease-in-out flex items-center justify-center focus:shadow-outline focus:outline-none">
                                    <span class="ml-">
                                        Verifikasi Kode
                                    </span>
                                </button>
                            </form>
                            
                            <div class="mt-6 text-center space-y-2">
                                <p class="text-xs text-gray-600">
                                    Tidak menerima kode?
                                    <a href="{{ route('password.resend-otp') }}" 
                                       class="text-blue-600 hover:text-blue-800">
                                        Kirim ulang
                                    </a>
                                </p>
                                <a href="{{ route('login') }}"
                                    class="text-xs text-blue-600 hover:text-blue-800 block">
                                    Kembali ke halaman login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex-1 bg-green-100 text-center hidden lg:flex">
                <div class="m-12 xl:m-16 w-full bg-contain bg-center bg-no-repeat"
                    style="background-image: url('https://cdni.iconscout.com/illustration/premium/thumb/otp-verification-illustration-download-in-svg-png-gif-file-formats--one-time-password-security-code-digital-marketing-pack-business-illustrations-8333961.png');">
                </div>
            </div>
        </div>
    </div>

    <script>
        function moveToNext(current, position) {
            if (current.value.length === 1 && position < 6) {
                const nextInput = document.querySelector(`input[name="otp_${position + 1}"]`);
                if (nextInput) {
                    nextInput.focus();
                }
            }
        }

        function moveToPrev(current, event, position) {
            if (event.key === 'Backspace' && current.value === '' && position > 1) {
                const prevInput = document.querySelector(`input[name="otp_${position - 1}"]`);
                if (prevInput) {
                    prevInput.focus();
                }
            }
        }

        // Auto-focus first input
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.querySelector('input[name="otp_1"]');
            if (firstInput) {
                firstInput.focus();
            }
        });
    </script>
@endsection