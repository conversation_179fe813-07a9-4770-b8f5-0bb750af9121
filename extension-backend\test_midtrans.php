<?php

/**
 * Script untuk testing koneksi Midtrans API
 * Jalankan dengan: php test_midtrans.php
 */

require_once 'vendor/autoload.php';

// Load environment variables
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

echo "=== MIDTRANS API CONNECTION TEST ===\n\n";

// Ambil konfigurasi dari .env
$serverKey = $_ENV['MIDTRANS_SERVER_KEY'] ?? '';
$clientKey = $_ENV['MIDTRANS_CLIENT_KEY'] ?? '';
$isProduction = ($_ENV['MIDTRANS_IS_PRODUCTION'] ?? 'false') === 'true';

echo "Server Key (first 15 chars): " . substr($serverKey, 0, 15) . "\n";
echo "Client Key (first 15 chars): " . substr($clientKey, 0, 15) . "\n";
echo "Is Production: " . ($isProduction ? 'true' : 'false') . "\n";
echo "Expected URL: " . ($isProduction ? 'https://app.midtrans.com' : 'https://app.sandbox.midtrans.com') . "\n\n";

// Validasi server key
if (empty($serverKey)) {
    echo "❌ ERROR: MIDTRANS_SERVER_KEY tidak ditemukan di file .env\n";
    exit(1);
}

// Validasi format server key berdasarkan environment
if ($isProduction) {
    if (!str_starts_with($serverKey, 'Mid-server-')) {
        echo "❌ ERROR: Server key tidak valid untuk production environment\n";
        echo "   Server key production harus dimulai dengan 'Mid-server-'\n";
        echo "   Server key saat ini: " . substr($serverKey, 0, 15) . "...\n";
        exit(1);
    }
    
    if (str_contains($serverKey, 'YOUR_PRODUCTION_SERVER_KEY_HERE')) {
        echo "❌ ERROR: Server key production belum diset\n";
        echo "   Silakan ganti 'YOUR_PRODUCTION_SERVER_KEY_HERE' dengan server key production yang valid\n";
        echo "   Dapatkan dari: https://dashboard.midtrans.com/ > Settings > Access Keys\n";
        exit(1);
    }
} else {
    if (!str_starts_with($serverKey, 'SB-Mid-server-')) {
        echo "❌ ERROR: Server key tidak valid untuk sandbox environment\n";
        echo "   Server key sandbox harus dimulai dengan 'SB-Mid-server-'\n";
        echo "   Server key saat ini: " . substr($serverKey, 0, 15) . "...\n";
        exit(1);
    }
}

echo "✅ Server key format valid\n\n";

// Konfigurasi Midtrans
\Midtrans\Config::$serverKey = $serverKey;
\Midtrans\Config::$isProduction = $isProduction;
\Midtrans\Config::$isSanitized = true;
\Midtrans\Config::$is3ds = true;

echo "=== TESTING SNAP TOKEN CREATION ===\n\n";

// Parameter test
$testParams = [
    'transaction_details' => [
        'order_id' => 'test-order-' . time(),
        'gross_amount' => 10000,
    ],
    'customer_details' => [
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'phone' => '+6281234567890'
    ],
    'item_details' => [[
        'id' => 'test-item',
        'price' => 10000,
        'quantity' => 1,
        'name' => 'Test Item'
    ]]
];

echo "Test Parameters:\n";
echo json_encode($testParams, JSON_PRETTY_PRINT) . "\n\n";

try {
    echo "Mencoba membuat Snap token...\n";
    $snapToken = \Midtrans\Snap::getSnapToken($testParams);
    
    echo "✅ SUCCESS: Snap token berhasil dibuat!\n";
    echo "Token (first 20 chars): " . substr($snapToken, 0, 20) . "...\n";
    echo "Token length: " . strlen($snapToken) . " characters\n\n";
    
    echo "=== TESTING SNAP URL ===\n";
    $snapUrl = \Midtrans\Snap::createTransaction($testParams)->redirect_url;
    echo "✅ Snap URL: " . $snapUrl . "\n\n";
    
    echo "🎉 SEMUA TEST BERHASIL!\n";
    echo "Midtrans API berfungsi dengan baik.\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
    
    if (strpos($e->getMessage(), '401') !== false) {
        echo "=== TROUBLESHOOTING ERROR 401 ===\n";
        echo "1. Pastikan MIDTRANS_SERVER_KEY benar\n";
        echo "2. Pastikan menggunakan server key yang sesuai environment\n";
        echo "3. Untuk sandbox: server key harus dimulai dengan 'SB-Mid-server-'\n";
        echo "4. Untuk production: server key harus dimulai dengan 'Mid-server-'\n";
        echo "5. Pastikan MIDTRANS_IS_PRODUCTION sesuai dengan server key\n\n";
        
        // Test manual authorization
        echo "=== MANUAL AUTHORIZATION TEST ===\n";
        $authString = base64_encode($serverKey . ':');
        echo "Authorization Header: Basic " . substr($authString, 0, 20) . "...\n";
        
        $url = $isProduction ? 'https://app.midtrans.com/snap/v1/transactions' : 'https://app.sandbox.midtrans.com/snap/v1/transactions';
        echo "Target URL: " . $url . "\n";
    }
    
    exit(1);
}

echo "\n=== TEST SELESAI ===\n";