<?php

namespace Tests\Feature;

use App\Models\Anyone;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionLogicTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_with_active_subscription_should_not_be_expired()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => Carbon::now()->addDays(5),
            'role' => 'premium'
        ]);

        $this->assertFalse($user->isSubscriptionExpired());
        $this->assertTrue($user->isSubscriptionActive());
        $this->assertEquals(5, $user->getDaysUntilExpiry());
    }

    /** @test */
    public function user_with_subscription_expiring_today_should_still_be_active()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => Carbon::now()->startOfDay(),
            'role' => 'premium'
        ]);

        $this->assertFalse($user->isSubscriptionExpired());
        $this->assertTrue($user->isSubscriptionActive());
        $this->assertEquals(0, $user->getDaysUntilExpiry());
    }

    /** @test */
    public function user_with_expired_subscription_should_be_expired()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => Carbon::now()->subDays(1),
            'role' => 'premium'
        ]);

        $this->assertTrue($user->isSubscriptionExpired());
        $this->assertFalse($user->isSubscriptionActive());
        $this->assertEquals(0, $user->getDaysUntilExpiry());
    }

    /** @test */
    public function user_with_expired_status_should_be_expired()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'expired',
            'subscription_expires_at' => Carbon::now()->addDays(5), // Even if date is in future
            'role' => 'none'
        ]);

        $this->assertTrue($user->isSubscriptionExpired());
        $this->assertFalse($user->isSubscriptionActive());
        $this->assertEquals(0, $user->getDaysUntilExpiry());
    }

    /** @test */
    public function user_with_unlimited_subscription_should_be_active()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => null, // Unlimited
            'role' => 'premium'
        ]);

        $this->assertFalse($user->isSubscriptionExpired());
        $this->assertTrue($user->isSubscriptionActive());
        $this->assertNull($user->getDaysUntilExpiry());
    }

    /** @test */
    public function dashboard_should_not_force_update_active_subscription()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => Carbon::now()->addDays(2),
            'role' => 'premium'
        ]);

        $this->actingAs($user, 'anyone');
        
        $response = $this->get('/dashboard');
        
        $response->assertStatus(200);
        
        // Refresh user from database
        $user->refresh();
        
        // Should still be active
        $this->assertEquals('active', $user->subscription_status);
        $this->assertEquals('premium', $user->role);
        $this->assertTrue($user->is_active);
    }

    /** @test */
    public function dashboard_should_update_expired_subscription()
    {
        $user = Anyone::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => Carbon::now()->subDays(1),
            'role' => 'premium'
        ]);

        $this->actingAs($user, 'anyone');
        
        $response = $this->get('/dashboard');
        
        $response->assertStatus(200);
        
        // Refresh user from database
        $user->refresh();
        
        // Should be updated to expired
        $this->assertEquals('expired', $user->subscription_status);
        $this->assertEquals('none', $user->role);
        $this->assertFalse($user->is_active);
    }
}
