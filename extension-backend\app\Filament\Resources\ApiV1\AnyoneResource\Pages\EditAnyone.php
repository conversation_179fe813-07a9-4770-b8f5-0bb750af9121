<?php

namespace App\Filament\Resources\ApiV1\AnyoneResource\Pages;

use App\Filament\Resources\ApiV1\AnyoneResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAnyone extends EditRecord
{
    protected static string $resource = AnyoneResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
