<?php

namespace App\Console\Commands;

use App\Models\Anyone;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CheckExpiredSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:check-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update expired subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for expired subscriptions...');

        // Find users with expired subscriptions that haven't been marked as expired yet
        $expiredUsers = Anyone::where('subscription_expires_at', '<=', Carbon::now())
            ->where('subscription_status', '!=', 'expired')
            ->whereNotNull('subscription_expires_at')
            ->get();

        $count = 0;
        foreach ($expiredUsers as $user) {
            // Double check if really expired
            if ($user->isSubscriptionExpired()) {
                $user->update([
                    'subscription_status' => 'expired',
                    'subscription_plan' => null,
                    'role' => 'none',
                    'is_active' => false,
                ]);
                
                $this->line("Updated user: {$user->name} ({$user->email})");
                $count++;
            }
        }

        $this->info("Updated {$count} expired subscriptions.");
        
        return Command::SUCCESS;
    }
}
