<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\ImportantInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:anyone');
    }

    public function index()
    {
        /** @var \App\Models\Anyone $user */
        $user = Auth::guard('anyone')->user();
        $importantInfos = ImportantInfo::getDisplayableInfos();

        $user->where('id', $user->id)->update([
            'subscription_status' => 'expired',
            'payment_status' => 'pending',
            'subscription_status' => 'expired',
            'subscription_plan' => null,
            'role' => 'none',
        ]);

        // Calculate days until expiry
        $daysLeft = $user->getDaysUntilExpiry();
        $showWarning = $daysLeft <= 6 && $daysLeft > 0 && !$user->isSubscriptionActive();

        return view('dashboard.index', compact('user', 'importantInfos', 'daysLeft', 'showWarning'));
    }
}
