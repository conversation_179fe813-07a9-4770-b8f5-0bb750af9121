<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\ImportantInfo;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:anyone');
    }

    public function index()
    {
        /** @var \App\Models\Anyone $user */
        $user = Auth::guard('anyone')->user();
        $importantInfos = ImportantInfo::getDisplayableInfos();

        // Check if subscription is expired and update status if needed
        if ($user->isSubscriptionExpired()) {
            // Only update if not already expired to avoid unnecessary database calls
            if ($user->subscription_status !== 'expired') {
                $user->update([
                    'subscription_status' => 'expired',
                    'subscription_plan' => null,
                    'role' => 'none',
                    'is_active' => false,
                ]);

                // Refresh user model to get updated data
                $user->refresh();
            }
        }

        // Calculate days until expiry
        $daysLeft = $user->getDaysUntilExpiry();
        $showWarning = $daysLeft <= 6 && $daysLeft > 0 && !$user->isSubscriptionActive();

        return view('dashboard.index', compact('user', 'importantInfos', 'daysLeft', 'showWarning'));
    }
}
