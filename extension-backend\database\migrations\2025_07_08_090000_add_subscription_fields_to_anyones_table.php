<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('anyones', function (Blueprint $table) {
            // Tambahkan field subscription yang diperlukan
            $table->string('phone')->nullable()->after('email');
            $table->string('subscription_plan')->nullable()->after('role'); // elite, premium
            $table->string('subscription_status')->default('pending')->after('subscription_plan'); // pending, active, expired, cancelled
            $table->timestamp('subscription_activated_at')->nullable()->after('subscription_expires_at');
            $table->string('pending_order_id')->nullable()->after('subscription_activated_at');
            $table->json('pending_payment_data')->nullable()->after('pending_order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('anyones', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'subscription_plan',
                'subscription_status', 
                'subscription_activated_at',
                'pending_order_id',
                'pending_payment_data'
            ]);
        });
    }
};