# Setup FAQ Dinamis

## Langkah-langkah Setup

### 1. Jalankan Migration
```bash
php artisan migrate
```

### 2. Jalankan Seeder FAQ
```bash
php artisan db:seed --class=FaqSeeder
```

### 3. Atau jalankan semua seeder
```bash
php artisan db:seed
```

## Akses Panel Admin

1. Buka browser dan akses: `http://localhost:8000/admin`
2. Login dengan kredensial admin
3. Klik menu "FAQ" di sidebar
4. Kelola FAQ (tambah, edit, hapus, atur urutan)

## Fitur FAQ

### Model FAQ
- **question**: Pertanyaan FAQ
- **answer**: Jawaban FAQ
- **order**: Urutan tampil (angka kecil = tampil lebih dulu)
- **is_active**: Status aktif/nonaktif

### Filament Resource
- CRUD lengkap untuk FAQ
- Filter berdasarkan status aktif
- Sorting berdasarkan urutan
- Pencarian berdasarkan pertanyaan

### Frontend
- FAQ ditampilkan secara dinamis dari database
- FAQ pertama otomatis terbuka (active)
- Fallback jika tidak ada FAQ atau tabel belum ada
- Responsive design

## Struktur File

```
app/
├── Models/Faq.php
├── Http/Controllers/HomeController.php (updated)
└── Filament/Resources/
    ├── FaqResource.php
    └── FaqResource/Pages/
        ├── ListFaqs.php
        ├── CreateFaq.php
        └── EditFaq.php

database/
├── migrations/2025_01_11_000000_create_faqs_table.php
└── seeders/
    ├── FaqSeeder.php
    └── DatabaseSeeder.php (updated)

resources/views/home/<USER>
```
