<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sites', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('url');
            $table->string('domain');
            $table->string('category')->default('general');
            $table->enum('visibility', ['elite', 'premium', 'both'])->default('both');
            $table->string('thumbnail')->nullable();
            $table->text('description')->nullable();
            $table->string('cookie_file_path')->nullable();
            $table->timestamp('cookie_file_uploaded_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sites');
    }
};
