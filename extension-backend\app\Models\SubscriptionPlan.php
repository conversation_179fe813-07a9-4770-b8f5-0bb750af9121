<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'price',
        'duration_days',
        'description',
        'features',
        'whatsapp_number',
        'whatsapp_message_template',
        'is_active',
    ];

    protected $casts = [
        'features' => 'array',
        'is_active' => 'boolean',
    ];

    public function getFormattedPriceAttribute()
    {
        return 'Rp ' . number_format($this->price, 0, ',', '.');
    }

    public function getDurationTextAttribute()
    {
        if ($this->duration_days == 14) {
            return '2 Pekan';
        } elseif ($this->duration_days == 30) {
            return '1 Bulan';
        } else {
            return $this->duration_days . ' Hari';
        }
    }
}