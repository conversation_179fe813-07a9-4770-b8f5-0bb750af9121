<?php

namespace Database\Seeders;

use App\Models\Anyone;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AnyoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // User dengan subscription aktif (30 hari ke depan)
        Anyone::create([
            'name' => 'Elite User Active',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'elite',
            'is_active' => true,
            'subscription_expires_at' => now()->addDays(30),
        ]);

        // User dengan subscription hampir expired (3 hari lagi)
        Anyone::create([
            'name' => 'Premium User Soon Expire',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'premium',
            'is_active' => true,
            'subscription_expires_at' => now()->addDays(3),
        ]);

        // User dengan subscription sudah expired
        Anyone::create([
            'name' => 'Elite User Expired',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'elite',
            'is_active' => true,
            'subscription_expires_at' => now()->subDays(5),
        ]);

        // User dengan subscription unlimited (null)
        Anyone::create([
            'name' => 'Premium User Unlimited',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'premium',
            'is_active' => true,
            'subscription_expires_at' => null,
        ]);

        // User dengan subscription expired hari ini
        Anyone::create([
            'name' => 'Elite User Today Expire',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'elite',
            'is_active' => true,
            'subscription_expires_at' => now()->subHours(1),
        ]);
    }
}
