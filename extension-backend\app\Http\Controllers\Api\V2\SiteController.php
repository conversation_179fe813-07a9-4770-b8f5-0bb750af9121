<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Models\SiteJsonFile;
use App\Models\ImportantInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class SiteController extends Controller
{
    /**
     * Display a listing of the sites.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $role = $user->role ?? 'free';

        $sites = Site::with(['categoryModel', 'activeJsonFiles'])
            ->active()
            ->visibleToRole($role)
            ->get()
            ->map(function ($site) {
                return [
                    'id' => $site->id,
                    'name' => $site->name,
                    'url' => $site->url,
                    'domain' => $site->domain,
                    'category' => $site->category,
                    'category_model' => $site->categoryModel ? [
                        'id' => $site->categoryModel->id,
                        'name' => $site->categoryModel->name,
                        'slug' => $site->categoryModel->slug,
                        'icon' => $site->categoryModel->icon,
                        'color' => $site->categoryModel->color,
                    ] : null,
                    'visibility' => $site->visibility,
                    'thumbnail' => $site->thumbnail,
                    'logo_path' => $site->logo_path,
                    'description' => $site->description,
                    'has_json_files' => $site->activeJsonFiles->count() > 0,
                    'json_files_count' => $site->activeJsonFiles->count(),
                    // Custom redirect fields
                    'enable_custom_redirect' => $site->enable_custom_redirect,
                    'redirect_url' => $site->redirect_url,
                    'redirect_title' => $site->redirect_title,
                    'redirect_content' => $site->redirect_content,
                    'redirect_delay' => $site->redirect_delay,
                    // Account injection fields
                    'login_url' => $site->login_url,
                    'email_selector' => $site->email_selector,
                    'password_selector' => $site->password_selector,
                    'submit_selector' => $site->submit_selector,
                    'additional_script' => $site->additional_script,
                    'js_after_submit' => $site->js_after_submit,
                    // Backward compatibility
                    'has_cookie_file' => $site->activeJsonFiles->count() > 0,
                    'cookie_count' => $site->activeJsonFiles->count(),
                ];
            });

        // Get important information to display at the bottom of the extension
        $importantInfo = ImportantInfo::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->first();

        return response()->json([
            'success' => true,
            'sites' => $sites,
            'important_info' => $importantInfo ? [
                'id' => $importantInfo->id,
                'title' => $importantInfo->title,
                'content' => $importantInfo->content,
                'type' => $importantInfo->type,
                'created_at' => $importantInfo->created_at->format('Y-m-d H:i:s'),
            ] : null,
        ]);
    }

    /**
     * Display the specified site.
     */
    public function show($id)
    {
        $user = Auth::user();
        $role = $user->role ?? 'free';

        $site = Site::with(['categoryModel', 'activeJsonFiles'])
            ->active()
            ->visibleToRole($role)
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'site' => [
                'id' => $site->id,
                'name' => $site->name,
                'url' => $site->url,
                'domain' => $site->domain,
                'category' => $site->category,
                'category_model' => $site->categoryModel ? [
                    'id' => $site->categoryModel->id,
                    'name' => $site->categoryModel->name,
                    'slug' => $site->categoryModel->slug,
                    'icon' => $site->categoryModel->icon,
                    'color' => $site->categoryModel->color,
                ] : null,
                'visibility' => $site->visibility,
                'thumbnail' => $site->thumbnail,
                'logo_path' => $site->logo_path,
                'description' => $site->description,
                'has_json_files' => $site->activeJsonFiles->count() > 0,
                'json_files_count' => $site->activeJsonFiles->count(),
                'json_files' => $site->activeJsonFiles->map(function ($jsonFile) {
                    return [
                        'id' => $jsonFile->id,
                        'name' => $jsonFile->name,
                        'description' => $jsonFile->description,
                        'is_active' => $jsonFile->is_active,
                        'created_at' => $jsonFile->created_at,
                    ];
                }),
                // Backward compatibility
                'has_cookie_file' => $site->activeJsonFiles->count() > 0,
                'cookie_count' => $site->activeJsonFiles->count(),
            ],
        ]);
    }

    /**
     * Get JSON files for a specific site.
     */
    public function getSiteCookies($id)
    {
        $user = Auth::user();
        $role = $user->role ?? 'free';

        $site = Site::with('activeJsonFiles')
            ->active()
            ->visibleToRole($role)
            ->findOrFail($id);

        // Format JSON files for display in the site detail page
        $formattedJsonFiles = [];
        foreach ($site->activeJsonFiles as $index => $jsonFile) {
            $formattedJsonFiles[] = [
                'id' => $jsonFile->id,
                'index' => $index + 1,
                'name' => $jsonFile->name,
                'description' => $jsonFile->description,
                'thumbnail' => $site->thumbnail,
                'logo_path' => $site->logo_path,
                'created_at' => $jsonFile->created_at,
            ];
        }

        return response()->json([
            'success' => true,
            'site' => [
                'id' => $site->id,
                'name' => $site->name,
                'url' => $site->url,
                'domain' => $site->domain,
                'description' => $site->description,
                'thumbnail' => $site->thumbnail,
                'logo_path' => $site->logo_path,
                'category' => $site->getCategoryName(),
                'category_color' => $site->getCategoryColor(),
                'category_icon' => $site->getCategoryIcon(),
            ],
            'json_files' => $formattedJsonFiles,
            'has_multiple_json_files' => $site->activeJsonFiles->count() > 1,
            // Backward compatibility
            'cookies' => $formattedJsonFiles,
            'has_multiple_cookies' => $site->activeJsonFiles->count() > 1,
        ]);
    }

    /**
     * Get JSON files for a specific domain.
     */
    public function getCookies(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'domain' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Domain is required',
                'errors' => $validator->errors(),
            ], 422);
        }

        $domain = $request->input('domain');
        $user = Auth::user();
        $role = $user->role ?? 'free';

        // Find site by domain
        $site = Site::with('activeJsonFiles')
            ->active()
            ->visibleToRole($role)
            ->where('domain', 'like', '%' . $domain . '%')
            ->first();

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'No JSON files found for this domain',
            ], 404);
        }

        // If there are multiple JSON files, return information about the site
        // so the frontend can redirect to the site detail page
        if ($site->activeJsonFiles->count() > 1) {
            return response()->json([
                'success' => true,
                'has_multiple_json_files' => true,
                'site_id' => $site->id,
                'site_name' => $site->name,
                'json_files_count' => $site->activeJsonFiles->count(),
                'message' => 'This site has multiple JSON files. Please select one from the detail page.',
                // Backward compatibility
                'has_multiple_cookies' => true,
            ]);
        }

        // If there's only one JSON file, get cookies from it
        $jsonFile = $site->activeJsonFiles->first();
        if (!$jsonFile) {
            return response()->json([
                'success' => false,
                'message' => 'No active JSON files found for this site',
            ], 404);
        }

        try {
            $cookies = $jsonFile->getCookiesFromFile();
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error reading JSON file: ' . $e->getMessage(),
            ], 500);
        }

        return response()->json([
            'success' => true,
            'has_multiple_json_files' => false,
            'site' => [
                'id' => $site->id,
                'name' => $site->name,
                'url' => $site->url,
                'domain' => $site->domain,
                'logo_path' => $site->logo_path,
            ],
            'json_file' => [
                'id' => $jsonFile->id,
                'name' => $jsonFile->name,
                'description' => $jsonFile->description,
            ],
            'cookies' => $cookies,
            // Backward compatibility
            'has_multiple_cookies' => false,
        ]);
    }

    /**
     * Store a newly created site.
     */
    public function store(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:255',
            'domain' => 'required|string|max:255',
            'category' => 'required|string|exists:categories,slug',
            'visibility' => 'required|in:elite,premium,both',
            'description' => 'nullable|string',
            'json_files' => 'nullable|array',
            'json_files.*' => 'file|mimes:json|max:5120',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        DB::beginTransaction();

        try {
            // Create new site
            $site = new Site();
            $site->name = $request->input('name');
            $site->url = $request->input('url');
            $site->domain = $request->input('domain');
            $site->category = $request->input('category');
            $site->visibility = $request->input('visibility');
            $site->description = $request->input('description');
            $site->is_active = true;

            // Handle thumbnail upload if provided
            if ($request->hasFile('thumbnail')) {
                $thumbnailPath = $request->file('thumbnail')->store('thumbnails', 'public');
                $site->thumbnail = Storage::url($thumbnailPath);
            }

            // Handle logo upload if provided
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('site-logos', 'public');
                $site->logo_path = Storage::url($logoPath);
            }

            $site->save();

            // Handle multiple JSON file uploads
            if ($request->hasFile('json_files')) {
                $jsonFiles = $request->file('json_files');

                foreach ($jsonFiles as $index => $jsonFile) {
                    // Get original filename without extension for name
                    $originalName = pathinfo($jsonFile->getClientOriginalName(), PATHINFO_FILENAME);

                    // Generate unique filename for storage
                    $fileName = 'json_files/' . Str::uuid() . '.json';

                    // Store file
                    Storage::put($fileName, file_get_contents($jsonFile));

                    // Create SiteJsonFile record
                    SiteJsonFile::create([
                        'site_id' => $site->id,
                        'name' => $originalName ?: 'JSON File ' . ($index + 1),
                        'file_path' => $fileName,
                        'description' => 'Uploaded JSON file: ' . $jsonFile->getClientOriginalName(),
                        'is_active' => true,
                    ]);
                }
            }

            DB::commit();

            // Load the site with its JSON files for response
            $site->load('jsonFiles');

            return response()->json([
                'success' => true,
                'message' => 'Site created successfully with ' . $site->jsonFiles->count() . ' JSON files',
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'url' => $site->url,
                    'domain' => $site->domain,
                    'category' => $site->category,
                    'visibility' => $site->visibility,
                    'description' => $site->description,
                    'thumbnail' => $site->thumbnail,
                    'logo_path' => $site->logo_path,
                    'json_files_count' => $site->jsonFiles->count(),
                    'json_files' => $site->jsonFiles->map(function ($jsonFile) {
                        return [
                            'id' => $jsonFile->id,
                            'name' => $jsonFile->name,
                            'description' => $jsonFile->description,
                            'is_active' => $jsonFile->is_active,
                            'created_at' => $jsonFile->created_at,
                        ];
                    }),
                ],
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create site: ' . $e->getMessage(),
            ], 500);
        }
    }
}
