<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Anyone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        // Jika user sudah login, redirect ke dashboard
        if (Auth::guard('anyone')->check()) {
            return redirect()->route('user.dashboard');
        }

        return view('auth.login');
    }

    public function register(Request $request)
    {
        // Jika user sudah login, redirect ke dashboard
        if (Auth::guard('anyone')->check()) {
            return redirect()->route('user.dashboard');
        }

        return view('auth.register');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:anyones'],
            'phone' => ['required', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ], [
            'name.required' => 'Nama harus diisi.',
            'email.required' => 'Email harus diisi.',
            'email.email' => 'Email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'phone.required' => 'No. telepon harus diisi.',
            'phone.string' => 'No. telepon harus berupa angka.',
            'password.confirmed' => 'Kata sandi tidak cocok.',
            'password.min' => 'Kata sandi minimal 8 karakter.',
            'password.regex' => 'Kata sandi harus mengandung huruf besar, huruf kecil, angka, dan karakter khusus (!@#$%^&*).',
        ]);

        $user = Anyone::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
        ]);

        Auth::guard('anyone')->login($user);

        // Store session info in cache for extension access
        $sessionId = $request->session()->getId();
        Cache::put('extension_session_' . $sessionId, [
            'user_id' => $user->id,
            'email' => $user->email,
            'created_at' => now()
        ], 60 * 24 * 7); // Cache for 7 days

        return redirect()->route('user.dashboard')->with('success', 'Akun berhasil dibuat!');
    }

    public function authenticate(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        $remember = $request->boolean('remember');
        
        if (Auth::guard('anyone')->attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // Set remember me expiration if checked
            if ($remember) {
                $request->session()->put('remember_me', true);
                $request->session()->put('last_activity', now());
            }

            // Store session info in cache for extension access
            $user = Auth::guard('anyone')->user();
            $sessionId = $request->session()->getId();
            Cache::put('extension_session_' . $sessionId, [
                'user_id' => $user->id,
                'email' => $user->email,
                'created_at' => now()
            ], 60 * 24 * 7); // Cache for 7 days

            return redirect()->intended(route('user.dashboard'));
        }

        throw ValidationException::withMessages([
            'email' => 'Email atau password tidak valid.',
        ]);
    }

    public function logout(Request $request)
    {
        // Remove session info from cache before logout
        $sessionId = $request->session()->getId();
        Cache::forget('extension_session_' . $sessionId);

        // Clear all authentication data
        Auth::guard('anyone')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        // Return response with headers to clear client-side storage
        return redirect()
            ->route('login')
            ->withHeaders([
                'Clear-Site-Data' => '"cache", "cookies", "storage", "executionContexts"'
            ])
            ->with('script', '
                // Clear localStorage and sessionStorage
                localStorage.clear();
                sessionStorage.clear();

                // Clear all cookies
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "")
                        .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });
            ');
    }

    public function showForgotPasswordForm()
    {
        return view('auth.forgot-password');
    }

    public function sendResetLinkEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $user = Anyone::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'Email tidak ditemukan.']);
        }

        // Generate OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store OTP in cache for 10 minutes
        Cache::put('password_reset_otp_' . $user->email, $otp, 600);

        // TODO: Send OTP via email
        // Mail::to($user->email)->send(new PasswordResetOTP($otp));

        return redirect()->route('password.verify-otp-form', ['email' => $user->email])
            ->with('status', 'Kode OTP telah dikirim ke email Anda.');
    }

    public function showVerifyOtpForm(Request $request)
    {
        return view('auth.verify-otp', ['email' => $request->email]);
    }

    public function verifyOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'otp_1' => 'required|numeric',
            'otp_2' => 'required|numeric',
            'otp_3' => 'required|numeric',
            'otp_4' => 'required|numeric',
            'otp_5' => 'required|numeric',
            'otp_6' => 'required|numeric',
        ]);

        $otp = $request->otp_1 . $request->otp_2 . $request->otp_3 .
               $request->otp_4 . $request->otp_5 . $request->otp_6;

        $cachedOtp = Cache::get('password_reset_otp_' . $request->email);

        if (!$cachedOtp || $cachedOtp !== $otp) {
            return back()->withErrors(['otp' => 'Kode OTP tidak valid atau sudah kedaluwarsa.']);
        }

        // Generate reset token
        $token = Str::random(64);
        Cache::put('password_reset_token_' . $request->email, $token, 600);
        Cache::forget('password_reset_otp_' . $request->email);

        return redirect()->route('password.reset-form', [
            'token' => $token,
            'email' => $request->email
        ]);
    }

    public function resendOtp(Request $request)
    {
        return $this->sendResetLinkEmail($request);
    }

    public function showResetPasswordForm(Request $request)
    {
        return view('auth.reset-password', [
            'token' => $request->token,
            'email' => $request->email
        ]);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $cachedToken = Cache::get('password_reset_token_' . $request->email);

        if (!$cachedToken || $cachedToken !== $request->token) {
            return back()->withErrors(['email' => 'Token reset password tidak valid atau sudah kedaluwarsa.']);
        }

        $user = Anyone::where('email', $request->email)->first();

        if (!$user) {
             return back()->withErrors(['email' => 'User tidak ditemukan.']);
         }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        Cache::forget('password_reset_token_' . $request->email);

        return redirect()->route('login')->with('status', 'Password berhasil direset. Silakan login dengan password baru.');
    }
}
