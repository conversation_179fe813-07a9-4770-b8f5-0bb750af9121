<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON>ie Manager Extension</title>
    <style>
      html,
      body {
        width: 420px;
        height: 600px;
        min-width: 420px;
        min-height: 600px;
        max-width: 420px;
        max-height: 600px;
        /* width: 100%;
        height: 100%;
        max-height: 100%; */
        padding: 0;
        margin: 0;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(
          135deg,
          #1a1a2e 0%,
          #16213e 50%,
          #0f3460 100%
        );
        color: white;
        overflow: hidden;
        box-sizing: border-box;
        position: relative;
      }

      /* Base Container Styles */
      .container {
        background: rgba(0, 0, 0, 0.4);
        border-radius: 20px;
        padding: 30px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        height: calc(100% - 32px);
        width: calc(100% - 32px);
        margin: 16px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        overflow: hidden;
      }

      /* Main App Container with Fixed Header */
      .main-app-container {
        background: rgba(0, 0, 0, 0.4);
        border-radius: 20px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        height: calc(100% - 32px);
        margin: 16px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        overflow: hidden;
        position: relative;
        max-height: calc(100% - 32px);
      }

      /* Login Header Styles */
      .login-header {
        text-align: center;
        margin-bottom: 30px;
        padding-top: 10px;
      }

      .app-logo {
        margin-bottom: 12px;
        display: flex;
        justify-content: center;
      }

      .login-header h1 {
        margin: 8px 0;
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(45deg, #fff, #e0e7ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .login-header p {
        margin: 10px 0;
        font-size: 15px;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        padding: 0 20px;
        line-height: 1.4;
      }
      h1 {
        text-align: center;
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
      }
      .upload-area {
        border: 2px dashed rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
      }
      .upload-area:hover {
        border-color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.1);
      }
      .upload-area.dragover {
        border-color: #4caf50;
        background: rgba(76, 175, 80, 0.1);
      }
      input[type="file"] {
        display: none;
      }
      .btn {
        width: 100%;
        padding: 12px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 5px 0;
      }
      .btn-primary {
        background: #4caf50;
        color: white;
        font-size: 16px;
        padding: 14px;
        margin-top: 10px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: transform 0.2s ease, background-color 0.3s ease,
          box-shadow 0.3s ease;
        z-index: 1;
      }

      .btn-loading {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        gap: 8px;
      }

      .btn-loading .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 0.8s linear infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .btn-ripple {
        position: absolute;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        transform: translate(-50%, -50%) scale(0);
        pointer-events: none;
        z-index: -1;
      }

      @keyframes ripple {
        0% {
          transform: translate(-50%, -50%) scale(0);
          opacity: 0.5;
        }
        100% {
          transform: translate(-50%, -50%) scale(3);
          opacity: 0;
        }
      }
      .btn-primary:hover {
        background: #45a049;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
      }

      .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(76, 175, 80, 0.4);
        background: #3d8b40;
      }

      .btn-primary:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.5);
      }
      .btn-secondary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
      .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      .status {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        padding: 12px 20px;
        border-radius: 12px;
        text-align: center;
        font-size: 13px;
        font-weight: 500;
        display: none;
        min-width: 200px;
        max-width: 350px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideInDown 0.3s ease-out;
      }

      @keyframes slideInDown {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }
      .status.success {
        background: rgba(76, 175, 80, 0.3);
        border: 1px solid #66bb6a;
        color: #81c784;
      }
      .status.error {
        background: rgba(244, 67, 54, 0.3);
        border: 1px solid #ef5350;
        color: #e57373;
      }
      .status.warning {
        background: rgba(255, 152, 0, 0.3);
        border: 1px solid #ffb74d;
        color: #ffcc02;
      }
      .file-info {
        background: rgba(255, 255, 255, 0.1);
        padding: 10px;
        border-radius: 8px;
        margin: 10px 0;
        display: none;
      }
      .site-buttons {
        margin-top: 15px;
      }
      .site-btn {
        width: 100%;
        padding: 10px;
        margin: 5px 0;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      .site-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      .progress {
        width: 100%;
        height: 6px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        overflow: hidden;
        margin: 10px 0;
        display: none;
      }
      .progress-bar {
        height: 100%;
        background: #4caf50;
        width: 0%;
        transition: width 0.3s ease;
      }

      /* Login Form Styles */
      .login-form {
        margin-top: 20px;
        width: 100%;
      }

      .input-group {
        margin-bottom: 15px;
      }

      .input-group label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: 500;
      }

      .input-group input {
        width: 100%;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        box-sizing: border-box;
      }

      .input-group input::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }

      .input-group input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.15);
      }

      .troubleshooting {
        margin-top: 15px;
        padding: 15px;
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: 8px;
      }

      .troubleshooting h3 {
        margin: 0 0 10px 0;
        color: #ffc107;
        font-size: 14px;
      }

      .troubleshooting-content {
        font-size: 12px;
        line-height: 1.4;
      }

      .troubleshooting ol {
        margin: 8px 0;
        padding-left: 20px;
      }

      .troubleshooting li {
        margin-bottom: 4px;
      }

      .btn-small {
        padding: 6px 12px;
        font-size: 11px;
        margin-top: 8px;
      }

      /* Main App Styles */
      .main-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 16px 12px 16px;
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px 16px 0 0;
        flex-shrink: 0;
      }

      .app-logo-main {
        width: 42px;
        height: 42px;
        border-radius: 10px;
        background: linear-gradient(
          135deg,
          #4f46e5 0%,
          #7c3aed 50%,
          #2563eb 100%
        );
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        flex-shrink: 0;
      }

      .header-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-left: 10px;
        flex: 1;
      }

      .app-title {
        margin: 0;
        font-size: 16px;
        font-weight: 700;
        background: linear-gradient(45deg, #fff, #e0e7ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .app-version {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: 2px;
        font-weight: 500;
      }

      .user-section {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .user-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
        font-size: 12px;
        text-align: right;
      }

      .user-name {
        font-weight: 600;
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
      }

      .subscription-info {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
        max-width: 120px;
        word-wrap: break-word;
        text-align: right;
        line-height: 1.2;
      }

      .subscription-info.warning {
        color: #fbbf24;
      }

      .subscription-info.danger {
        color: #f87171;
      }

      .role-badge {
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 9px;
        font-weight: 600;
        text-align: center;
        text-transform: uppercase;
        margin-top: 2px;
        display: inline-block;
      }

      .role-badge.elite {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
        border: 1px solid rgba(59, 130, 246, 0.4);
      }

      .role-badge.premium {
        background: rgba(34, 197, 94, 0.2);
        color: #4ade80;
        border: 1px solid rgba(34, 197, 94, 0.4);
      }

      .btn-logout {
        padding: 4px 10px;
        font-size: 11px;
        background: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border: 1px solid rgba(239, 68, 68, 0.4);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .btn-logout:hover {
        background: rgba(239, 68, 68, 0.3);
        border-color: rgba(239, 68, 68, 0.6);
      }

      .btn-logout {
        padding: 8px 16px;
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.5);
        color: #f87171;
        border-radius: 8px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        flex-shrink: 0;
      }

      .btn-logout:hover {
        background: rgba(239, 68, 68, 0.3);
        transform: translateY(-1px);
      }

      /* Search Section */
      .search-section {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .search-container {
        position: relative;
        display: flex;
        align-items: center;
      }

      .search-input {
        width: 100%;
        padding: 10px 16px 10px 40px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-radius: 12px;
        font-size: 13px;
        outline: none;
        transition: all 0.3s ease;
      }

      .search-input::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      .search-input:focus {
        border-color: rgba(79, 70, 229, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
      }

      .search-icon {
        position: absolute;
        left: 14px;
        color: rgba(255, 255, 255, 0.6);
        pointer-events: none;
      }

      /* Filter Section */
      .filter-section {
        margin-bottom: 0;
        flex-shrink: 0;
        overflow-x: auto;
        margin: 0 -16px;
        padding: 0 16px;
      }

      .filter-section::-webkit-scrollbar {
        height: 0;
        display: none;
      }

      .filter-tabs {
        display: flex;
        gap: 6px;
        margin-bottom: 0;
        padding-top: 4px;
        padding-bottom: 4px;
        white-space: nowrap;
      }

      .filter-tab {
        padding: 5px 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
      }

      .filter-tab.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .filter-tab:hover:not(.active) {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
      }

      /* Main Content Area */
      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow: hidden; /* Changed from auto to hidden */
        height: 100%;
        position: relative;
      }

      /* Search and filter sections */
      .search-filter-wrapper {
        padding: 16px 24px;
        flex-shrink: 0;
        background: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin: 0 -16px;
        /* padding: 0 16px 8px 16px; */
      }

      /* Sites Section */
      .sites-section {
        flex: 1;
        min-height: 0;
        overflow-y: auto !important; /* Force overflow-y to be auto */
        padding: 16px 16px 20px 16px;
        background: transparent;
        height: calc(
          100% - 120px
        ); /* Adjust height to account for header and search */
        max-height: calc(100% - 120px);
        position: relative;
        z-index: 1; /* Ensure it's above other elements */
      }

      .sites-section::-webkit-scrollbar {
        width: 6px;
      }

      .sites-section::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        margin: 8px 0;
      }

      .sites-section::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
      }

      .sites-section::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }

      /* Sites List */
      .sites-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        padding-top: 8px;
        padding-bottom: 16px;
        min-height: min-content;
        width: 100%;
        overflow: visible;
      }

      .site-card {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        padding: 12px 8px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
        min-height: 90px;
        max-height: 110px;
        text-align: center;
      }

      .site-card:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(79, 70, 229, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
          0 0 0 1px rgba(255, 255, 255, 0.1);
      }

      .site-logo {
        width: 52px;
        height: 52px;
        border-radius: 12px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.15),
          rgba(255, 255, 255, 0.05)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
        flex-shrink: 0;
        margin-bottom: 4px;
      }

      .site-logo img {
        width: 90%;
        height: 90%;
        object-fit: contain;
        border-radius: 8px;
      }

      .site-info {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        min-width: 0;
        margin-top: -4px;
      }

      .site-name {
        font-size: 13px;
        font-weight: 600;
        line-height: 1.3;
        color: rgba(255, 255, 255, 0.95);
        margin: 0;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        max-width: 100%;
        padding: 0 4px;
      }

      .site-description {
        font-size: 12px;
        font-weight: 400;
        line-height: 1.2;
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }

      .site-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 2px;
      }

      .site-status {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: 10px;
        font-weight: 500;
        padding: 2px 6px;
        border-radius: 6px;
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
      }

      .site-status.premium {
        background: rgba(168, 85, 247, 0.2);
        color: #a855f7;
        border-color: rgba(168, 85, 247, 0.3);
      }

      .site-status.elite {
        background: rgba(251, 191, 36, 0.2);
        color: #fbbf24;
        border-color: rgba(251, 191, 36, 0.3);
      }

      .site-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
      }

      .site-action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
      }

      .site-action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: scale(1.05);
      }

      .site-action-btn.primary {
        background: linear-gradient(135deg, #4f46e5, #7c3aed);
        border-color: rgba(79, 70, 229, 0.5);
        color: white;
      }

      .site-action-btn.primary:hover {
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        transform: scale(1.05);
      }

      .site-category-badge {
        padding: 2px 6px;
        border-radius: 6px;
        font-size: 9px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .site-category-badge.elite {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #1a1a1a;
      }

      .site-category-badge.premium {
        background: linear-gradient(135deg, #8b5cf6, #a78bfa);
        color: white;
      }

      .site-category-badge.general {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
      }

      /* No sites message */
      .no-sites {
        text-align: center;
        color: rgba(255, 255, 255, 0.6);
        padding: 40px 20px;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
      }

      .no-sites-icon {
        font-size: 48px;
        opacity: 0.5;
      }

      .no-sites-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
      }

      .no-sites-subtitle {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        margin: 0;
      }

      .site-card.hidden {
        display: none;
      }

      /* Footer Section Styles */
      .footer-section {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40px;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        z-index: 1000;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
      }

      .footer-left,
      .footer-right {
        flex-shrink: 0;
        z-index: 1001;
      }

      .footer-center {
        flex: 1;
        overflow: hidden;
        margin: 0 12px;
        position: relative;
      }

      .footer-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);
        padding: 6px 10px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .footer-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-1px);
      }

      .footer-btn:active {
        transform: translateY(0);
      }

      /* Scrolling Text Animation */
      .scrolling-text-container {
        width: 100%;
        height: 20px;
        overflow: hidden;
        position: relative;
        display: flex;
        align-items: center;
      }

      .scrolling-text {
        white-space: nowrap;
        font-size: 11px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        animation: scrollLeft 30s linear infinite;
        position: absolute;
        left: 100%;
      }

      @keyframes scrollLeft {
        0% {
          left: 100%;
        }
        100% {
          left: -100%;
        }
      }

      /* Adjust main content to account for footer */
      .main-app-container {
        margin-bottom: 40px;
        height: calc(100% - 72px);
      }

      .container {
        margin-bottom: 40px;
        height: calc(100% - 72px);
      }

      /* Login Form Styles */
      .login-form {
        margin: 20px 0;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-size: 15px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }

      .form-group input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 13px;
        box-sizing: border-box;
        transition: all 0.3s ease;
      }

      .form-group input:focus {
        border-color: rgba(79, 70, 229, 0.5);
        outline: none;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
      }

      .form-group input:focus {
        outline: none;
        border-color: #4caf50;
        background: rgba(255, 255, 255, 0.15);
      }

      .form-group input::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }

      .btn-loading {
        display: none;
        align-items: center;
        justify-content: center;
        gap: 8px;
        width: 100%;
      }

      .btn-loading .spinner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        display: inline-block;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Efek Ripple untuk tombol */
      .btn-ripple {
        position: absolute;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
        pointer-events: none;
      }

      /* Animasi ripple akan dijalankan melalui JavaScript */

      @keyframes ripple {
        0% {
          transform: translate(-50%, -50%) scale(0);
          opacity: 1;
        }
        80% {
          transform: translate(-50%, -50%) scale(1.5);
          opacity: 0.5;
        }
        100% {
          transform: translate(-50%, -50%) scale(2);
          opacity: 0;
        }
      }

      /* Full page loader */
      .full-page-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
      }

      .full-page-loader .loader-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #4caf50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      .full-page-loader .loader-text {
        color: white;
        font-size: 14px;
        text-align: center;
        margin-bottom: 8px;
      }

      .full-page-loader .loader-subtext {
        color: rgba(255, 255, 255, 0.7);
        font-size: 12px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <!-- Login Screen -->
    <div class="container" id="loginScreen">
      <div class="status" id="loginStatus"></div>
    </div>

    <!-- Main App Screen -->
    <div class="main-app-container" id="mainScreen" style="display: none">
      <div class="main-content">
        <!-- Header Container (Fixed) -->
        <div class="main-header">
          <div class="app-logo-main">
            <svg
              width="48"
              height="48"
              viewBox="0 0 64 64"
              xmlns="http://www.w3.org/2000/svg"
            >
              <defs>
                <linearGradient
                  id="doorGradientMain"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: #ffffff; stop-opacity: 1"
                  />
                  <stop
                    offset="50%"
                    style="stop-color: #f8fafc; stop-opacity: 1"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: #e2e8f0; stop-opacity: 1"
                  />
                </linearGradient>
                <linearGradient
                  id="numberGradientMain"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: #fbbf24; stop-opacity: 1"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: #f59e0b; stop-opacity: 1"
                  />
                </linearGradient>
              </defs>

              <!-- Door Frame -->
              <rect
                x="18"
                y="12"
                width="28"
                height="40"
                rx="2"
                ry="2"
                fill="url(#doorGradientMain)"
                opacity="0.9"
              />
              <rect
                x="20"
                y="14"
                width="24"
                height="36"
                rx="1"
                ry="1"
                fill="url(#doorGradientMain)"
              />

              <!-- Door Handle -->
              <circle cx="38" cy="32" r="2" fill="#fbbf24" />
              <circle cx="38" cy="32" r="1.5" fill="#f59e0b" />

              <!-- Door Panel Lines -->
              <rect
                x="22"
                y="18"
                width="20"
                height="1"
                fill="rgba(79, 70, 229, 0.3)"
              />
              <rect
                x="22"
                y="22"
                width="20"
                height="1"
                fill="rgba(79, 70, 229, 0.3)"
              />
              <rect
                x="22"
                y="40"
                width="20"
                height="1"
                fill="rgba(79, 70, 229, 0.3)"
              />
              <rect
                x="22"
                y="44"
                width="20"
                height="1"
                fill="rgba(79, 70, 229, 0.3)"
              />

              <!-- Number "1" -->
              <g transform="translate(26, 26)">
                <path
                  d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18"
                  stroke="url(#numberGradientMain)"
                  stroke-width="3"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  fill="none"
                />
                <path
                  d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18"
                  stroke="#4f46e5"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  fill="none"
                />
              </g>
            </svg>
          </div>
          <div class="header-content">
            <h1 class="app-title">🚪 Satu Pintu</h1>
            <div class="app-version" id="appVersion">Versi 3.0</div>
          </div>

          <div class="user-section">
            <div class="user-info">
              <span id="userName"></span>
              <span id="userRole" class="role-badge"></span>
              <div class="subscription-info" id="subscriptionInfo"></div>
            </div>
            <button class="btn-logout" id="logoutBtn">Keluar</button>
          </div>
        </div>

        <div class="search-filter-wrapper">
          <div class="search-section">
            <div class="search-container">
              <input
                type="text"
                id="searchInput"
                placeholder="Cari aplikasi..."
                class="search-input"
              />
              <div class="search-icon">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </div>
            </div>
          </div>

          <div class="filter-section">
            <div class="filter-tabs" id="filterTabs">
              <button class="filter-tab active" data-category="all">
                Semua
              </button>
              <!-- Kategori akan dimuat secara dinamis -->
            </div>
          </div>
        </div>

        <!-- Sites Container (Scrollable) -->
        <div class="sites-section" id="sitesSection">
          <div class="sites-grid" id="sitesList"></div>
        </div>
      </div>

      <!-- Troubleshooting Section -->
      <div class="troubleshooting" id="troubleshooting" style="display: none">
        <h3>🔧 Troubleshooting</h3>
        <div class="troubleshooting-content">
          <p><strong>Jika ekstensi tidak berfungsi:</strong></p>
          <ol>
            <li>Klik ikon ekstensi di toolbar browser</li>
            <li>Klik "Reload" atau "Muat ulang"</li>
            <li>Atau buka chrome://extensions/ dan klik reload</li>
            <li>Coba login kembali</li>
          </ol>
          <button class="btn btn-small" onclick="location.reload()">
            Reload Popup
          </button>
        </div>
      </div>
    </div>

    <!-- Status notifications -->
    <div class="status" id="status"></div>

    <!-- Loading Container -->
    <div class="loading-container">
      <div id="fullPageLoader" class="full-page-loader">
        <div class="loader-spinner"></div>
        <div class="loader-text">Memuat data situs...</div>
        <div class="loader-subtext">Mohon tunggu sebentar</div>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="footer-section">
      <div class="footer-left">
        <button class="footer-btn" id="infoFaqBtn">
          <span>ℹ️ Informasi & FAQ</span>
        </button>
      </div>

      <div class="footer-center">
        <div class="scrolling-text-container">
          <div class="scrolling-text" id="scrollingText"></div>
        </div>
      </div>

      <div class="footer-right">
        <button class="footer-btn" id="helpBtn">
          <span>❓ Bantuan</span>
        </button>
      </div>
    </div>

    <script type="module" src="js/popup.js"></script>
  </body>
</html>
