<?php

namespace Database\Seeders;

use App\Models\Anyone;
use App\Models\ManualPayment;
use Illuminate\Database\Seeder;

class ManualPaymentSeeder extends Seeder
{
    public function run()
    {
        // Get some users to create payments for
        $users = Anyone::take(5)->get();

        foreach ($users as $user) {
            // Create pending payments
            ManualPayment::create([
                'user_id' => $user->id,
                'amount' => 50000,
                'payment_method' => 'manual_transfer',
                'status' => 'pending',
                'payment_proof' => 'payments/sample-proof.jpg',
                'notes' => 'Pembayaran untuk perpanjangan akun ' . $user->name,
                'whatsapp_message' => 'Halo admin, saya sudah transfer Rp 50.000 untuk perpanjangan akun',
                'whatsapp_number' => '6281234567890',
                'expired_at' => now()->addDays(7),
            ]);

            // Create approved payments
            ManualPayment::create([
                'user_id' => $user->id,
                'amount' => 100000,
                'payment_method' => 'ewallet',
                'status' => 'approved',
                'payment_proof' => 'payments/sample-proof2.jpg',
                'notes' => 'Pembayaran via e-wallet',
                'whatsapp_message' => 'Sudah transfer via e-wallet',
                'whatsapp_number' => '6281234567890',
                'approved_at' => now()->subDays(3),
                'expired_at' => now()->addDays(30),
            ]);

            // Create rejected payments
            ManualPayment::create([
                'user_id' => $user->id,
                'amount' => 75000,
                'payment_method' => 'cash',
                'status' => 'rejected',
                'payment_proof' => 'payments/sample-proof3.jpg',
                'notes' => 'Pembayaran tunai',
                'whatsapp_message' => 'Sudah bayar tunai',
                'whatsapp_number' => '6281234567890',
                'rejected_at' => now()->subDays(1),
                'admin_notes' => 'Bukti pembayaran tidak valid',
            ]);
        }
    }
}