<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ReportController extends Controller
{
    /**
     * Store a new report from extension
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'site_id' => 'required|integer',
                'site_name' => 'required|string|max:255',
                'report_type' => [
                    'required',
                    'string',
                    Rule::in(array_keys(Report::REPORT_TYPES))
                ],
                'custom_message' => 'nullable|string',
                'user_agent' => 'nullable|string',
                'timestamp' => 'required|date',
            ]);

            $report = Report::create([
                'site_id' => $validated['site_id'],
                'site_name' => $validated['site_name'],
                'report_type' => $validated['report_type'],
                'custom_message' => $validated['custom_message'] ?? null,
                'user_agent' => $validated['user_agent'],
                'reported_at' => Carbon::parse($validated['timestamp']),
                'status' => Report::STATUS_PENDING,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Laporan berhasil dikirim',
                'data' => [
                    'id' => $report->id,
                    'status' => $report->status,
                    'created_at' => $report->created_at,
                ]
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error creating report: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan laporan'
            ], 500);
        }
    }

    /**
     * Get reports statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Report::getStatistics();
            $reportsByType = Report::getReportsByType();

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => $stats,
                    'reports_by_type' => $reportsByType,
                    'report_types' => Report::REPORT_TYPES,
                    'statuses' => Report::STATUSES,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting report statistics: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil statistik'
            ], 500);
        }
    }

    /**
     * Get recent reports
     */
    public function recent(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $reports = Report::getRecentReports($limit);

            return response()->json([
                'success' => true,
                'data' => $reports->map(function ($report) {
                    return [
                        'id' => $report->id,
                        'site_id' => $report->site_id,
                        'site_name' => $report->site_name,
                        'report_type' => $report->report_type,
                        'report_type_name' => $report->report_type_name,
                        'status' => $report->status,
                        'status_name' => $report->status_name,
                        'reported_at' => $report->reported_at,
                        'created_at' => $report->created_at,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting recent reports: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil laporan terbaru'
            ], 500);
        }
    }

    /**
     * Get all reports with pagination
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Report::query();

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Filter by report type
            if ($request->has('report_type')) {
                $query->where('report_type', $request->report_type);
            }

            // Filter by site
            if ($request->has('site_id')) {
                $query->where('site_id', $request->site_id);
            }

            // Search by site name
            if ($request->has('search')) {
                $query->where('site_name', 'like', '%' . $request->search . '%');
            }

            $reports = $query->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $reports->items(),
                'pagination' => [
                    'current_page' => $reports->currentPage(),
                    'last_page' => $reports->lastPage(),
                    'per_page' => $reports->perPage(),
                    'total' => $reports->total(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting reports: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil laporan'
            ], 500);
        }
    }

    /**
     * Update report status
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => [
                    'required',
                    'string',
                    Rule::in(array_keys(Report::STATUSES))
                ],
                'admin_notes' => 'nullable|string',
                'resolved_by' => 'nullable|integer',
            ]);

            $report = Report::findOrFail($id);

            $updateData = [
                'status' => $validated['status'],
                'admin_notes' => $validated['admin_notes'] ?? null,
            ];

            if (in_array($validated['status'], [Report::STATUS_RESOLVED, Report::STATUS_DISMISSED])) {
                $updateData['resolved_by'] = $validated['resolved_by'] ?? null;
                $updateData['resolved_at'] = Carbon::now();
            }

            $report->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Status laporan berhasil diperbarui',
                'data' => $report->fresh()
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Laporan tidak ditemukan'
            ], 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error updating report status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui status laporan'
            ], 500);
        }
    }

    /**
     * Get detailed report with admin notes
     */
    public function show(Request $request, $id): JsonResponse
    {
        try {
            $report = Report::findOrFail($id);

            // Get related statistics
            $similarReports = Report::where('site_id', $report->site_id)
                ->where('id', '!=', $report->id)
                ->count();

            $sameTypeReports = Report::where('report_type', $report->report_type)
                ->where('id', '!=', $report->id)
                ->count();

            $recentReportsSameSite = Report::where('site_id', $report->site_id)
                ->where('id', '!=', $report->id)
                ->where('created_at', '>=', now()->subDays(7))
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $report->id,
                    'site_id' => $report->site_id,
                    'site_name' => $report->site_name,
                    'report_type' => $report->report_type,
                    'report_type_name' => Report::REPORT_TYPES[$report->report_type] ?? $report->report_type,
                    'custom_message' => $report->custom_message,
                    'user_agent' => $report->user_agent,
                    'reported_at' => $report->reported_at,
                    'status' => $report->status,
                    'status_name' => Report::STATUSES[$report->status] ?? $report->status,
                    'admin_notes' => $report->admin_notes,
                    'resolved_by' => $report->resolved_by,
                    'resolved_at' => $report->resolved_at,
                    'created_at' => $report->created_at,
                    'updated_at' => $report->updated_at,
                    'statistics' => [
                        'similar_reports_count' => $similarReports,
                        'same_type_reports_count' => $sameTypeReports,
                        'recent_reports_same_site' => $recentReportsSameSite,
                    ],
                    'admin_response' => [
                        'has_response' => !empty($report->admin_notes),
                        'response_text' => $report->admin_notes,
                        'response_date' => $report->resolved_at,
                        'is_resolved' => in_array($report->status, [Report::STATUS_RESOLVED, Report::STATUS_DISMISSED]),
                        'resolution_type' => $report->status === Report::STATUS_RESOLVED ? 'resolved' : ($report->status === Report::STATUS_DISMISSED ? 'dismissed' : null),
                    ]
                ]
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Laporan tidak ditemukan'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting report details: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil detail laporan'
            ], 500);
        }
    }

    /**
     * Get reports for a specific site with admin responses
     */
    public function getBySite(Request $request, $siteId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'nullable|string|in:' . implode(',', array_keys(Report::STATUSES)),
                'limit' => 'nullable|integer|min:1|max:50',
                'include_admin_notes' => 'nullable|boolean',
            ]);

            $query = Report::where('site_id', $siteId)
                ->orderBy('created_at', 'desc');

            if (isset($validated['status'])) {
                $query->where('status', $validated['status']);
            }

            $limit = $validated['limit'] ?? 10;
            $includeAdminNotes = $validated['include_admin_notes'] ?? false;

            $reports = $query->limit($limit)->get()->map(function ($report) use ($includeAdminNotes) {
                $data = [
                    'id' => $report->id,
                    'report_type' => $report->report_type,
                    'report_type_name' => Report::REPORT_TYPES[$report->report_type] ?? $report->report_type,
                    'custom_message' => $report->custom_message,
                    'reported_at' => $report->reported_at,
                    'status' => $report->status,
                    'status_name' => Report::STATUSES[$report->status] ?? $report->status,
                    'created_at' => $report->created_at,
                    'has_admin_response' => !empty($report->admin_notes),
                ];

                if ($includeAdminNotes && !empty($report->admin_notes)) {
                    $data['admin_notes'] = $report->admin_notes;
                    $data['resolved_by'] = $report->resolved_by;
                    $data['resolved_at'] = $report->resolved_at;
                }

                return $data;
            });

            return response()->json([
                'success' => true,
                'data' => $reports,
                'meta' => [
                    'site_id' => $siteId,
                    'total_count' => $reports->count(),
                    'has_admin_responses' => $reports->where('has_admin_response', true)->count(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting site reports: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil laporan situs'
            ], 500);
        }
    }
}
